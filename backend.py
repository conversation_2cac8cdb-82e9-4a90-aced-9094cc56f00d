from flask import request, jsonify, send_file, Response
import sqlite3
from functools import wraps
import io
import csv
import secrets
import os
import json
from datetime import datetime, timedelta
from config import ADMIN_PASSWORD, logger, get_sku_type, generate_activation_code, config, append_system_log
import smtplib
from email.mime.text import MIMEText
from email.header import Header
from email.utils import formataddr, make_msgid, formatdate
import re

# 文件上传配置
UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'doc', 'docx', 'xls', 'xlsx', 'zip', 'rar'}
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB

# 确保上传目录存在
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def register_backend_routes(app, db):
    """注册后台管理相关路由"""
    
    def authenticate_admin(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # 兼容GET请求无JSON体的情况，避免None.get报错
            json_data = request.get_json(silent=True) or {}
            password = request.headers.get('X-Admin-Password') or json_data.get('password')
            if not password or password != ADMIN_PASSWORD:
                logger.warn('后台管理身份验证失败。')
                return jsonify({'success': False, 'message': '未授权。请提供正确的管理员密码。'}), 401
            return f(*args, **kwargs)
        
        return decorated_function
    # 系统设置：读取
    @app.route('/admin/settings', methods=['GET'])
    @authenticate_admin
    def admin_get_settings():
        try:
            keys = [
                'admin_email','smtp_host','smtp_port','smtp_user','smtp_pass','smtp_tls','smtp_from','smtp_from_name',
                'site_title','support_contact','site_link','tickets_enabled','feature_blacklist_enabled',
                'feature_whitelist_enabled','register_email_verification_enabled','ticket_email_notify_enabled','admin_ticket_email_notify_enabled','admin_reply_every_email_enabled','pre_expiry_renewal_email_enabled','pre_expiry_renewal_days','pre_expiry_renewal_repeat_enabled',
                # 新增：邮件模板键
                'email_template_end','email_template_captcha','email_template_work_order','email_template_messages',
                # 新增：注册/绑定可用邮箱后缀
                'allowed_email_suffixes',
                # 新增：背景图API
                'background_image_api',
                # 新增：按次付费功能开关
                'pay_per_use_enabled',
                # 新增：机器人群聊自动刷新设置
                'robot_groups_auto_refresh_enabled','robot_groups_refresh_interval_minutes'
            ]
            settings = {k: (db.get_setting(k, '') or '') for k in keys}
            # 屏蔽敏感字段显示
            if settings.get('smtp_pass'):
                settings['smtp_pass_masked'] = '******'
            resp = jsonify({'success': True, 'data': settings})
            try:
                resp.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
                resp.headers['Pragma'] = 'no-cache'
                resp.headers['Expires'] = '0'
            except Exception:
                pass
            return resp
        except Exception as e:
            logger.error('读取系统设置失败:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    # 系统设置：保存
    @app.route('/admin/settings', methods=['POST'])
    @authenticate_admin
    def admin_save_settings():
        try:
            data = request.get_json() or {}
            allowed_keys = {
                'admin_email','smtp_host','smtp_port','smtp_user','smtp_tls','smtp_from','smtp_from_name',
                'site_title','support_contact','site_link','tickets_enabled','feature_blacklist_enabled',
                'feature_whitelist_enabled','register_email_verification_enabled','ticket_email_notify_enabled','admin_ticket_email_notify_enabled','admin_reply_every_email_enabled','pre_expiry_renewal_email_enabled','pre_expiry_renewal_days','pre_expiry_renewal_repeat_enabled',
                # 新增：邮件模板键
                'email_template_end','email_template_captcha','email_template_work_order','email_template_messages',
                # 新增：注册/绑定可用邮箱后缀
                'allowed_email_suffixes',
                # 新增：背景图API
                'background_image_api',
                # 新增：按次付费功能开关
                'pay_per_use_enabled',
                # 新增：机器人群聊自动刷新设置
                'robot_groups_auto_refresh_enabled','robot_groups_refresh_interval_minutes'
            }
            # 规范化布尔/端口
            kv_to_save = {}
            for k in allowed_keys:
                if k in data:
                    v = data.get(k)
                    if k in ('tickets_enabled','feature_blacklist_enabled','smtp_tls','register_email_verification_enabled','ticket_email_notify_enabled','admin_ticket_email_notify_enabled','admin_reply_every_email_enabled','pre_expiry_renewal_email_enabled','pre_expiry_renewal_repeat_enabled','feature_whitelist_enabled','pay_per_use_enabled','robot_groups_auto_refresh_enabled'):
                        v = '1' if str(v) in ('1','true','True','yes','on') else '0'
                    if k == 'pre_expiry_renewal_days':
                        try:
                            v = str(max(0, int(v)))
                        except:
                            v = '3'
                    if k == 'robot_groups_refresh_interval_minutes':
                        try:
                            v = str(max(5, int(v)))  # 最小5分钟
                        except:
                            v = '60'
                    if k == 'smtp_port':
                        try:
                            v = str(int(v))
                        except:
                            v = '587'
                    # 模板键允许空字符串；去除潜在路径首尾空白
                    if k.startswith('email_template_') and isinstance(v, str):
                        v = v.strip()
                    kv_to_save[k] = v

            # smtp_pass 单独处理：仅当显式传入时才改
            if 'smtp_pass' in data:
                kv_to_save['smtp_pass'] = data.get('smtp_pass') or ''

            # 打印将要保存的设置（掩码敏感字段，始终打印，便于排错）
            try:
                masked = {k: ('******' if k == 'smtp_pass' and kv_to_save.get(k) else kv_to_save.get(k)) for k in kv_to_save}
                logger.info(f"保存系统设置: {masked}")
            except Exception:
                pass
            if not db.set_settings(kv_to_save):
                return jsonify({'success': False, 'message': '保存失败'}), 500
            # 记录系统设置变更日志
            changed_keys = list(kv_to_save.keys())
            append_system_log('admin', '系统设置/保存设置', f'修改了{len(changed_keys)}项设置: {", ".join(changed_keys)}')
            # 返回保存后的关键字段，便于前端立即回填验证
            saved_snapshot = {
                'site_title': kv_to_save.get('site_title', db.get_setting('site_title', '')),
                'support_contact': kv_to_save.get('support_contact', db.get_setting('support_contact', '')),
                'site_link': kv_to_save.get('site_link', db.get_setting('site_link', '')),
                'smtp_from_name': db.get_setting('smtp_from_name', ''),
                'smtp_from': db.get_setting('smtp_from', ''),
                'smtp_host': db.get_setting('smtp_host', ''),
                'smtp_user': db.get_setting('smtp_user', ''),
                'smtp_port': db.get_setting('smtp_port', '587'),
                'smtp_tls': db.get_setting('smtp_tls', '1'),
                'admin_email': db.get_setting('admin_email', ''),
                'tickets_enabled': db.get_setting('tickets_enabled', '1'),
                'feature_blacklist_enabled': db.get_setting('feature_blacklist_enabled', '1'),
                'feature_whitelist_enabled': db.get_setting('feature_whitelist_enabled', '1'),
                'register_email_verification_enabled': db.get_setting('register_email_verification_enabled', '0'),
                'ticket_email_notify_enabled': db.get_setting('ticket_email_notify_enabled', '0'),
                'admin_ticket_email_notify_enabled': db.get_setting('admin_ticket_email_notify_enabled', '0'),
                'admin_reply_every_email_enabled': db.get_setting('admin_reply_every_email_enabled', '0'),
                'pre_expiry_renewal_email_enabled': db.get_setting('pre_expiry_renewal_email_enabled', '0'),
                'pre_expiry_renewal_days': db.get_setting('pre_expiry_renewal_days', '3'),
                'pre_expiry_renewal_repeat_enabled': db.get_setting('pre_expiry_renewal_repeat_enabled', '0'),
                'allowed_email_suffixes': db.get_setting('allowed_email_suffixes', ''),
                # 回传模板键，便于前端回填
                'email_template_end': db.get_setting('email_template_end', ''),
                'email_template_captcha': db.get_setting('email_template_captcha', ''),
                'email_template_work_order': db.get_setting('email_template_work_order', ''),
                'email_template_messages': db.get_setting('email_template_messages', ''),
                # 机器人群聊自动刷新设置
                'robot_groups_auto_refresh_enabled': db.get_setting('robot_groups_auto_refresh_enabled', '1'),
                'robot_groups_refresh_interval_minutes': db.get_setting('robot_groups_refresh_interval_minutes', '60')
            }
            resp = jsonify({'success': True, 'message': '设置已保存', 'data': saved_snapshot})
            try:
                resp.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
                resp.headers['Pragma'] = 'no-cache'
                resp.headers['Expires'] = '0'
            except Exception:
                pass
            return resp
        except Exception as e:
            logger.error('保存系统设置失败:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    # 新增：列出模板目录文件
    @app.route('/admin/settings/email-templates', methods=['GET'])
    @authenticate_admin
    def admin_list_email_templates():
        try:
            base_dirs = {
                'end': os.path.join('public', 'email_end'),
                'captcha': os.path.join('public', 'email_captcha'),
                'work_order': os.path.join('public', 'email_work_order'),
                'messages': os.path.join('public', 'email_messages')
            }
            result = {}
            for key, dir_path in base_dirs.items():
                files = []
                try:
                    if os.path.isdir(dir_path):
                        for fname in os.listdir(dir_path):
                            if fname.lower().endswith('.html'):
                                files.append(fname)
                except Exception as e:
                    logger.warn(f'读取模板目录失败: {dir_path}', e)
                result[key] = sorted(files)
            return jsonify({'success': True, 'data': result})
        except Exception as e:
            logger.error('列出模板目录失败:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    # 渲染普通消息模板
    def render_messages_email_html(subject: str, content: str) -> str:
        try:
            template_filename = (db.get_setting('email_template_messages', '') or '').strip()
            content_html = (content or '').replace('\n', '<br>')
            # 可替换变量
            site_name = db.get_setting('site_title', '') or ''
            site_link = db.get_setting('site_link', '') or ''
            support_contact = db.get_setting('support_contact', '') or ''
            if template_filename:
                path = os.path.join('public', 'email_messages', template_filename)
                if os.path.isfile(path):
                    with open(path, 'r', encoding='utf-8') as f:
                        html = f.read()
                    # 优先替换占位符
                    if '{{subject}}' in html:
                        html = html.replace('{{subject}}', subject or '')
                    else:
                        # 替换 <title>
                        try:
                            html = re.sub(r'(<title>)(.*?)(</title>)', rf"\\1{re.escape(subject or '')}\\3", html, count=1, flags=re.I)
                        except Exception:
                            pass
                    # 站点信息与联系方式
                    html = html.replace('{{site_name}}', site_name)
                    html = html.replace('{{site_link}}', site_link)
                    html = html.replace('{{contact}}', support_contact)
                    # 正文
                    if '{{content}}' in html:
                        html = html.replace('{{content}}', content_html)
                    else:
                        # 在 </body> 前插入内容块
                        insertion = f"<div style=\"padding:20px;\">{content_html}</div>"
                        if re.search(r'</body>', html, flags=re.I):
                            html = re.sub(r'</body>', insertion + '</body>', html, count=1, flags=re.I)
                        else:
                            html = html + insertion
                    return html
        except Exception as e:
            logger.warn('渲染普通消息模板失败，回退到默认模板', e)
        # 回退：简单的默认HTML包装
        safe_subject = subject or ''
        site_name = db.get_setting('site_title', '') or ''
        site_link = db.get_setting('site_link', '') or ''
        support_contact = db.get_setting('support_contact', '') or ''
        return (
            f"<!doctype html><html><head><meta charset='utf-8'><title>{safe_subject}</title></head>"
            f"<body><div style='max-width:600px;margin:20px auto;font-family:Arial,Helvetica,\"Microsoft YaHei\",sans-serif;'>"
            f"<h2 style='margin:0 0 10px 0;'>{safe_subject}</h2>"
            f"<div style='margin-top:12px;line-height:1.6'>{(content or '').replace('\n','<br>')}</div>"
            f"<div style='margin-top:24px;color:#888;font-size:12px;border-top:1px solid #eee;padding-top:10px;'>"
            f"客服联系方式：{support_contact or '-'}<br>"
            f"网站：<a href='{site_link}'>{(site_name or site_link) or '-'}</a>"
            f"</div>"
            f"</div></body></html>"
        )

    # 渲染到期提醒模板（public/email_end）
    def render_end_email_html(subject: str, content: str, *, sku_name: str = '', group_number: str = '', expiration_date: str = '', days_left: int = 0) -> str:
        try:
            template_filename = (db.get_setting('email_template_end', '') or '').strip()
            site_name = db.get_setting('site_title', '') or ''
            site_link = db.get_setting('site_link', '') or ''
            support_contact = db.get_setting('support_contact', '') or ''
            content_html = (content or '').replace('\n', '<br>')
            if template_filename:
                path = os.path.join('public', 'email_end', template_filename)
                if os.path.isfile(path):
                    with open(path, 'r', encoding='utf-8') as f:
                        html = f.read()
                    # 标题
                    if '{{subject}}' in html:
                        html = html.replace('{{subject}}', subject or '')
                    else:
                        try:
                            html = re.sub(r'(<title>)(.*?)(</title>)', rf"\\1{re.escape(subject or '')}\\3", html, count=1, flags=re.I)
                        except Exception:
                            pass
                    # 站点信息
                    html = html.replace('{{site_name}}', site_name)
                    html = html.replace('{{site_link}}', site_link)
                    html = html.replace('{{contact}}', support_contact)
                    # 业务信息
                    html = html.replace('{{content}}', content_html)
                    html = html.replace('{{sku_name}}', sku_name or '')
                    html = html.replace('{{group_number}}', group_number or '')
                    html = html.replace('{{expiration_date}}', expiration_date or '')
                    html = html.replace('{{days_left}}', str(days_left) if days_left is not None else '')
                    return html
        except Exception as e:
            logger.warn('渲染到期提醒模板失败，回退到默认模板', e)
        # 回退模板
        safe_subject = subject or '到期提醒'
        return (
            f"<!doctype html><html><head><meta charset='utf-8'><title>{safe_subject}</title></head>"
            f"<body><div style='max-width:600px;margin:20px auto;font-family:Arial,Helvetica,\"Microsoft YaHei\",sans-serif;'>"
            f"<h2 style='margin:0 0 10px 0;'>{safe_subject}</h2>"
            f"<div style='margin-top:12px;line-height:1.6'>{(content or '').replace('\n','<br>')}</div>"
            f"<div style='margin-top:12px;color:#444'>套餐：{sku_name or '-'}；群号：{group_number or '-'}；到期：{expiration_date or '-'}；剩余：{days_left}天</div>"
            f"</div></body></html>"
        )

    # 渲染注册邮箱验证码模板
    def render_captcha_email_html(subject: str, content: str, code: str, ttl_minutes: int, request_ip: str = '', request_time: str = '') -> str:
        try:
            template_filename = (db.get_setting('email_template_captcha', '') or '').strip()
            site_name = db.get_setting('site_title', '') or ''
            site_link = db.get_setting('site_link', '') or ''
            support_contact = db.get_setting('support_contact', '') or ''
            if template_filename:
                path = os.path.join('public', 'email_captcha', template_filename)
                if os.path.isfile(path):
                    with open(path, 'r', encoding='utf-8') as f:
                        html = f.read()
                    # 标题
                    if '{{subject}}' in html:
                        html = html.replace('{{subject}}', subject or '')
                    else:
                        try:
                            html = re.sub(r'(<title>)(.*?)(</title>)', rf"\\1{re.escape(subject or '')}\\3", html, count=1, flags=re.I)
                        except Exception:
                            pass
                    # 站点信息
                    html = html.replace('{{site_name}}', site_name)
                    html = html.replace('{{site_link}}', site_link)
                    html = html.replace('{{contact}}', support_contact)
                    # 内容与验证码
                    html = html.replace('{{content}}', (content or '').replace('\n', '<br>'))
                    html = html.replace('{{code}}', code)
                    html = html.replace('{{ttl_minutes}}', str(ttl_minutes))
                    html = html.replace('{{request_ip}}', request_ip or '-')
                    html = html.replace('{{request_time}}', request_time or '-')
                    return html
        except Exception as e:
            logger.warn('渲染验证码模板失败，回退到默认模板', e)
        # 回退模板
        safe_subject = subject or '邮箱验证码'
        return (
            f"<!doctype html><html><head><meta charset='utf-8'><title>{safe_subject}</title></head>"
            f"<body><div style='max-width:600px;margin:20px auto;font-family:Arial,Helvetica,\"Microsoft YaHei\",sans-serif;'>"
            f"<h2 style='margin:0 0 10px 0;'>{safe_subject}</h2>"
            f"<div style='margin-top:12px;line-height:1.6'>{(content or '').replace('\n','<br>')}</div>"
            f"<div style='margin-top:16px;font-size:28px;font-weight:bold;letter-spacing:6px;'>{code}</div>"
            f"<div style='margin-top:8px;color:#888;font-size:12px;'>有效期：{ttl_minutes}分钟</div>"
            f"</div></body></html>"
        )

    # 渲染工单回复提醒模板（public/email_work_order）
    def render_work_order_email_html(subject: str, content: str, *, ticket_number: str = '', created_time: str = '', view_link: str = '', site_name_override: str = '') -> str:
        try:
            template_filename = (db.get_setting('email_template_work_order', '') or '').strip()
            site_name = site_name_override or (db.get_setting('site_title', '') or '')
            site_link = db.get_setting('site_link', '') or ''
            support_contact = db.get_setting('support_contact', '') or ''
            content_html = (content or '').replace('\n', '<br>')
            if template_filename:
                path = os.path.join('public', 'email_work_order', template_filename)
                if os.path.isfile(path):
                    with open(path, 'r', encoding='utf-8') as f:
                        html = f.read()
                    # 标题
                    if '{{subject}}' in html:
                        html = html.replace('{{subject}}', subject or '')
                    else:
                        try:
                            html = re.sub(r'(<title>)(.*?)(</title>)', rf"\\1{re.escape(subject or '')}\\3", html, count=1, flags=re.I)
                        except Exception:
                            pass
                    # 站点信息
                    html = html.replace('{{site_name}}', site_name)
                    html = html.replace('{{site_link}}', site_link)
                    html = html.replace('{{contact}}', support_contact)
                    # 业务信息
                    html = html.replace('{{content}}', content_html)
                    html = html.replace('{{ticket_number}}', ticket_number or '')
                    html = html.replace('{{created_time}}', created_time or '')
                    html = html.replace('{{view_link}}', view_link or (site_link or '#'))
                    return html
        except Exception as e:
            logger.warn('渲染工单回复模板失败，回退到默认模板', e)
        # 回退模板
        safe_subject = subject or '工单提醒'
        return (
            f"<!doctype html><html><head><meta charset='utf-8'><title>{safe_subject}</title></head>"
            f"<body><div style='max-width:600px;margin:20px auto;font-family:Arial,Helvetica,\"Microsoft YaHei\",sans-serif;'>"
            f"<h2 style='margin:0 0 10px 0;'>{safe_subject}</h2>"
            f"<div style='margin-top:12px;line-height:1.6'>{(content or '').replace('\n','<br>')}</div>"
            f"<div style='margin-top:12px;color:#444'>工单号：{ticket_number or '-'}；创建时间：{created_time or '-'}</div>"
            f"</div></body></html>"
        )

    # 发送邮件（支持HTML）
    def send_email_smtp(to_email: str, subject: str, html_body: str):
        host = db.get_setting('smtp_host', '')
        port = int(db.get_setting('smtp_port', '587') or '587')
        user = db.get_setting('smtp_user', '')
        pwd = db.get_setting('smtp_pass', '')
        use_tls = str(db.get_setting('smtp_tls', '1')) in ('1','true','True','yes','on')
        from_addr = db.get_setting('smtp_from', '') or user
        from_name = db.get_setting('smtp_from_name', '') or db.get_setting('site_title', '')

        if not host or not user or not pwd or not from_addr:
            raise Exception('SMTP配置不完整')

        msg = MIMEText(html_body or '', 'html', 'utf-8')
        msg['Subject'] = str(Header(subject or '', 'utf-8'))
        msg['From'] = formataddr((str(Header(from_name, 'utf-8')), from_addr)) if from_name else from_addr
        msg['To'] = to_email
        msg['Date'] = formatdate(localtime=True)
        msg['Message-ID'] = make_msgid()

        attempts = []
        if use_tls:
            attempts = ['starttls', 'ssl']
        else:
            attempts = ['ssl' if port == 465 else 'smtp', 'starttls', 'ssl']

        last_error = None
        for method in attempts:
            server = None
            try:
                if method == 'starttls':
                    server = smtplib.SMTP(host, port, timeout=10)
                    server.ehlo()
                    server.starttls()
                    server.ehlo()
                elif method == 'ssl':
                    server = smtplib.SMTP_SSL(host, port, timeout=10)
                    server.ehlo()
                else:
                    server = smtplib.SMTP(host, port, timeout=10)
                    server.ehlo()
                server.login(user, pwd)
                server.sendmail(from_addr, [to_email], msg.as_string())
                server.quit()
                return
            except Exception as e2:
                last_error = e2
                try:
                    if server:
                        server.quit()
                except Exception:
                    pass
        raise last_error if last_error else Exception('未知SMTP错误')

    # 系统设置：测试邮件（异步队列）
    @app.route('/admin/settings/test-email', methods=['POST'])
    @authenticate_admin
    def admin_test_email():
        try:
            data = request.get_json() or {}
            to_email = data.get('to') or db.get_setting('admin_email', '')
            if not to_email:
                return jsonify({'success': False, 'message': '请先设置管理员邮箱或传入收件邮箱'}), 400
            # 通过普通消息模板渲染并发送
            subject = '测试邮件'
            html = render_messages_email_html(subject, '这是一封测试邮件：系统设置中的SMTP配置已生效。')
            try:
                app.enqueue_email(to_email, subject, html, {'actor': 'admin', 'action': '发送测试邮件'})
            except Exception:
                # 兼容旧环境无队列时同步发送
                send_email_smtp(to_email, subject, html)
            append_system_log('admin', '系统设置/发送测试邮件', '已入队')
            return jsonify({'success': True, 'message': '测试邮件已入队'})
        except Exception as e:
            logger.error('发送测试邮件失败:', e)
            return jsonify({'success': False, 'message': f'发送失败: {str(e)}'}), 500



    # 后台：针对某条绑定，手动发送到期提醒邮件（异步队列）
    @app.route('/admin/bindings/send-expiry-reminder', methods=['POST'])
    @authenticate_admin
    def admin_send_expiry_reminder_for_binding():
        try:
            data = request.get_json() or {}
            group_number = (data.get('groupNumber') or '').strip()
            sku_id = (data.get('skuId') or '').strip()
            owner_username = (data.get('owner') or '').strip()
            if not group_number or not sku_id or not owner_username:
                return jsonify({'success': False, 'message': '参数不完整'}), 400

            cursor = db.conn.cursor()
            # 取该群该套餐最新到期时间
            cursor.execute('''
                SELECT MAX(expiration_time) AS expiration_time
                FROM bindings
                WHERE group_number = ? AND sku_id = ? AND owner_username = ?
            ''', (group_number, sku_id, owner_username))
            row = cursor.fetchone()
            expiration_time = row['expiration_time'] if row else None
            if not expiration_time:
                return jsonify({'success': False, 'message': '未找到到期时间或为永久有效'}), 400
            # 获取用户邮箱
            cursor.execute('SELECT email FROM users WHERE username = ? LIMIT 1', (owner_username,))
            user_row = cursor.fetchone()
            if not user_row or not user_row['email']:
                return jsonify({'success': False, 'message': '该用户未绑定邮箱'}), 400

            # 计算剩余天数
            try:
                # 处理带时区信息的日期格式
                exp_str = expiration_time
                if exp_str.endswith('Z'):
                    exp_str = exp_str[:-1]
                elif exp_str.endswith('.000Z'):
                    exp_str = exp_str[:-5]
                expiry_dt = datetime.fromisoformat(exp_str)
            except Exception:
                try:
                    expiry_dt = datetime.strptime(expiration_time.split('.')[0], '%Y-%m-%dT%H:%M:%S') if 'T' in expiration_time else datetime.fromisoformat(expiration_time)
                except Exception:
                    expiry_dt = datetime.now()
            days_left = max(0, (expiry_dt.date() - datetime.now().date()).days)
            sku_name = config.get_tier_name(sku_id)
            site_name = db.get_setting('site_title', '') or ''
            subject = f"{site_name} 业务即将到期提醒" if site_name else '业务即将到期提醒'
            content = (
                f"您的订阅的：{sku_name}\n"
                f"将于：{expiry_dt.strftime('%Y-%m-%d %H:%M')} 到期\n"
                f"为了不影响功能的使用，请您及时续费。"
            )
            html = render_end_email_html(subject, content, sku_name=sku_name, group_number=group_number, expiration_date=expiry_dt.strftime('%Y-%m-%d %H:%M'), days_left=days_left)
            try:
                app.enqueue_email(user_row['email'], subject, html, {'actor': 'admin', 'action': f'手动发送到期提醒 群{group_number} 用户{owner_username}'})
            except Exception:
                send_email_smtp(user_row['email'], subject, html)
            append_system_log('admin', '绑定管理/手动发送到期提醒', f'群{group_number} 用户{owner_username} 已入队')
            return jsonify({'success': True, 'message': '到期提醒已入队'})
        except Exception as e:
            logger.error('发送到期提醒失败:', e)
            return jsonify({'success': False, 'message': f'发送失败: {str(e)}'}), 500





    # 管理员给指定用户发送邮件（普通消息模板，异步队列）
    @app.route('/admin/users/send-email', methods=['POST'])
    @authenticate_admin
    def admin_send_email_to_user():
        try:
            data = request.get_json() or {}
            user_id = data.get('userId')
            subject = (data.get('subject') or '').strip()
            content = (data.get('content') or '').strip()

            if not user_id:
                return jsonify({'success': False, 'message': '用户ID不能为空'}), 400
            if not subject:
                return jsonify({'success': False, 'message': '主题不能为空'}), 400
            if not content:
                return jsonify({'success': False, 'message': '内容不能为空'}), 400

            cursor = db.conn.cursor()
            cursor.execute('SELECT email, username FROM users WHERE id = ?', (user_id,))
            row = cursor.fetchone()
            if not row or not row['email']:
                return jsonify({'success': False, 'message': '用户不存在或未绑定邮箱'}), 404

            html = render_messages_email_html(subject, content)
            try:
                app.enqueue_email(row['email'], subject, html, {'actor': 'admin', 'action': f'发送站内消息邮件 用户{row["username"]}'})
            except Exception:
                send_email_smtp(row['email'], subject, html)
            append_system_log('admin', '用户管理/发送邮件', f'用户{row["username"]} 已入队')
            return jsonify({'success': True, 'message': '邮件已入队'})
        except Exception as e:
            logger.error('给用户发送邮件失败:', e)
            return jsonify({'success': False, 'message': f'发送失败: {str(e)}'}), 500

    # 系统日志：列出最近N天（读取 /log 目录按天文件）
    @app.route('/admin/system-logs', methods=['GET'])
    @authenticate_admin
    def admin_get_system_logs():
        try:
            days = int(request.args.get('days', 7))
            if days <= 0:
                days = 1
            from datetime import datetime, timedelta
            logs = []
            for i in range(days):
                d = (datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d')
                path = os.path.join('log', f'{d}.log')
                if os.path.isfile(path):
                    try:
                        with open(path, 'r', encoding='utf-8') as f:
                            content = f.read()
                        logs.append({'date': d, 'content': content})
                    except Exception as e:
                        logs.append({'date': d, 'content': f'读取失败: {str(e)}'})
            return jsonify({'success': True, 'data': logs})
        except Exception as e:
            logger.error('读取系统日志失败:', e)
            return jsonify({'success': False, 'message': str(e)}), 500
    
    @app.route('/admin/login', methods=['POST'])
    def admin_login():
        """管理员登录验证"""
        data = request.json
        password = data.get('password', '')
        
        if not password:
            return jsonify({'success': False, 'message': '请输入管理员密码'}), 400
        
        if password != ADMIN_PASSWORD:
            logger.warn('后台管理登录失败：密码错误')
            return jsonify({'success': False, 'message': '管理员密码错误'}), 401
        
        logger.info('管理员登录成功')
        append_system_log('admin', '系统管理/登录', '管理员登录成功')
        return jsonify({
            'success': True,
            'message': '登录成功'
        })
    
    @app.route('/admin/bindings', methods=['POST'])
    @authenticate_admin
    def admin_get_bindings():
        """获取所有绑定信息（需要管理员权限）- 使用时间池逻辑"""
        try:
            logger.info('正在为后台管理面板从数据库获取所有绑定信息。')

            from config import get_group_all_time_pools
            cursor = db.conn.cursor()

            # 获取所有群组和SKU的组合
            cursor.execute('''
                SELECT DISTINCT group_number, sku_id
                FROM bindings
                ORDER BY group_number, sku_id
            ''')

            group_sku_pairs = cursor.fetchall()

            bindings_list = []

            # 使用时间池逻辑处理每个群组+SKU组合
            for pair in group_sku_pairs:
                group_number = pair['group_number']
                sku_id = pair['sku_id']

                # 获取该群组该SKU的时间池信息
                time_pools = get_group_all_time_pools(db.conn, group_number)

                if sku_id not in time_pools:
                    continue  # 该SKU已完全过期，跳过

                pool_info = time_pools[sku_id]

                # 获取所有贡献者的详细信息
                cursor.execute('''
                    SELECT DISTINCT b.owner_username, u.qq as owner_qq
                    FROM bindings b
                    LEFT JOIN users u ON b.owner_username = u.username
                    WHERE b.group_number = ? AND b.sku_id = ?
                ''', (group_number, sku_id))

                contributors = cursor.fetchall()

                # 获取所有激活码
                cursor.execute('''
                    SELECT activation_code, bind_time
                    FROM bindings
                    WHERE group_number = ? AND sku_id = ?
                    ORDER BY bind_time ASC
                ''', (group_number, sku_id))

                all_codes_data = cursor.fetchall()
                all_codes = [code['activation_code'] for code in all_codes_data]
                first_bind_time = all_codes_data[0]['bind_time'] if all_codes_data else None

                # 计算剩余天数（使用时间池逻辑）
                ttl = None
                expiration_iso_string = None

                if pool_info['total_remaining_seconds'] == float('inf'):
                    ttl = -1  # 永久有效
                elif pool_info['total_remaining_seconds'] > 0:
                    ttl = pool_info['total_remaining_days']
                    expiration_iso_string = pool_info['final_expiration']
                else:
                    ttl = -2  # 已过期

                # 格式化到期时间显示
                if pool_info['final_expiration']:
                    try:
                        expiry_time = datetime.fromisoformat(pool_info['final_expiration'])
                        expiration_display = expiry_time.strftime('%Y-%m-%d %H:%M')
                    except Exception:
                        expiration_display = pool_info['final_expiration']
                else:
                    expiration_display = '永久'

                # 处理 Infinity 值，转换为 JavaScript 可以处理的值
                total_remaining_days = pool_info['total_remaining_days']
                total_remaining_seconds = pool_info['total_remaining_seconds']

                if total_remaining_days == float('inf'):
                    total_remaining_days = -1  # 用 -1 表示永久
                if total_remaining_seconds == float('inf'):
                    total_remaining_seconds = -1  # 用 -1 表示永久

                # 创建一个群组+SKU的条目，包含所有激活码的详细信息
                bindings_list.append({
                    'id': f"{group_number}_{sku_id}",  # 生成唯一ID
                    'groupNumber': group_number,
                    'skuType': config.get_tier_name(sku_id),
                    'skuId': sku_id,
                    'expirationDate': expiration_display,
                    'expirationISOString': expiration_iso_string,
                    'bindTime': first_bind_time,
                    'ttl': ttl,
                    # 时间池相关信息
                    'totalRemainingDays': total_remaining_days,
                    'totalRemainingSeconds': total_remaining_seconds,
                    'activeCodesCount': len(pool_info['active_codes']),
                    'expiredCodesCount': len(pool_info['expired_codes']),
                    'isTimePool': True,
                    # 详细的激活码信息
                    'activeCodes': [
                        {
                            'activation_code': code['activation_code'],
                            'owner_username': code['owner_username'],
                            'bind_time': code['bind_time'],
                            'expiration_time': code['expiration_time'],
                            'remaining_seconds': code['remaining_seconds'] if code['remaining_seconds'] != float('inf') else -1,
                            'remaining_days': int(code['remaining_seconds'] / 86400) if code['remaining_seconds'] != float('inf') else -1,
                            'is_permanent': code.get('is_permanent', False),
                            'status': code.get('status', 'unknown'),  # consuming, waiting, expired
                            'original_duration_seconds': code.get('original_duration_seconds', 0)
                        } for code in pool_info['active_codes']
                    ],
                    'expiredCodes': [
                        {
                            'activation_code': code['activation_code'],
                            'owner_username': code['owner_username'],
                            'bind_time': code['bind_time'],
                            'expiration_time': code['expiration_time'],
                            'expired_seconds': code['expired_seconds']
                        } for code in pool_info['expired_codes']
                    ],
                    # 所有贡献者信息
                    'contributors': [
                        {
                            'username': contributor['owner_username'],
                            'qq': contributor['owner_qq'] or '未绑定'
                        } for contributor in contributors
                    ]
                })
            
            logger.info(f'为后台管理面板准备了 {len(bindings_list)} 条绑定条目。')
            return jsonify({'success': True, 'data': bindings_list})
        except Exception as e:
            logger.error('为后台管理面板获取绑定信息时出错:', e)
            return jsonify({'success': False, 'message': f'获取绑定列表失败: {str(e)}'}), 500
    
    @app.route('/admin/codes/create', methods=['POST'])
    @authenticate_admin
    def admin_create_codes():
        data = request.json
        sku_id = data.get('skuId', '').strip()
        duration_months = int(data.get('durationMonths', 1))
        quantity = int(data.get('quantity', 1))
        batch_note = data.get('note', '').strip()
        
        # 验证SKU ID是否有效
        if sku_id not in config.TIER_MAP:
            return jsonify({'success': False, 'message': '无效的档位'}), 400
        
        if quantity < 1 or quantity > 1000:
            return jsonify({'success': False, 'message': '数量必须在1-1000之间'}), 400
        
        try:
            batch_id = datetime.now().strftime('%Y%m%d%H%M%S') + secrets.token_hex(4)
            created_codes = []
            
            cursor = db.conn.cursor()
            
            for _ in range(quantity):
                while True:
                    code = generate_activation_code()
                    cursor.execute('SELECT COUNT(*) as count FROM activation_codes WHERE code = ?', (code,))
                    if cursor.fetchone()['count'] == 0:
                        break
                
                cursor.execute(
                    '''INSERT INTO activation_codes 
                       (code, sku_id, duration_months, batch_id, note) 
                       VALUES (?, ?, ?, ?, ?)''',
                    (code, sku_id, duration_months, batch_id, batch_note)
                )
                created_codes.append(code)
            
            db.conn.commit()
            
            logger.info(f'批量创建了 {quantity} 个激活码，批次ID: {batch_id}')
            append_system_log('admin', '兑换码管理/批量创建', f'{quantity}个激活码 档位{config.get_tier_name(sku_id)} 时长{duration_months}月 批次{batch_id}')
            
            return jsonify({
                'success': True,
                'message': f'成功创建 {quantity} 个激活码',
                'data': {
                    'batchId': batch_id,
                    'codes': created_codes,
                    'skuType': config.get_tier_name(sku_id),
                    'durationMonths': duration_months
                }
            })
        except Exception as e:
            logger.error('创建激活码时出错:', e)
            return jsonify({'success': False, 'message': f'创建失败: {str(e)}'}), 500
    
    @app.route('/admin/codes', methods=['GET'])
    @authenticate_admin
    def admin_get_codes():
        try:
            page = int(request.args.get('page', 1))
            per_page = int(request.args.get('perPage', 20))
            status_filter = request.args.get('used', '')
            search_query = request.args.get('search', '')
            
            offset = (page - 1) * per_page
            
            cursor = db.conn.cursor()
            
            where_conditions = []
            params = []
            
            if status_filter:
                where_conditions.append("is_used = ?")
                params.append(int(status_filter))
            
            if search_query:
                search_conditions = []
                search_param = f'%{search_query}%'
                
                search_conditions.append("used_by LIKE ?")
                params.append(search_param)
                
                search_conditions.append("note LIKE ?")
                params.append(search_param)
                
                search_conditions.append("strftime('%Y-%m-%d %H:%M', created_time) LIKE ?")
                params.append(search_param)
                
                where_conditions.append(f"({' OR '.join(search_conditions)})")
            
            where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""
            
            count_query = f"SELECT COUNT(*) as total FROM activation_codes{where_clause}"
            cursor.execute(count_query, params)
            total_count = cursor.fetchone()['total']
            
            data_query = f"""
                SELECT 
                    id,
                    code,
                    sku_id,
                    duration_months,
                    is_used,
                    used_by,
                    note,
                    created_time,
                    used_time
                FROM activation_codes
                {where_clause}
                ORDER BY created_time DESC
                LIMIT ? OFFSET ?
            """
            
            cursor.execute(data_query, params + [per_page, offset])
            codes = cursor.fetchall()
            
            codes_list = []
            for code in codes:
                sku_type = config.get_tier_name(code['sku_id'])
                codes_list.append({
                    'id': code['id'],
                    'code': code['code'],
                    'skuId': code['sku_id'],
                    'skuType': sku_type,
                    'durationMonths': code['duration_months'],
                    'isUsed': bool(code['is_used']),
                    'usedBy': code['used_by'],
                    'note': code['note'] or '',
                    'createdTime': code['created_time'],
                    'usedTime': code['used_time']
                })
            
            total_pages = (total_count + per_page - 1) // per_page
            
            return jsonify({
                'success': True,
                'data': {
                    'codes': codes_list,
                    'pagination': {
                        'total': total_count,
                        'page': page,
                        'perPage': per_page,
                        'totalPages': total_pages
                    }
                }
            })
            
        except Exception as e:
            logger.error(f'获取兑换码列表时出错: {e}')
            return jsonify({'success': False, 'message': str(e)}), 500
    
    @app.route('/admin/codes/export', methods=['POST'])
    @authenticate_admin
    def admin_export_codes():
        data = request.json
        batch_id = data.get('batchId')
        export_format = data.get('format', 'csv')
        
        try:
            query = 'SELECT * FROM activation_codes'
            params = []
            
            if batch_id:
                query += ' WHERE batch_id = ?'
                params.append(batch_id)
            
            query += ' ORDER BY created_time DESC'
            
            cursor = db.conn.cursor()
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            if export_format == 'csv':
                csv_content = 'Code,SKU Type,Duration (Months),Used,Used By,Used Time,Created Time,Batch ID,Note\n'
                for row in rows:
                    csv_content += f"{row['code']},{config.get_tier_name(row['sku_id'])},{row['duration_months']},{row['is_used']},{row['used_by'] or ''},{row['used_time'] or ''},{row['created_time']},{row['batch_id']},{row['note'] or ''}\n"
                
                # 记录导出日志
                if batch_id:
                    append_system_log('admin', '兑换码管理/导出兑换码', f'导出批次{batch_id} 共{len(rows)}个兑换码 CSV格式')
                else:
                    append_system_log('admin', '兑换码管理/导出兑换码', f'导出全部共{len(rows)}个兑换码 CSV格式')
                
                response = Response(csv_content, mimetype='text/csv')
                response.headers['Content-Disposition'] = f'attachment; filename=activation_codes_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
                return response
            else:
                codes_list = []
                for row in rows:
                    codes_list.append({
                        'code': row['code'],
                        'skuType': config.get_tier_name(row['sku_id']),
                        'durationMonths': row['duration_months'],
                        'isUsed': bool(row['is_used']),
                        'usedBy': row['used_by'],
                        'usedTime': row['used_time'],
                        'createdTime': row['created_time'],
                        'batchId': row['batch_id'],
                        'note': row['note']
                    })
                
                # 记录导出日志
                if batch_id:
                    append_system_log('admin', '兑换码管理/导出兑换码', f'导出批次{batch_id} 共{len(rows)}个兑换码 JSON格式')
                else:
                    append_system_log('admin', '兑换码管理/导出兑换码', f'导出全部共{len(rows)}个兑换码 JSON格式')
                
                return jsonify({
                    'success': True,
                    'data': codes_list
                })
        except Exception as e:
            logger.error('导出激活码时出错:', e)
            return jsonify({'success': False, 'message': f'导出失败: {str(e)}'}), 500
    
    @app.route('/admin/codes/delete', methods=['POST'])
    @authenticate_admin
    def admin_delete_codes():
        data = request.json
        code_ids = data.get('ids', [])
        batch_id = data.get('batchId')
        delete_used = data.get('deleteUsed', False)
        
        if not code_ids and not batch_id:
            return jsonify({'success': False, 'message': '必须提供激活码ID或批次ID'}), 400
        
        try:
            cursor = db.conn.cursor()
            
            if batch_id:
                if delete_used:
                    cursor.execute('DELETE FROM activation_codes WHERE batch_id = ?', (batch_id,))
                else:
                    cursor.execute('DELETE FROM activation_codes WHERE batch_id = ? AND is_used = 0', (batch_id,))
            else:
                if delete_used:
                    placeholders = ','.join('?' * len(code_ids))
                    cursor.execute(f'DELETE FROM activation_codes WHERE id IN ({placeholders})', code_ids)
                else:
                    placeholders = ','.join('?' * len(code_ids))
                    cursor.execute(f'DELETE FROM activation_codes WHERE id IN ({placeholders}) AND is_used = 0', code_ids)
            
            deleted_count = cursor.rowcount
            db.conn.commit()
            
            logger.info(f'删除了 {deleted_count} 个激活码')
            
            # 记录删除兑换码日志
            if batch_id:
                append_system_log('admin', '兑换码管理/删除兑换码', f'删除批次{batch_id} 共{deleted_count}个兑换码 包含已使用:{delete_used}')
            else:
                append_system_log('admin', '兑换码管理/删除兑换码', f'删除{len(code_ids)}个兑换码 共{deleted_count}个 包含已使用:{delete_used}')
            
            return jsonify({
                'success': True,
                'message': f'成功删除 {deleted_count} 个激活码'
            })
        except Exception as e:
            logger.error('删除激活码时出错:', e)
            return jsonify({'success': False, 'message': f'删除失败: {str(e)}'}), 500
    
    @app.route('/admin/bindings/add', methods=['POST'])
    @authenticate_admin
    def admin_add_binding():
        data = request.json
        order_number = data.get('orderNumber', '').strip()
        group_number = data.get('groupNumber', '').strip()
        sku_id = data.get('skuId', '').strip()
        is_permanent = data.get('isPermanent', False)
        expiration_timestamp = data.get('expirationTimestamp')
        owner = data.get('owner', 'admin')

        # 如果激活码为空，自动生成
        if not order_number:
            import random, string
            order_number = ''.join(random.choices(string.ascii_uppercase + string.digits, k=16))

        if not all([group_number, sku_id]):
            return jsonify({'success': False, 'message': '群号和SKU ID不能为空。'}), 400
        
        try:
            cursor = db.conn.cursor()
            
            cursor.execute(
                'SELECT * FROM bindings WHERE activation_code = ? AND group_number = ?',
                (order_number, group_number)
            )
            existing = cursor.fetchone()
            
            if existing:
                return jsonify({'success': False, 'message': f'激活码 {order_number} 已绑定到群号 {existing["group_number"]}。'}), 409
            
            expiration_time = None
            expiration_date_display = '永久'
            
            if not is_permanent and expiration_timestamp:
                try:
                    from datetime import timezone

                    # 处理ISO格式的时间戳
                    timestamp_str = expiration_timestamp
                    if timestamp_str.endswith('Z'):
                        timestamp_str = timestamp_str[:-1] + '+00:00'
                    elif timestamp_str.endswith('.000Z'):
                        timestamp_str = timestamp_str[:-5] + '+00:00'

                    # 解析时间戳
                    if 'T' in timestamp_str:
                        if '+' in timestamp_str or timestamp_str.endswith('Z'):
                            # 带时区信息的时间戳
                            expiration_dt = datetime.fromisoformat(timestamp_str).replace(tzinfo=None)
                        else:
                            # 不带时区信息的时间戳
                            expiration_dt = datetime.fromisoformat(timestamp_str)
                    else:
                        expiration_dt = datetime.fromisoformat(timestamp_str)

                    expiration_time = expiration_dt.isoformat()

                    # 确保比较的是同类型的datetime对象（都是naive datetime）
                    now = datetime.now()
                    if expiration_dt <= now:
                        return jsonify({'success': False, 'message': '到期时间必须在未来。'}), 400
                    expiration_date_display = expiration_dt.strftime('%Y-%m-%d %H:%M:%S')
                except Exception as e:
                    logger.error(f'解析到期时间失败: {e}, 时间戳: {expiration_timestamp}')
                    return jsonify({'success': False, 'message': '到期时间格式错误。'}), 400
            
            cursor.execute(
                'INSERT INTO bindings (activation_code, sku_id, group_number, owner_username, expiration_time) VALUES (?, ?, ?, ?, ?)',
                (order_number, sku_id, group_number, owner, expiration_time)
            )
            db.conn.commit()
            
            logger.info(f'后台管理已添加绑定: 激活码 {order_number} (SKU {sku_id}) 到群组 {group_number}, 到期时间: {expiration_date_display}, 所有者: {owner}')
            append_system_log('admin', '绑定管理/新增绑定', f'激活码{order_number} 群{group_number} 到期{expiration_date_display} 用户{owner}')
            
            return jsonify({
                'success': True,
                'message': '绑定添加成功！',
                'data': {
                    'id': cursor.lastrowid,
                    'orderNumber': order_number,
                    'groupNumber': group_number,
                    'skuId': sku_id,
                    'skuType': config.get_tier_name(sku_id),
                    'expirationDate': expiration_date_display,
                    'owner': owner
                }
            })
        except Exception as e:
            logger.error('后台管理添加绑定过程中出错:', e)
            return jsonify({'success': False, 'message': f'添加绑定失败: {str(e)}'}), 500
    
    @app.route('/admin/bindings/update', methods=['POST'])
    @authenticate_admin
    def admin_update_binding():
        data = request.json
        original_order_number = data.get('originalOrderNumber', '').strip()
        original_sku_id = data.get('originalSkuId', '').strip()
        new_order_number = data.get('newOrderNumber', '').strip()
        new_sku_id = data.get('newSkuId', '').strip()
        new_group_number = data.get('newGroupNumber', '').strip()
        is_permanent = data.get('isPermanent', False)
        expiration_timestamp = data.get('expirationTimestamp')
        owner = data.get('owner')
        
        if not all([original_order_number, new_order_number, new_sku_id, new_group_number]):
            return jsonify({'success': False, 'message': '所有必填字段均不能为空。'}), 400
        
        try:
            cursor = db.conn.cursor()
            
            cursor.execute(
                'SELECT * FROM bindings WHERE activation_code = ?',
                (original_order_number,)
            )
            original = cursor.fetchone()
            
            if not original:
                return jsonify({'success': False, 'message': f'未找到激活码 {original_order_number} 的原始绑定信息。'}), 404
            
            expiration_time = None
            expiration_date_display = '永久'
            
            if not is_permanent and expiration_timestamp:
                expiration_time = datetime.fromisoformat(expiration_timestamp.replace('T', ' ')).isoformat()
                if datetime.fromisoformat(expiration_time) <= datetime.now():
                    return jsonify({'success': False, 'message': '到期时间必须在未来。'}), 400
                expiration_date_display = datetime.fromisoformat(expiration_time).strftime('%Y-%m-%d %H:%M:%S')
            
            current_owner = owner if owner else original['owner_username']
            
            cursor.execute(
                'UPDATE bindings SET activation_code = ?, sku_id = ?, group_number = ?, owner_username = ?, expiration_time = ? WHERE id = ?',
                (new_order_number, new_sku_id, new_group_number, current_owner, expiration_time, original['id'])
            )
            db.conn.commit()
            
            logger.info(f'后台管理已更新绑定: 新激活码 {new_order_number} (新SKU {new_sku_id}) 到群组 {new_group_number}, 所有者: {current_owner}')
            # 操作日志：更改绑定群号或到期时间（按需求示例格式）
            try:
                ts = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                append_system_log('admin', '绑定管理/修改绑定', f'修改用户{current_owner}的群聊绑定/群{new_group_number}的到期时间', when=ts)
            except Exception:
                pass
            
            return jsonify({
                'success': True,
                'message': '绑定更新成功！',
                'data': {
                    'id': original['id'],
                    'orderNumber': new_order_number,
                    'groupNumber': new_group_number,
                    'skuId': new_sku_id,
                    'skuType': config.get_tier_name(new_sku_id),
                    'expirationDate': expiration_date_display,
                    'owner': current_owner
                }
            })
        except Exception as e:
            logger.error('后台管理更新绑定过程中出错:', e)
            return jsonify({'success': False, 'message': f'更新绑定失败: {str(e)}'}), 500
    
    @app.route('/admin/bindings/delete', methods=['POST'])
    @authenticate_admin
    def admin_delete_binding():
        data = request.json
        order_number = data.get('orderNumber', '').strip()
        sku_id = data.get('skuId', '').strip()
        
        if not order_number:
            return jsonify({'success': False, 'message': '激活码不能为空。'}), 400
        
        try:
            cursor = db.conn.cursor()
            
            cursor.execute(
                'SELECT * FROM bindings WHERE activation_code = ?',
                (order_number,)
            )
            binding = cursor.fetchone()
            
            if not binding:
                return jsonify({'success': False, 'message': f'未找到激活码 {order_number} 的绑定信息。'}), 404
            
            owner = binding['owner_username']
            
            cursor.execute(
                'DELETE FROM bindings WHERE activation_code = ?',
                (order_number,)
            )
            db.conn.commit()
            
            logger.info(f'后台管理已删除激活码为 {order_number} 的绑定')
            append_system_log('admin', '绑定管理/删除绑定', f'激活码{order_number} 用户{owner}')
            
            return jsonify({
                'success': True,
                'message': '绑定删除成功！',
                'data': {'orderNumber': order_number, 'owner': owner}
            })
        except Exception as e:
            logger.error('后台管理删除绑定过程中出错:', e)
            return jsonify({'success': False, 'message': f'删除绑定失败: {str(e)}'}), 500

    @app.route('/admin/bindings/delete-group', methods=['POST'])
    @authenticate_admin
    def admin_delete_group_bindings():
        """删除群组的所有绑定"""
        data = request.json
        group_number = data.get('groupNumber', '').strip()
        sku_id = data.get('skuId', '').strip()

        if not group_number or not sku_id:
            return jsonify({'success': False, 'message': '群号和SKU ID不能为空'}), 400

        try:
            cursor = db.conn.cursor()

            # 检查是否存在该群组的绑定
            cursor.execute(
                'SELECT COUNT(*) as count FROM bindings WHERE group_number = ? AND sku_id = ?',
                (group_number, sku_id)
            )
            count = cursor.fetchone()['count']

            if count == 0:
                return jsonify({'success': False, 'message': f'未找到群组 {group_number} 的绑定信息。'}), 404

            # 删除该群组该SKU的所有绑定
            cursor.execute(
                'DELETE FROM bindings WHERE group_number = ? AND sku_id = ?',
                (group_number, sku_id)
            )
            db.conn.commit()

            logger.info(f'后台管理已删除群组 {group_number} 的所有 {sku_id} 绑定，共删除 {count} 条记录')
            append_system_log('admin', '绑定管理/删除群组绑定', f'群组{group_number} SKU{sku_id} 共{count}条')

            return jsonify({
                'success': True,
                'message': f'成功删除 {count} 条绑定记录',
                'data': {'groupNumber': group_number, 'skuId': sku_id, 'deletedCount': count}
            })

        except Exception as e:
            logger.error('后台管理删除群组绑定过程中出错:', e)
            return jsonify({'success': False, 'message': f'删除群组绑定失败: {str(e)}'}), 500





    # Robot management routes
    @app.route('/admin/robots', methods=['GET'])
    @authenticate_admin
    def admin_get_robots():
        try:
            cursor = db.conn.cursor()
            cursor.execute('SELECT * FROM robots ORDER BY created_time DESC')
            rows = cursor.fetchall()
            
            robots_list = []
            for row in rows:
                robots_list.append({
                    'id': row['id'],
                    'botAccount': row['bot_account'],
                    'botName': row['bot_name'],
                    'apiUrl': row['api_url'],
                    'apiToken': row['api_token'],
                    'isActive': bool(row['is_active']),
                    'createdTime': row['created_time'],
                    'updatedTime': row['updated_time']
                })
            
            return jsonify({'success': True, 'data': robots_list})
        except Exception as e:
            logger.error('获取机器人列表时出错:', e)
            return jsonify({'success': False, 'message': f'获取失败: {str(e)}'}), 500
    
    @app.route('/admin/robots/save', methods=['POST'])
    @authenticate_admin
    def admin_save_robot():
        data = request.json
        robot_id = data.get('id')
        bot_account = data.get('botAccount', '').strip()
        bot_name = data.get('botName', '').strip()
        api_url = data.get('apiUrl', '').strip()
        api_token = data.get('apiToken', '').strip()
        
        if not all([bot_account, bot_name, api_url]):
            return jsonify({'success': False, 'message': '机器人账号、名称和API地址不能为空'}), 400
        
        if not api_url.endswith('/'):
            api_url += '/'
        
        try:
            cursor = db.conn.cursor()
            
            if robot_id:
                cursor.execute('''
                    UPDATE robots 
                    SET bot_name = ?, api_url = ?, api_token = ?, updated_time = ?
                    WHERE id = ?
                ''', (bot_name, api_url, api_token, datetime.now().isoformat(), robot_id))
                
                message = '机器人信息更新成功'
            else:
                cursor.execute('SELECT * FROM robots WHERE bot_account = ?', (bot_account,))
                if cursor.fetchone():
                    return jsonify({'success': False, 'message': '该机器人账号已存在'}), 409
                
                cursor.execute('''
                    INSERT INTO robots (bot_account, bot_name, api_url, api_token)
                    VALUES (?, ?, ?, ?)
                ''', (bot_account, bot_name, api_url, api_token))
                robot_id = cursor.lastrowid
                message = '机器人添加成功'
            
            db.conn.commit()
            
            action = '更新' if robot_id else '新增'
            append_system_log('admin', '机器人管理/保存机器人', f'{action}机器人 {bot_name}({bot_account})')
            
            return jsonify({
                'success': True,
                'message': message,
                'data': {'id': robot_id}
            })
        except Exception as e:
            logger.error('保存机器人信息时出错:', e)
            return jsonify({'success': False, 'message': f'保存失败: {str(e)}'}), 500
    
    @app.route('/admin/robots/delete', methods=['POST'])
    @authenticate_admin
    def admin_delete_robot():
        data = request.json
        robot_id = data.get('id')
        
        if not robot_id:
            return jsonify({'success': False, 'message': '机器人ID不能为空'}), 400
        
        try:
            cursor = db.conn.cursor()
            cursor.execute('DELETE FROM robots WHERE id = ?', (robot_id,))
            db.conn.commit()
            
            append_system_log('admin', '机器人管理/删除机器人', f'机器人ID:{robot_id}')
            
            return jsonify({
                'success': True,
                'message': '机器人删除成功'
            })
        except Exception as e:
            logger.error('删除机器人时出错:', e)
            return jsonify({'success': False, 'message': f'删除失败: {str(e)}'}), 500
    
    @app.route('/admin/robots/<int:robot_id>/groups', methods=['GET'])
    @authenticate_admin
    def admin_get_robot_groups(robot_id):
        try:
            cursor = db.conn.cursor()
            cursor.execute('''
                SELECT rg.*, r.bot_name, r.bot_account 
                FROM robot_groups rg
                JOIN robots r ON rg.robot_id = r.id
                WHERE rg.robot_id = ?
                ORDER BY rg.group_name
            ''', (robot_id,))
            
            rows = cursor.fetchall()
            
            groups_list = []
            bot_info = None
            
            for row in rows:
                if not bot_info:
                    bot_info = {
                        'botName': row['bot_name'],
                        'botAccount': row['bot_account']
                    }
                
                groups_list.append({
                    'id': row['id'],
                    'groupId': row['group_id'],
                    'groupName': row['group_name'],
                    'memberCount': row['member_count'],
                    'maxMemberCount': row['max_member_count'],
                    'groupRemark': row['group_remark'],
                    'lastUpdate': row['last_update']
                })
            
            return jsonify({
                'success': True,
                'data': {
                    'botInfo': bot_info,
                    'groups': groups_list
                }
            })
        except Exception as e:
            logger.error('获取机器人群聊列表时出错:', e)
            return jsonify({'success': False, 'message': f'获取失败: {str(e)}'}), 500
    
    @app.route('/admin/robots/<int:robot_id>/refresh-groups', methods=['POST'])
    @authenticate_admin
    def admin_refresh_robot_groups(robot_id):
        try:
            cursor = db.conn.cursor()
            
            cursor.execute('SELECT * FROM robots WHERE id = ?', (robot_id,))
            robot = cursor.fetchone()
            
            if not robot:
                return jsonify({'success': False, 'message': '机器人不存在'}), 404
            
            api_url = robot['api_url']
            api_token = robot['api_token']
            
            import requests
            
            headers = {}
            if api_token:
                headers['Authorization'] = f'Bearer {api_token}'
            
            try:
                full_url = api_url + 'get_group_list'
                
                response = requests.post(
                    full_url,
                    json={'next_token': ''},
                    headers=headers,
                    timeout=10
                )
                
                if response.status_code != 200:
                    return jsonify({'success': False, 'message': f'API请求失败，状态码: {response.status_code}'}), 500
                
                result = response.json()
                
                if result.get('retcode') != 0:
                    return jsonify({'success': False, 'message': f'API返回错误: {result.get("message", "未知错误")}'}), 500
                
                groups = result.get('data', [])
                
                cursor.execute('DELETE FROM robot_groups WHERE robot_id = ?', (robot_id,))
                
                for group in groups:
                    cursor.execute('''
                        INSERT INTO robot_groups 
                        (robot_id, group_id, group_name, member_count, max_member_count, group_remark)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (
                        robot_id,
                        str(group.get('group_id', '')),
                        group.get('group_name', '未知群名'),
                        group.get('member_count', 0),
                        group.get('max_member_count', 0),
                        group.get('group_remark', '')
                    ))
                
                db.conn.commit()
                
                # 记录单个机器人刷新群聊日志
                append_system_log('admin', '机器人管理/刷新群聊', f'机器人{robot["bot_name"]}({robot["bot_account"]}) 刷新群聊{len(groups)}个')
                
                return jsonify({
                    'success': True,
                    'message': f'成功获取 {len(groups)} 个群聊',
                    'data': {
                        'count': len(groups)
                    }
                })
                
            except requests.exceptions.RequestException as e:
                return jsonify({'success': False, 'message': f'请求API失败: {str(e)}'}), 500
                
        except Exception as e:
            logger.error('刷新机器人群聊列表时出错:', e)
            return jsonify({'success': False, 'message': f'刷新失败: {str(e)}'}), 500
    
    @app.route('/admin/robots/refresh-all-groups', methods=['POST'])
    @authenticate_admin
    def admin_refresh_all_robot_groups():
        try:
            cursor = db.conn.cursor()
            
            cursor.execute('SELECT * FROM robots WHERE is_active = 1')
            robots = cursor.fetchall()
            
            if not robots:
                return jsonify({'success': False, 'message': '没有可用的机器人'}), 404
            
            total_groups = 0
            failed_robots = []
            success_robots = []
            
            cursor.execute('DELETE FROM robot_groups')
            
            import requests
            
            for robot in robots:
                robot_id = robot['id']
                api_url = robot['api_url']
                api_token = robot['api_token']
                bot_account = robot['bot_account']
                
                headers = {}
                if api_token:
                    headers['Authorization'] = f'Bearer {api_token}'
                
                try:
                    full_url = api_url + 'get_group_list'
                    
                    response = requests.post(
                        full_url,
                        json={'next_token': ''},
                        headers=headers,
                        timeout=10
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        
                        if result.get('retcode') == 0:
                            groups = result.get('data', [])
                            
                            for group in groups:
                                cursor.execute('''
                                    INSERT INTO robot_groups 
                                    (robot_id, group_id, group_name, member_count, max_member_count, group_remark)
                                    VALUES (?, ?, ?, ?, ?, ?)
                                ''', (
                                    robot_id,
                                    str(group.get('group_id', '')),
                                    group.get('group_name', '未知群名'),
                                    group.get('member_count', 0),
                                    group.get('max_member_count', 0),
                                    group.get('group_remark', '')
                                ))
                            
                            success_robots.append({
                                'botAccount': bot_account,
                                'groupCount': len(groups)
                            })
                            total_groups += len(groups)
                        else:
                            failed_robots.append({
                                'botAccount': bot_account,
                                'error': result.get('message', '未知错误')
                            })
                    else:
                        failed_robots.append({
                            'botAccount': bot_account,
                            'error': f'状态码: {response.status_code}'
                        })
                        
                except requests.exceptions.RequestException as e:
                    failed_robots.append({
                        'botAccount': bot_account,
                        'error': str(e)
                    })
            
            db.conn.commit()
            
            # 记录刷新群聊日志
            append_system_log('admin', '机器人管理/刷新群聊', f'成功机器人{len(success_robots)}个 失败机器人{len(failed_robots)}个 总计群聊{total_groups}个')
            
            return jsonify({
                'success': True,
                'message': f'成功获取 {total_groups} 个群聊',
                'data': {
                    'totalGroups': total_groups,
                    'successRobots': success_robots,
                    'failedRobots': failed_robots
                }
            })
            
        except Exception as e:
            logger.error('刷新所有机器人群聊列表时出错:', e)
            return jsonify({'success': False, 'message': f'刷新失败: {str(e)}'}), 500
    
    @app.route('/admin/robots/leave-group', methods=['POST'])
    @authenticate_admin
    def admin_robot_leave_group():
        try:
            data = request.get_json()
            robot_id = data.get('robotId')
            group_id = data.get('groupId')
            
            if not robot_id or not group_id:
                return jsonify({'success': False, 'message': '参数不完整'}), 400
            
            cursor = db.conn.cursor()
            
            cursor.execute('SELECT * FROM robots WHERE id = ?', (robot_id,))
            robot = cursor.fetchone()
            
            if not robot:
                return jsonify({'success': False, 'message': '机器人不存在'}), 404
            
            import requests
            
            headers = {}
            if robot['api_token']:
                headers['Authorization'] = f'Bearer {robot["api_token"]}'
            
            response = requests.post(
                robot['api_url'] + 'set_group_leave',
                json={'group_id': str(group_id)},
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('retcode') == 0:
                    cursor.execute('DELETE FROM robot_groups WHERE robot_id = ? AND group_id = ?', 
                                 (robot_id, group_id))
                    db.conn.commit()
                    
                    # 记录退群日志
                    append_system_log('admin', '机器人管理/退群', f'机器人{robot["bot_name"]}({robot["bot_account"]}) 退出群{group_id}')
                    
                    return jsonify({'success': True, 'message': '退群成功'})
                else:
                    return jsonify({'success': False, 'message': result.get('message', '退群失败')}), 400
            else:
                return jsonify({'success': False, 'message': f'API调用失败: {response.status_code}'}), 500
                
        except Exception as e:
            logger.error('机器人退群时出错:', e)
            return jsonify({'success': False, 'message': f'退群失败: {str(e)}'}), 500
    
    @app.route('/admin/groups/broadcast', methods=['POST'])
    @authenticate_admin
    def admin_group_broadcast():
        try:
            data = request.get_json()
            message = data.get('message')
            group_ids = data.get('groupIds', [])
            
            if not message:
                return jsonify({'success': False, 'message': '消息内容不能为空'}), 400
            
            cursor = db.conn.cursor()
            
            if group_ids:
                placeholders = ','.join('?' * len(group_ids))
                query = f'''
                    SELECT DISTINCT rg.group_id, r.id as robot_id, r.bot_account, 
                           r.api_url, r.api_token, rg.group_name
                    FROM robot_groups rg
                    JOIN robots r ON rg.robot_id = r.id
                    WHERE r.is_active = 1 AND rg.group_id IN ({placeholders})
                '''
                cursor.execute(query, group_ids)
            else:
                cursor.execute('''
                    SELECT DISTINCT rg.group_id, r.id as robot_id, r.bot_account, 
                           r.api_url, r.api_token, rg.group_name
                    FROM robot_groups rg
                    JOIN robots r ON rg.robot_id = r.id
                    WHERE r.is_active = 1
                ''')
            
            groups = cursor.fetchall()
            
            if not groups:
                return jsonify({'success': False, 'message': '没有可发送的群聊'}), 404
            
            import requests
            
            success_count = 0
            failed_groups = []
            
            message_data = [
                {
                    "type": "text",
                    "data": {
                        "text": message
                    }
                }
            ]
            
            for group in groups:
                headers = {}
                if group['api_token']:
                    headers['Authorization'] = f'Bearer {group["api_token"]}'
                
                try:
                    response = requests.post(
                        group['api_url'] + 'send_group_msg',
                        json={
                            'group_id': group['group_id'],
                            'message': message_data
                        },
                        headers=headers,
                        timeout=10
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        if result.get('retcode') == 0:
                            success_count += 1
                        else:
                            failed_groups.append({
                                'groupId': group['group_id'],
                                'groupName': group['group_name'],
                                'error': result.get('message', '发送失败')
                            })
                    else:
                        failed_groups.append({
                            'groupId': group['group_id'],
                            'groupName': group['group_name'],
                            'error': f'状态码: {response.status_code}'
                        })
                        
                except Exception as e:
                    failed_groups.append({
                        'groupId': group['group_id'],
                        'groupName': group['group_name'],
                        'error': str(e)
                    })
            
            return jsonify({
                'success': True,
                'message': f'发送完成：成功 {success_count} 个，失败 {len(failed_groups)} 个',
                'data': {
                    'successCount': success_count,
                    'failedGroups': failed_groups,
                    'totalGroups': len(groups)
                }
            })
            
            # 记录广播日志
            append_system_log('admin', '群聊管理/广播消息', f'成功{success_count}个群 失败{len(failed_groups)}个群 总计{len(groups)}个群')
            
        except Exception as e:
            logger.error('群聊广播时出错:', e)
            return jsonify({'success': False, 'message': f'广播失败: {str(e)}'}), 500
    
    @app.route('/admin/groups/all', methods=['GET'])
    @authenticate_admin
    def admin_get_all_groups():
        try:
            cursor = db.conn.cursor()
            
            cursor.execute('''
                SELECT 
                    rg.group_id,
                    rg.group_name,
                    MAX(rg.member_count) as member_count,
                    MAX(rg.max_member_count) as max_member_count,
                    rg.group_remark,
                    GROUP_CONCAT(r.bot_account, ',') as bot_accounts,
                    GROUP_CONCAT(r.bot_name, ',') as bot_names,
                    GROUP_CONCAT(r.id, ',') as robot_ids,
                    COUNT(DISTINCT r.id) as bot_count,
                    CASE WHEN b.group_number IS NOT NULL THEN 1 ELSE 0 END as is_authorized
                FROM robot_groups rg
                JOIN robots r ON rg.robot_id = r.id
                LEFT JOIN bindings b ON rg.group_id = b.group_number
                GROUP BY rg.group_id
                ORDER BY rg.group_name
            ''')
            
            rows = cursor.fetchall()
            
            groups_list = []
            for row in rows:
                groups_list.append({
                    'groupId': row['group_id'],
                    'groupName': row['group_name'],
                    'memberCount': row['member_count'],
                    'maxMemberCount': row['max_member_count'],
                    'groupRemark': row['group_remark'],
                    'botAccounts': row['bot_accounts'].split(',') if row['bot_accounts'] else [],
                    'botNames': row['bot_names'].split(',') if row['bot_names'] else [],
                    'robotIds': row['robot_ids'].split(',') if row['robot_ids'] else [],
                    'botCount': row['bot_count'],
                    'isAuthorized': bool(row['is_authorized'])
                })
            
            cursor.execute('''
                SELECT 
                    r.bot_account,
                    r.bot_name,
                    COUNT(rg.id) as group_count
                FROM robots r
                LEFT JOIN robot_groups rg ON r.id = rg.robot_id
                GROUP BY r.id
                ORDER BY r.bot_name
            ''')
            
            bot_stats = []
            for row in cursor.fetchall():
                bot_stats.append({
                    'botAccount': row['bot_account'],
                    'botName': row['bot_name'],
                    'groupCount': row['group_count']
                })
            
            return jsonify({
                'success': True,
                'data': {
                    'groups': groups_list,
                    'botStats': bot_stats,
                    'totalGroups': len(groups_list)
                }
            })
            
        except Exception as e:
            logger.error('获取所有群聊列表时出错:', e)
            return jsonify({'success': False, 'message': f'获取失败: {str(e)}'}), 500

    # 用户管理路由
    @app.route('/admin/users', methods=['GET'])
    @authenticate_admin
    def admin_get_users():
        """获取所有用户列表"""
        try:
            cursor = db.conn.cursor()
            cursor.execute('''
                SELECT 
                    u.id,
                    u.username,
                    u.email,
                    u.qq,
                    u.register_time,
                    u.is_banned,
                    u.ban_reason,
                    u.ban_time,
                    COUNT(DISTINCT b.group_number) as binding_count,
                    COUNT(DISTINCT b.activation_code) as code_count
                FROM users u
                LEFT JOIN bindings b ON u.username = b.owner_username
                GROUP BY u.id
                ORDER BY u.id DESC
            ''')
            
            users = []
            for row in cursor.fetchall():
                users.append({
                    'id': row['id'],
                    'username': row['username'],
                    'email': row['email'],
                    'qq': row['qq'],
                    'registerTime': row['register_time'],
                    'isBanned': bool(row['is_banned']) if row['is_banned'] is not None else False,
                    'banReason': row['ban_reason'],
                    'banTime': row['ban_time'],
                    'bindingCount': row['binding_count'],
                    'codeCount': row['code_count']
                })
            
            return jsonify({'success': True, 'data': users})
        except Exception as e:
            logger.error('获取用户列表时出错:', e)
            return jsonify({'success': False, 'message': str(e)}), 500
    
    @app.route('/admin/users/create', methods=['POST'])
    @authenticate_admin
    def admin_create_user():
        """创建新用户"""
        data = request.json
        username = data.get('username', '').strip()
        email = data.get('email', '').strip()
        password = data.get('password', '').strip()
        qq = data.get('qq', '').strip()
        
        if not all([username, email, password, qq]):
            return jsonify({'success': False, 'message': '所有字段都不能为空'}), 400
        
        if len(password) < 6:
            return jsonify({'success': False, 'message': '密码长度至少6位'}), 400
        
        if not qq.isdigit() or len(qq) < 5 or len(qq) > 18:
            return jsonify({'success': False, 'message': 'QQ号格式不正确'}), 400
        
        try:
            import bcrypt
            hashed = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())
            
            cursor = db.conn.cursor()
            
            # 检查用户名是否已存在
            cursor.execute('SELECT id FROM users WHERE username = ?', (username,))
            if cursor.fetchone():
                return jsonify({'success': False, 'message': '该用户名已被注册'}), 409
            
            # 检查邮箱是否已存在
            cursor.execute('SELECT id FROM users WHERE email = ?', (email,))
            if cursor.fetchone():
                return jsonify({'success': False, 'message': '该邮箱已被注册'}), 409
            
            # 检查QQ号是否已存在
            cursor.execute('SELECT id FROM users WHERE qq = ?', (qq,))
            if cursor.fetchone():
                return jsonify({'success': False, 'message': '该QQ号已被注册'}), 409
            
            # 创建用户
            cursor.execute(
                'INSERT INTO users (username, email, password, qq, register_time) VALUES (?, ?, ?, ?, ?)',
                (username, email, hashed.decode('utf-8'), qq, datetime.now().isoformat())
            )
            user_id = cursor.lastrowid
            db.conn.commit()
            
            logger.info(f'管理员创建了新用户: {username} (ID: {user_id})')
            append_system_log('admin', '用户管理/创建用户', f'创建用户{username} 邮箱{email} QQ{qq}')
            
            return jsonify({
                'success': True,
                'message': '用户创建成功',
                'data': {
                    'id': user_id,
                    'username': username,
                    'email': email,
                    'qq': qq
                }
            })
        except Exception as e:
            logger.error('创建用户时出错:', e)
            return jsonify({'success': False, 'message': f'创建失败: {str(e)}'}), 500
    
    @app.route('/admin/users/update-email', methods=['POST'])
    @authenticate_admin
    def admin_update_user_email():
        """修改用户邮箱"""
        data = request.json
        user_id = data.get('userId')
        new_email = data.get('newEmail', '').strip()
        
        if not user_id or not new_email:
            return jsonify({'success': False, 'message': '用户ID和新邮箱不能为空'}), 400
        
        # 验证邮箱格式
        import re
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, new_email):
            return jsonify({'success': False, 'message': '邮箱格式不正确'}), 400
        
        try:
            cursor = db.conn.cursor()
            
            # 检查邮箱是否已被其他用户使用
            cursor.execute('SELECT id FROM users WHERE email = ? AND id != ?', (new_email, user_id))
            if cursor.fetchone():
                return jsonify({'success': False, 'message': '该邮箱已被其他用户绑定'}), 400
            
            # 获取原邮箱信息用于日志
            cursor.execute('SELECT email FROM users WHERE id = ?', (user_id,))
            user = cursor.fetchone()
            if not user:
                return jsonify({'success': False, 'message': '用户不存在'}), 404
            
            old_email = user['email']
            
            cursor.execute(
                'UPDATE users SET email = ? WHERE id = ?',
                (new_email, user_id)
            )
            db.conn.commit()
            
            if cursor.rowcount == 0:
                return jsonify({'success': False, 'message': '用户不存在'}), 404
            
            logger.info(f'管理员修改了用户ID {user_id} 的邮箱: {old_email} -> {new_email}')
            append_system_log('admin', '用户管理/修改邮箱', f'用户ID:{user_id} 邮箱{old_email}->{new_email}')
            
            return jsonify({'success': True, 'message': '邮箱修改成功'})
        except Exception as e:
            logger.error('修改用户邮箱时出错:', e)
            return jsonify({'success': False, 'message': str(e)}), 500
    
    @app.route('/admin/users/update-password', methods=['POST'])
    @authenticate_admin
    def admin_update_user_password():
        """修改用户密码"""
        data = request.json
        user_id = data.get('userId')
        new_password = data.get('newPassword', '').strip()
        
        if not user_id or not new_password:
            return jsonify({'success': False, 'message': '用户ID和新密码不能为空'}), 400
        
        if len(new_password) < 6:
            return jsonify({'success': False, 'message': '密码长度至少6位'}), 400
        
        try:
            import bcrypt
            hashed = bcrypt.hashpw(new_password.encode('utf-8'), bcrypt.gensalt())
            
            cursor = db.conn.cursor()
            cursor.execute(
                'UPDATE users SET password = ? WHERE id = ?',
                (hashed.decode('utf-8'), user_id)
            )
            db.conn.commit()
            
            if cursor.rowcount == 0:
                return jsonify({'success': False, 'message': '用户不存在'}), 404
            
            logger.info(f'管理员修改了用户ID {user_id} 的密码')
            append_system_log('admin', '用户管理/修改密码', f'修改用户ID:{user_id}的密码')
            return jsonify({'success': True, 'message': '密码修改成功'})
        except Exception as e:
            logger.error('修改用户密码时出错:', e)
            return jsonify({'success': False, 'message': str(e)}), 500
    
    @app.route('/admin/users/update-qq', methods=['POST'])
    @authenticate_admin
    def admin_update_user_qq():
        """修改用户QQ"""
        data = request.json
        user_id = data.get('userId')
        new_qq = data.get('newQQ', '').strip()
        
        if not user_id:
            return jsonify({'success': False, 'message': '用户ID不能为空'}), 400
        
        if new_qq and (not new_qq.isdigit() or len(new_qq) < 5 or len(new_qq) > 18):
            return jsonify({'success': False, 'message': 'QQ号格式不正确'}), 400
        
        try:
            cursor = db.conn.cursor()
            
            # 检查QQ号是否已被其他用户使用
            if new_qq:
                cursor.execute('SELECT id FROM users WHERE qq = ? AND id != ?', (new_qq, user_id))
                if cursor.fetchone():
                    return jsonify({'success': False, 'message': '该QQ号已被其他用户绑定'}), 400
            
            cursor.execute(
                'UPDATE users SET qq = ? WHERE id = ?',
                (new_qq if new_qq else None, user_id)
            )
            db.conn.commit()
            
            if cursor.rowcount == 0:
                return jsonify({'success': False, 'message': '用户不存在'}), 404
            
            logger.info(f'管理员修改了用户ID {user_id} 的QQ号为 {new_qq}')
            append_system_log('admin', '用户管理/修改QQ', f'修改用户ID:{user_id}的QQ号:{new_qq}')
            return jsonify({'success': True, 'message': 'QQ号修改成功'})
        except Exception as e:
            logger.error('修改用户QQ时出错:', e)
            return jsonify({'success': False, 'message': str(e)}), 500
    
    @app.route('/admin/users/ban', methods=['POST'])
    @authenticate_admin
    def admin_ban_user():
        """封禁/解封用户"""
        data = request.json
        user_id = data.get('userId')
        is_banned = data.get('isBanned', True)
        ban_reason = data.get('banReason', '').strip()
        
        if not user_id:
            return jsonify({'success': False, 'message': '用户ID不能为空'}), 400
        
        if is_banned and not ban_reason:
            return jsonify({'success': False, 'message': '封禁原因不能为空'}), 400
        
        try:
            cursor = db.conn.cursor()
            
            if is_banned:
                cursor.execute(
                    'UPDATE users SET is_banned = 1, ban_reason = ?, ban_time = ? WHERE id = ?',
                    (ban_reason, datetime.now().isoformat(), user_id)
                )
            else:
                cursor.execute(
                    'UPDATE users SET is_banned = 0, ban_reason = NULL, ban_time = NULL WHERE id = ?',
                    (user_id,)
                )
            
            db.conn.commit()
            
            if cursor.rowcount == 0:
                return jsonify({'success': False, 'message': '用户不存在'}), 404
            
            action = '封禁' if is_banned else '解封'
            logger.info(f'管理员{action}了用户ID {user_id}')
            append_system_log('admin', '用户管理/封禁用户', f'{action}用户ID:{user_id} 原因:{ban_reason if is_banned else "解封"}')
            return jsonify({'success': True, 'message': f'{action}成功'})
        except Exception as e:
            logger.error(f'{action}用户时出错:', e)
            return jsonify({'success': False, 'message': str(e)}), 500
    
    @app.route('/admin/users/delete', methods=['POST'])
    @authenticate_admin
    def admin_delete_user():
        """删除用户"""
        data = request.json
        user_id = data.get('userId')
        
        if not user_id:
            return jsonify({'success': False, 'message': '用户ID不能为空'}), 400
        
        try:
            cursor = db.conn.cursor()
            
            # 先获取用户名
            cursor.execute('SELECT username FROM users WHERE id = ?', (user_id,))
            user = cursor.fetchone()
            
            if not user:
                return jsonify({'success': False, 'message': '用户不存在'}), 404
            
            username = user['username']
            
            # 删除用户的所有绑定记录
            cursor.execute('DELETE FROM bindings WHERE owner_username = ?', (username,))
            
            # 删除用户
            cursor.execute('DELETE FROM users WHERE id = ?', (user_id,))
            
            db.conn.commit()
            
            logger.info(f'管理员删除了用户 {username} (ID: {user_id})')
            append_system_log('admin', '用户管理/删除用户', f'删除用户{username} ID:{user_id}')
            return jsonify({'success': True, 'message': '用户删除成功'})
        except Exception as e:
            logger.error('删除用户时出错:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/admin/tiers', methods=['GET'])
    @authenticate_admin
    def admin_get_tiers():
        """获取档位配置"""
        return jsonify({'success': True, 'data': config.TIERS})
    
    @app.route('/admin/tiers/save', methods=['POST'])
    @authenticate_admin
    def admin_save_tiers():
        """保存整个档位列表（批量覆盖）。"""
        try:
            data = request.get_json() or {}
            tiers = data.get('tiers')
            if not isinstance(tiers, list):
                return jsonify({'success': False, 'message': '参数 tiers 必须是数组'}), 400
            # 基础校验：sku_id 唯一且非空
            seen = set()
            for t in tiers:
                sku = (t or {}).get('sku_id')
                if not sku:
                    return jsonify({'success': False, 'message': '每个档位必须包含 sku_id'}), 400
                if sku in seen:
                    return jsonify({'success': False, 'message': f'重复的 sku_id: {sku}'}), 400
                seen.add(sku)
            ok = config.set_tiers(tiers)
            if not ok:
                return jsonify({'success': False, 'message': '保存失败'}), 500
            append_system_log('admin', '档位管理/批量保存', f'保存{len(tiers)}个档位')
            return jsonify({'success': True, 'message': '档位已保存', 'data': config.TIERS})
        except Exception as e:
            logger.error('保存档位配置失败:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/admin/tiers/upsert', methods=['POST'])
    @authenticate_admin
    def admin_upsert_tier():
        """新增或更新单个档位。"""
        try:
            tier = request.get_json() or {}
            if not tier.get('sku_id'):
                return jsonify({'success': False, 'message': '缺少 sku_id'}), 400
            ok = config.upsert_tier(tier)
            if not ok:
                return jsonify({'success': False, 'message': '保存失败'}), 500
            action = '更新' if config.get_tier_by_sku_id(tier['sku_id']) else '新增'
            append_system_log('admin', '档位管理/保存档位', f'{action}档位 {tier.get("display_name", tier.get("sku_id"))}')
            return jsonify({'success': True, 'message': '保存成功', 'data': config.get_tier_by_sku_id(tier['sku_id'])})
        except Exception as e:
            logger.error('保存档位失败:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/admin/tiers/delete', methods=['POST'])
    @authenticate_admin
    def admin_delete_tier():
        """删除单个档位，若已被使用需前端确认后再删。"""
        try:
            data = request.get_json() or {}
            sku_id = (data.get('sku_id') or '').strip()
            if not sku_id:
                return jsonify({'success': False, 'message': '缺少 sku_id'}), 400
            # 检查是否有激活码或绑定使用此档位
            cursor = db.conn.cursor()
            cursor.execute('SELECT COUNT(*) AS cnt FROM activation_codes WHERE sku_id = ?', (sku_id,))
            codes_cnt = cursor.fetchone()['cnt']
            cursor.execute('SELECT COUNT(*) AS cnt FROM bindings WHERE sku_id = ?', (sku_id,))
            bind_cnt = cursor.fetchone()['cnt']
            if codes_cnt > 0 or bind_cnt > 0:
                return jsonify({'success': False, 'code': 'IN_USE', 'message': f'该档位正在使用中：兑换码{codes_cnt}条，绑定{bind_cnt}条'}), 400
            ok = config.delete_tier(sku_id)
            if not ok:
                return jsonify({'success': False, 'message': '删除失败，可能不存在该档位'}), 400
            append_system_log('admin', '档位管理/删除档位', f'删除档位SKU:{sku_id}')
            return jsonify({'success': True, 'message': '删除成功'})
        except Exception as e:
            logger.error('删除档位失败:', e)
            return jsonify({'success': False, 'message': str(e)}), 500
    
    # 功能黑名单管理路由
    @app.route('/admin/feature-blacklist', methods=['GET'])
    @authenticate_admin
    def admin_get_feature_blacklist():
        """获取功能黑名单定义列表"""
        try:
            # 若系统关闭黑名单功能，直接返回空
            enabled = db.get_setting('feature_blacklist_enabled', '1')
            if str(enabled) not in ('1','true','True','yes','on'):
                return jsonify({'success': True, 'data': []})
            cursor = db.conn.cursor()
            cursor.execute('''
                SELECT * FROM feature_blacklist_definitions
                ORDER BY id DESC
            ''')
            
            features = []
            for row in cursor.fetchall():
                features.append({
                    'id': row['id'],
                    'displayName': row['display_name'],
                    'actualFeatures': row['actual_features'].split(',') if row['actual_features'] else [],
                    'requestIdentifiers': row['request_identifiers'].split(',') if row['request_identifiers'] else [],
                    'allowedTiers': row['allowed_tiers'].split(',') if row['allowed_tiers'] else [],
                    'createdTime': row['created_time'],
                    'updatedTime': row['updated_time']
                })
            
            return jsonify({'success': True, 'data': features})
        except Exception as e:
            logger.error('获取功能黑名单时出错:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/admin/feature-blacklist/save', methods=['POST'])
    @authenticate_admin
    def admin_save_feature_blacklist():
        """保存功能黑名单定义"""
        data = request.json
        feature_id = data.get('id')
        display_name = data.get('displayName', '').strip()
        actual_features = ','.join(data.get('actualFeatures', []))
        request_identifiers = ','.join(data.get('requestIdentifiers', []))
        allowed_tiers = ','.join(data.get('allowedTiers', []))
        
        # 若系统关闭黑名单功能，拒绝写
        enabled = db.get_setting('feature_blacklist_enabled', '1')
        if str(enabled) not in ('1','true','True','yes','on'):
            return jsonify({'success': False, 'message': '功能黑名单已关闭'}), 400

        if not all([display_name, actual_features, request_identifiers, allowed_tiers]):
            return jsonify({'success': False, 'message': '所有字段都是必填的'}), 400
        
        try:
            cursor = db.conn.cursor()
            
            if feature_id:
                # 更新
                cursor.execute('''
                    UPDATE feature_blacklist_definitions 
                    SET display_name = ?, actual_features = ?, request_identifiers = ?, 
                        allowed_tiers = ?, updated_time = ?
                    WHERE id = ?
                ''', (display_name, actual_features, request_identifiers, allowed_tiers, 
                      datetime.now().isoformat(), feature_id))
                message = '功能黑名单更新成功'
            else:
                # 新增
                cursor.execute('''
                    INSERT INTO feature_blacklist_definitions 
                    (display_name, actual_features, request_identifiers, allowed_tiers)
                    VALUES (?, ?, ?, ?)
                ''', (display_name, actual_features, request_identifiers, allowed_tiers))
                feature_id = cursor.lastrowid
                message = '功能黑名单添加成功'
            
            db.conn.commit()
            
            action = '更新' if feature_id else '新增'
            append_system_log('admin', '功能黑名单/保存定义', f'{action}功能黑名单 {display_name}')
            
            return jsonify({
                'success': True,
                'message': message,
                'data': {'id': feature_id}
            })
        except Exception as e:
            logger.error('保存功能黑名单时出错:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/admin/feature-blacklist/delete', methods=['POST'])
    @authenticate_admin
    def admin_delete_feature_blacklist():
        """删除功能黑名单定义"""
        data = request.json
        feature_id = data.get('id')
        
        if not feature_id:
            return jsonify({'success': False, 'message': '功能ID不能为空'}), 400
        
        try:
            enabled = db.get_setting('feature_blacklist_enabled', '1')
            if str(enabled) not in ('1','true','True','yes','on'):
                return jsonify({'success': False, 'message': '功能黑名单已关闭'}), 400
            cursor = db.conn.cursor()
            cursor.execute('DELETE FROM feature_blacklist_definitions WHERE id = ?', (feature_id,))
            db.conn.commit()
            
            append_system_log('admin', '功能黑名单/删除定义', f'删除功能黑名单ID:{feature_id}')
            
            return jsonify({
                'success': True,
                'message': '功能黑名单删除成功'
            })
        except Exception as e:
            logger.error('删除功能黑名单时出错:', e)
            return jsonify({'success': False, 'message': str(e)}), 500
    
    # 功能白名单管理路由
    @app.route('/admin/feature-whitelist', methods=['GET'])
    @authenticate_admin
    def admin_get_feature_whitelist():
        """获取功能白名单定义列表"""
        try:
            # 若系统关闭白名单功能，直接返回空
            enabled = db.get_setting('feature_whitelist_enabled', '1')
            if str(enabled) not in ('1','true','True','yes','on'):
                return jsonify({'success': True, 'data': []})
            cursor = db.conn.cursor()
            cursor.execute('''
                SELECT * FROM feature_whitelist_definitions
                ORDER BY id DESC
            ''')
            
            features = []
            for row in cursor.fetchall():
                features.append({
                    'id': row['id'],
                    'displayName': row['display_name'],
                    'actualFeatures': row['actual_features'].split(',') if row['actual_features'] else [],
                    'requestIdentifiers': row['request_identifiers'].split(',') if row['request_identifiers'] else [],
                    'allowedTiers': row['allowed_tiers'].split(',') if row['allowed_tiers'] else [],
                    'createdTime': row['created_time'],
                    'updatedTime': row['updated_time']
                })
            
            return jsonify({'success': True, 'data': features})
        except Exception as e:
            logger.error('获取功能白名单时出错:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/admin/feature-whitelist/save', methods=['POST'])
    @authenticate_admin
    def admin_save_feature_whitelist():
        """保存功能白名单定义"""
        data = request.json
        feature_id = data.get('id')
        display_name = data.get('displayName', '').strip()
        actual_features = ','.join(data.get('actualFeatures', []))
        request_identifiers = ','.join(data.get('requestIdentifiers', []))
        allowed_tiers = ','.join(data.get('allowedTiers', []))
        
        # 若系统关闭白名单功能，拒绝写
        enabled = db.get_setting('feature_whitelist_enabled', '1')
        if str(enabled) not in ('1','true','True','yes','on'):
            return jsonify({'success': False, 'message': '功能白名单已关闭'}), 400

        if not all([display_name, actual_features, request_identifiers, allowed_tiers]):
            return jsonify({'success': False, 'message': '所有字段都是必填的'}), 400
        
        try:
            cursor = db.conn.cursor()
            
            if feature_id:
                # 更新
                cursor.execute('''
                    UPDATE feature_whitelist_definitions 
                    SET display_name = ?, actual_features = ?, request_identifiers = ?, 
                        allowed_tiers = ?, updated_time = ?
                    WHERE id = ?
                ''', (display_name, actual_features, request_identifiers, allowed_tiers, 
                      datetime.now().isoformat(), feature_id))
                message = '功能白名单更新成功'
            else:
                # 新增
                cursor.execute('''
                    INSERT INTO feature_whitelist_definitions 
                    (display_name, actual_features, request_identifiers, allowed_tiers)
                    VALUES (?, ?, ?, ?)
                ''', (display_name, actual_features, request_identifiers, allowed_tiers))
                feature_id = cursor.lastrowid
                message = '功能白名单添加成功'
            
            db.conn.commit()
            
            action = '更新' if feature_id else '新增'
            append_system_log('admin', '功能白名单/保存定义', f'{action}功能白名单 {display_name}')
            
            return jsonify({
                'success': True,
                'message': message,
                'data': {'id': feature_id}
            })
        except Exception as e:
            logger.error('保存功能白名单时出错:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/admin/feature-whitelist/delete', methods=['POST'])
    @authenticate_admin
    def admin_delete_feature_whitelist():
        """删除功能白名单定义"""
        data = request.json
        feature_id = data.get('id')
        
        if not feature_id:
            return jsonify({'success': False, 'message': '功能ID不能为空'}), 400
        
        try:
            enabled = db.get_setting('feature_whitelist_enabled', '1')
            if str(enabled) not in ('1','true','True','yes','on'):
                return jsonify({'success': False, 'message': '功能白名单已关闭'}), 400
            cursor = db.conn.cursor()
            cursor.execute('DELETE FROM feature_whitelist_definitions WHERE id = ?', (feature_id,))
            db.conn.commit()
            
            append_system_log('admin', '功能白名单/删除定义', f'删除功能白名单ID:{feature_id}')
            
            return jsonify({
                'success': True,
                'message': '功能白名单删除成功'
            })
        except Exception as e:
            logger.error('删除功能白名单时出错:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    # 工单管理路由
    @app.route('/admin/ticket-types', methods=['GET'])
    @authenticate_admin
    def admin_get_ticket_types():
        """获取工单类型列表"""
        try:
            cursor = db.conn.cursor()
            cursor.execute('''
                SELECT * FROM ticket_types 
                ORDER BY sort_order ASC, id ASC
            ''')
            
            types = []
            for row in cursor.fetchall():
                types.append({
                    'id': row['id'],
                    'name': row['name'],
                    'description': row['description'],
                    'isActive': bool(row['is_active']),
                    'sortOrder': row['sort_order'],
                    'createdTime': row['created_time'],
                    'updatedTime': row['updated_time']
                })
            
            return jsonify({'success': True, 'data': types})
        except Exception as e:
            logger.error('获取工单类型列表时出错:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/admin/ticket-types/save', methods=['POST'])
    @authenticate_admin
    def admin_save_ticket_type():
        """保存工单类型"""
        data = request.json
        type_id = data.get('id')
        name = data.get('name', '').strip()
        description = data.get('description', '').strip()
        is_active = data.get('isActive', True)
        sort_order = data.get('sortOrder', 0)
        
        if not name:
            return jsonify({'success': False, 'message': '类型名称不能为空'}), 400
        
        try:
            cursor = db.conn.cursor()
            
            if type_id:
                # 更新
                cursor.execute('''
                    UPDATE ticket_types 
                    SET name = ?, description = ?, is_active = ?, sort_order = ?, updated_time = ?
                    WHERE id = ?
                ''', (name, description, is_active, sort_order, datetime.now().isoformat(), type_id))
                message = '工单类型更新成功'
            else:
                # 新增
                cursor.execute('''
                    INSERT INTO ticket_types (name, description, is_active, sort_order)
                    VALUES (?, ?, ?, ?)
                ''', (name, description, is_active, sort_order))
                type_id = cursor.lastrowid
                message = '工单类型添加成功'
            
            db.conn.commit()
            
            action = '更新' if type_id else '新增'
            append_system_log('admin', '工单类型/保存类型', f'{action}工单类型 {name}')
            
            return jsonify({
                'success': True,
                'message': message,
                'data': {'id': type_id}
            })
        except sqlite3.IntegrityError:
            # 常见如 name 唯一键冲突
            db.conn.rollback()
            logger.error('保存工单类型违反唯一约束')
            return jsonify({'success': False, 'message': '类型名称已存在'}), 400
        except Exception as e:
            logger.error('保存工单类型时出错:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/admin/ticket-types/delete', methods=['POST'])
    @authenticate_admin
    def admin_delete_ticket_type():
        """删除工单类型"""
        data = request.json
        type_id = data.get('id')
        
        if not type_id:
            return jsonify({'success': False, 'message': '类型ID不能为空'}), 400
        
        try:
            cursor = db.conn.cursor()
            
            # 检查是否有工单使用此类型
            cursor.execute('SELECT COUNT(*) as count FROM tickets WHERE ticket_type_id = ?', (type_id,))
            if cursor.fetchone()['count'] > 0:
                return jsonify({'success': False, 'message': '该工单类型正在使用中，无法删除'}), 400
            
            cursor.execute('DELETE FROM ticket_types WHERE id = ?', (type_id,))
            db.conn.commit()
            
            append_system_log('admin', '工单类型/删除类型', f'删除工单类型ID:{type_id}')
            
            return jsonify({
                'success': True,
                'message': '工单类型删除成功'
            })
        except Exception as e:
            logger.error('删除工单类型时出错:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/admin/tickets', methods=['GET'])
    @authenticate_admin
    def admin_get_tickets():
        """获取工单列表"""
        try:
            page = int(request.args.get('page', 1))
            per_page = int(request.args.get('perPage', 20))
            status_filter = request.args.get('status', '')
            type_filter = request.args.get('type', '')
            priority_filter = request.args.get('priority', '')
            search_query = request.args.get('search', '')
            
            offset = (page - 1) * per_page
            
            cursor = db.conn.cursor()
            
            where_conditions = []
            params = []
            
            if status_filter:
                where_conditions.append("t.status = ?")
                params.append(status_filter)
            
            if type_filter:
                where_conditions.append("t.ticket_type_id = ?")
                params.append(type_filter)
            
            if priority_filter:
                where_conditions.append("t.priority = ?")
                params.append(priority_filter)
            
            if search_query:
                search_conditions = []
                search_param = f'%{search_query}%'
                
                search_conditions.append("t.title LIKE ?")
                params.append(search_param)
                
                search_conditions.append("t.content LIKE ?")
                params.append(search_param)
                
                search_conditions.append("u.username LIKE ?")
                params.append(search_param)
                
                search_conditions.append("t.ticket_number LIKE ?")
                params.append(search_param)
                
                where_conditions.append(f"({' OR '.join(search_conditions)})")
            
            where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""
            
            count_query = f"""
                SELECT COUNT(*) as total 
                FROM tickets t
                LEFT JOIN users u ON t.user_id = u.id
                {where_clause}
            """
            cursor.execute(count_query, params)
            total_count = cursor.fetchone()['total']
            
            data_query = f"""
                SELECT 
                    t.*,
                    u.username as user_username,
                    u.qq as user_qq,
                    tt.name as type_name,
                    a.username as admin_username
                FROM tickets t
                LEFT JOIN users u ON t.user_id = u.id
                LEFT JOIN ticket_types tt ON t.ticket_type_id = tt.id
                LEFT JOIN users a ON t.assigned_admin_id = a.id
                {where_clause}
                ORDER BY 
                    CASE t.priority 
                        WHEN 'high' THEN 1 
                        WHEN 'normal' THEN 2 
                        WHEN 'low' THEN 3 
                    END,
                    t.created_time DESC
                LIMIT ? OFFSET ?
            """
            
            cursor.execute(data_query, params + [per_page, offset])
            tickets = cursor.fetchall()
            
            tickets_list = []
            for ticket in tickets:
                tickets_list.append({
                    'id': ticket['id'],
                    'ticketNumber': ticket['ticket_number'],
                    'title': ticket['title'],
                    'content': ticket['content'],
                    'typeId': ticket['ticket_type_id'],
                    'typeName': ticket['type_name'],
                    'status': ticket['status'],
                    'priority': ticket['priority'],
                    'userId': ticket['user_id'],
                    'username': ticket['user_username'],
                    'userQQ': ticket['user_qq'],
                    'groupNumber': ticket['group_number'],
                    'assignedAdminId': ticket['assigned_admin_id'],
                    'assignedAdminName': ticket['admin_username'],
                    'createdTime': ticket['created_time'],
                    'updatedTime': ticket['updated_time'],
                    'closedTime': ticket['closed_time'],
                    'closedBy': ticket['closed_by']
                })
            
            total_pages = (total_count + per_page - 1) // per_page
            
            return jsonify({
                'success': True,
                'data': {
                    'tickets': tickets_list,
                    'pagination': {
                        'total': total_count,
                        'page': page,
                        'perPage': per_page,
                        'totalPages': total_pages
                    }
                }
            })
            
        except Exception as e:
            logger.error('获取工单列表时出错:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/admin/tickets/<int:ticket_id>', methods=['GET'])
    @authenticate_admin
    def admin_get_ticket_detail(ticket_id):
        """获取工单详情"""
        try:
            cursor = db.conn.cursor()
            
            # 获取工单基本信息
            cursor.execute('''
                SELECT 
                    t.*,
                    u.username as user_username,
                    u.qq as user_qq,
                    tt.name as type_name,
                    a.username as admin_username
                FROM tickets t
                LEFT JOIN users u ON t.user_id = u.id
                LEFT JOIN ticket_types tt ON t.ticket_type_id = tt.id
                LEFT JOIN users a ON t.assigned_admin_id = a.id
                WHERE t.id = ?
            ''', (ticket_id,))
            
            ticket = cursor.fetchone()
            if not ticket:
                return jsonify({'success': False, 'message': '工单不存在'}), 404
            
            # 获取工单回复
            cursor.execute('''
                SELECT 
                    r.*,
                    u.username,
                    u.qq
                FROM ticket_replies r
                LEFT JOIN users u ON r.user_id = u.id
                WHERE r.ticket_id = ?
                ORDER BY r.created_time ASC
            ''', (ticket_id,))
            
            replies = []
            for reply in cursor.fetchall():
                replies.append({
                    'id': reply['id'],
                    'content': reply['content'],
                    'isAdmin': bool(reply['is_admin']),
                    'username': reply['username'],
                    'qq': reply['qq'],
                    'createdTime': reply['created_time']
                })
            
            # 获取工单附件
            cursor.execute('''
                SELECT * FROM ticket_attachments 
                WHERE ticket_id = ? OR reply_id IN (
                    SELECT id FROM ticket_replies WHERE ticket_id = ?
                )
                ORDER BY upload_time ASC
            ''', (ticket_id, ticket_id))
            
            attachments = []
            for attachment in cursor.fetchall():
                attachments.append({
                    'id': attachment['id'],
                    'fileName': attachment['file_name'],
                    'filePath': attachment['file_path'],
                    'fileSize': attachment['file_size'],
                    'fileType': attachment['file_type'],
                    'uploadTime': attachment['upload_time'],
                    'isReplyAttachment': attachment['reply_id'] is not None
                })
            
            # 获取状态变更记录
            cursor.execute('''
                SELECT 
                    l.*,
                    u.username as changed_by_username
                FROM ticket_status_logs l
                LEFT JOIN users u ON l.changed_by = u.id
                WHERE l.ticket_id = ?
                ORDER BY l.changed_time DESC
            ''', (ticket_id,))
            
            status_logs = []
            for log in cursor.fetchall():
                status_logs.append({
                    'id': log['id'],
                    'oldStatus': log['old_status'],
                    'newStatus': log['new_status'],
                    'changedBy': log['changed_by_username'],
                    'changeReason': log['change_reason'],
                    'changedTime': log['changed_time']
                })
            
            ticket_data = {
                'id': ticket['id'],
                'ticketNumber': ticket['ticket_number'],
                'title': ticket['title'],
                'content': ticket['content'],
                'typeId': ticket['ticket_type_id'],
                'typeName': ticket['type_name'],
                'status': ticket['status'],
                'priority': ticket['priority'],
                'userId': ticket['user_id'],
                'username': ticket['user_username'],
                'userQQ': ticket['user_qq'],
                'groupNumber': ticket['group_number'],
                'assignedAdminId': ticket['assigned_admin_id'],
                'assignedAdminName': ticket['admin_username'],
                'createdTime': ticket['created_time'],
                'updatedTime': ticket['updated_time'],
                'closedTime': ticket['closed_time'],
                'closedBy': ticket['closed_by'],
                'replies': replies,
                'attachments': attachments,
                'statusLogs': status_logs
            }
            
            return jsonify({'success': True, 'data': ticket_data})
            
        except Exception as e:
            logger.error('获取工单详情时出错:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/admin/tickets/<int:ticket_id>/reply', methods=['POST'])
    @authenticate_admin
    def admin_reply_ticket(ticket_id):
        """管理员回复工单"""
        data = request.json
        content = data.get('content', '').strip()
        
        if not content:
            return jsonify({'success': False, 'message': '回复内容不能为空'}), 400
        
        try:
            cursor = db.conn.cursor()
            
            # 检查工单是否存在且未关闭
            cursor.execute('SELECT status FROM tickets WHERE id = ?', (ticket_id,))
            ticket = cursor.fetchone()
            if not ticket:
                return jsonify({'success': False, 'message': '工单不存在'}), 404
            
            if ticket['status'] == 'closed':
                return jsonify({'success': False, 'message': '工单已关闭，无法回复'}), 400
            
            # 添加回复
            cursor.execute('''
                INSERT INTO ticket_replies (ticket_id, user_id, content, is_admin)
                VALUES (?, ?, ?, 1)
            ''', (ticket_id, 1, content))  # 假设管理员ID为1
            
            # 更新工单状态为"处理中"
            if ticket['status'] == 'open':
                cursor.execute('''
                    UPDATE tickets SET status = 'in_progress', updated_time = ?
                    WHERE id = ?
                ''', (datetime.now().isoformat(), ticket_id))
                
                # 记录状态变更
                cursor.execute('''
                    INSERT INTO ticket_status_logs (ticket_id, old_status, new_status, changed_by, change_reason)
                    VALUES (?, ?, ?, ?, ?)
                ''', (ticket_id, 'open', 'in_progress', 1, '管理员回复'))

            # 管理员回复后，重置管理员提醒标记，允许后续用户回复再次触发管理员提醒
            try:
                cursor.execute('UPDATE tickets SET admin_notified_flag = 0 WHERE id = ?', (ticket_id,))
            except Exception:
                pass
            
            db.conn.commit()

            # 发送邮件通知：尊重系统开关与用户个人开关
            try:
                # 缺省按开启处理，避免因缺省值导致不发送
                ticket_email_enabled = str(db.get_setting('ticket_email_notify_enabled', '1')) in ('1','true','True','yes','on')
                if ticket_email_enabled:
                    admin_reply_every = str(db.get_setting('admin_reply_every_email_enabled', '0')) in ('1','true','True','yes','on')
                    # 获取工单详情和用户邮箱、用户个人开关
                    cursor.execute('''
                        SELECT t.ticket_number, t.created_time, t.user_notified_flag, u.email, u.email_notify_enabled, u.username
                        FROM tickets t
                        JOIN users u ON t.user_id = u.id
                        WHERE t.id = ?
                    ''', (ticket_id,))
                    info = cursor.fetchone()
                    if info and info['email']:
                        # 若用户显式关闭，则不发
                        try:
                            if info['email_notify_enabled'] is not None and int(info['email_notify_enabled']) == 0:
                                info = None
                        except Exception:
                            pass
                    # 去重控制：当未启用“每条回复都提醒”时，仅首次提醒
                    if info and info['email'] and not admin_reply_every:
                        try:
                            if info['user_notified_flag'] is not None and int(info['user_notified_flag']) == 1:
                                info = None
                        except Exception:
                            pass
                    if info and info['email']:
                        site_name = db.get_setting('site_title', '') or ''
                        subject = f"{site_name} 工单有新回复" if site_name else '您的工单有新回复'
                        # 简要正文（可在模板中展示完整内容）
                        content_preview = content
                        view_link = db.get_setting('site_link', '') or ''
                        html = render_work_order_email_html(
                            subject,
                            content_preview,
                            ticket_number=info['ticket_number'],
                            created_time=(info['created_time'] or '') ,
                            view_link=view_link,
                            site_name_override=site_name
                        )
                        try:
                            try:
                                app.enqueue_email(info['email'], subject, html, {'actor': 'admin', 'action': f'工单回复邮件 用户{info["username"]} 工单{info["ticket_number"]}'})
                            except Exception:
                                send_email_smtp(info['email'], subject, html)
                            try:
                                append_system_log('admin', '工单/发送回复邮件', f'用户{info["username"]} 工单{info["ticket_number"]} 已入队')
                            except Exception:
                                pass
                            # 若是非每条提醒模式，标记已提醒
                            if not admin_reply_every:
                                try:
                                    cursor.execute('UPDATE tickets SET user_notified_flag = 1 WHERE id = ?', (ticket_id,))
                                    db.conn.commit()
                                except Exception:
                                    pass
                        except Exception as e_send:
                            logger.warn('发送工单回复邮件失败', e_send)
            except Exception as e_notify:
                logger.warn('处理工单回复邮件通知时异常', e_notify)

            return jsonify({
                'success': True,
                'message': '回复成功'
            })
            
        except Exception as e:
            logger.error('管理员回复工单时出错:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/admin/tickets/<int:ticket_id>/status', methods=['POST'])
    @authenticate_admin
    def admin_update_ticket_status(ticket_id):
        """更新工单状态"""
        data = request.json
        new_status = data.get('status', '').strip()
        change_reason = data.get('changeReason', '').strip()
        
        if not new_status:
            return jsonify({'success': False, 'message': '新状态不能为空'}), 400
        
        valid_statuses = ['open', 'in_progress', 'waiting', 'closed', 'reopened']
        if new_status not in valid_statuses:
            return jsonify({'success': False, 'message': '无效的状态值'}), 400
        
        try:
            cursor = db.conn.cursor()
            
            # 获取当前状态
            cursor.execute('SELECT status FROM tickets WHERE id = ?', (ticket_id,))
            ticket = cursor.fetchone()
            if not ticket:
                return jsonify({'success': False, 'message': '工单不存在'}), 404
            
            old_status = ticket['status']
            if old_status == new_status:
                return jsonify({'success': False, 'message': '状态未发生变化'}), 400
            
            # 更新状态
            update_data = [new_status, datetime.now().isoformat(), ticket_id]
            if new_status == 'closed':
                cursor.execute('''
                    UPDATE tickets SET status = ?, updated_time = ?, closed_time = ?, closed_by = ?
                    WHERE id = ?
                ''', [new_status, datetime.now().isoformat(), datetime.now().isoformat(), 1, ticket_id])
            else:
                cursor.execute('''
                    UPDATE tickets SET status = ?, updated_time = ?
                    WHERE id = ?
                ''', update_data)
            
            # 记录状态变更
            cursor.execute('''
                INSERT INTO ticket_status_logs (ticket_id, old_status, new_status, changed_by, change_reason)
                VALUES (?, ?, ?, ?, ?)
            ''', (ticket_id, old_status, new_status, 1, change_reason))
            
            db.conn.commit()
            
            # 记录工单状态变更日志
            status_names = {
                'open': '待处理', 'in_progress': '处理中', 'waiting': '等待回复',
                'closed': '已关闭', 'reopened': '重新打开'
            }
            old_name = status_names.get(old_status, old_status)
            new_name = status_names.get(new_status, new_status)
            append_system_log('admin', '工单管理/状态变更', f'工单ID:{ticket_id} 状态{old_name}->{new_name} 原因:{change_reason}')
            
            return jsonify({
                'success': True,
                'message': '状态更新成功'
            })
            
        except Exception as e:
            logger.error('更新工单状态时出错:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/admin/tickets/<int:ticket_id>/assign', methods=['POST'])
    @authenticate_admin
    def admin_assign_ticket(ticket_id):
        """分配工单给管理员"""
        data = request.json
        admin_id = data.get('adminId')
        
        try:
            cursor = db.conn.cursor()
            
            # 检查工单是否存在
            cursor.execute('SELECT * FROM tickets WHERE id = ?', (ticket_id,))
            if not cursor.fetchone():
                return jsonify({'success': False, 'message': '工单不存在'}), 404
            
            # 更新分配的管理员
            cursor.execute('''
                UPDATE tickets SET assigned_admin_id = ?, updated_time = ?
                WHERE id = ?
            ''', (admin_id, datetime.now().isoformat(), ticket_id))
            
            db.conn.commit()
            
            append_system_log('admin', '工单管理/分配工单', f'工单ID:{ticket_id} 分配给管理员ID:{admin_id}')
            
            return jsonify({
                'success': True,
                'message': '工单分配成功'
            })
            
        except Exception as e:
            logger.error('分配工单时出错:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/admin/tickets/statistics', methods=['GET'])
    @authenticate_admin
    def admin_get_ticket_statistics():
        """获取工单统计信息"""
        try:
            cursor = db.conn.cursor()
            
            # 总工单数
            cursor.execute('SELECT COUNT(*) as total FROM tickets')
            total_tickets = cursor.fetchone()['total']
            
            # 各状态工单数
            cursor.execute('''
                SELECT status, COUNT(*) as count 
                FROM tickets 
                GROUP BY status
            ''')
            status_counts = {}
            for row in cursor.fetchall():
                status_counts[row['status']] = row['count']
            
            # 各类型工单数
            cursor.execute('''
                SELECT tt.name, COUNT(*) as count 
                FROM tickets t
                JOIN ticket_types tt ON t.ticket_type_id = tt.id
                GROUP BY t.ticket_type_id
            ''')
            type_counts = {}
            for row in cursor.fetchall():
                type_counts[row['name']] = row['count']
            
            # 今日新增工单数
            cursor.execute('''
                SELECT COUNT(*) as count 
                FROM tickets 
                WHERE DATE(created_time) = DATE('now')
            ''')
            today_tickets = cursor.fetchone()['count']
            
            # 本周新增工单数
            cursor.execute('''
                SELECT COUNT(*) as count 
                FROM tickets 
                WHERE DATE(created_time) >= DATE('now', 'weekday 0', '-6 days')
            ''')
            week_tickets = cursor.fetchone()['count']
            
            return jsonify({
                'success': True,
                'data': {
                    'totalTickets': total_tickets,
                    'statusCounts': status_counts,
                    'typeCounts': type_counts,
                    'todayTickets': today_tickets,
                    'weekTickets': week_tickets
                }
            })
            
        except Exception as e:
            logger.error('获取工单统计信息时出错:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    # 文件上传相关路由
    @app.route('/admin/upload', methods=['POST'])
    @authenticate_admin
    def admin_upload_file():
        """管理员上传文件"""
        try:
            if 'file' not in request.files:
                return jsonify({'success': False, 'message': '没有选择文件'}), 400
            
            file = request.files['file']
            if file.filename == '':
                return jsonify({'success': False, 'message': '没有选择文件'}), 400
            
            if not allowed_file(file.filename):
                return jsonify({'success': False, 'message': '不支持的文件类型'}), 400
            
            # 检查文件大小
            file.seek(0, 2)  # 移动到文件末尾
            file_size = file.tell()
            file.seek(0)  # 重置到文件开头
            
            if file_size > MAX_FILE_SIZE:
                return jsonify({'success': False, 'message': '文件大小超过限制（10MB）'}), 400
            
            # 生成安全的文件名
            import uuid
            file_extension = file.filename.rsplit('.', 1)[1].lower()
            safe_filename = f"{uuid.uuid4().hex}.{file_extension}"
            file_path = os.path.join(UPLOAD_FOLDER, safe_filename)
            
            # 保存文件
            file.save(file_path)
            
            return jsonify({
                'success': True,
                'message': '文件上传成功',
                'data': {
                    'fileName': file.filename,
                    'filePath': safe_filename,
                    'fileSize': file_size,
                    'fileType': file_extension
                }
            })
            
        except Exception as e:
            logger.error('文件上传时出错:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/admin/files/<filename>', methods=['GET'])
    @authenticate_admin
    def admin_download_file(filename):
        """管理员下载文件"""
        try:
            file_path = os.path.join(UPLOAD_FOLDER, filename)
            if not os.path.exists(file_path):
                return jsonify({'success': False, 'message': '文件不存在'}), 404
            
            return send_file(file_path, as_attachment=True)
            
        except Exception as e:
            logger.error('文件下载时出错:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/admin/files/<filename>', methods=['DELETE'])
    @authenticate_admin
    def admin_delete_file(filename):
        """管理员删除文件"""
        try:
            file_path = os.path.join(UPLOAD_FOLDER, filename)
            if not os.path.exists(file_path):
                return jsonify({'success': False, 'message': '文件不存在'}), 404
            
            os.remove(file_path)
            
            return jsonify({
                'success': True,
                'message': '文件删除成功'
            })
            
        except Exception as e:
            logger.error('文件删除时出错:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    # ==================== 按次付费功能管理接口 ====================

    @app.route('/admin/pay-per-use/features', methods=['GET'])
    @authenticate_admin
    def admin_get_pay_per_use_features():
        """获取按次付费功能列表"""
        try:
            cursor = db.conn.cursor()
            cursor.execute('''
                SELECT * FROM pay_per_use_features
                ORDER BY created_time DESC
            ''')
            features = cursor.fetchall()
            return jsonify({'success': True, 'data': [dict(row) for row in features]})
        except Exception as e:
            logger.error('获取按次付费功能列表失败:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/admin/pay-per-use/features/save', methods=['POST'])
    @authenticate_admin
    def admin_save_pay_per_use_feature():
        """保存按次付费功能"""
        try:
            data = request.get_json() or {}
            feature_id = data.get('id')
            feature_code = data.get('feature_code', '').strip()
            display_name = data.get('display_name', '').strip()
            description = data.get('description', '').strip()
            api_endpoint = data.get('api_endpoint', '').strip()
            is_active = data.get('is_active', True)

            if not feature_code or not display_name:
                return jsonify({'success': False, 'message': '功能代码和显示名称不能为空'}), 400

            cursor = db.conn.cursor()

            if feature_id:
                # 更新
                cursor.execute('''
                    UPDATE pay_per_use_features
                    SET feature_code = ?, display_name = ?, description = ?,
                        api_endpoint = ?, is_active = ?, updated_time = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (feature_code, display_name, description, api_endpoint, is_active, feature_id))
                action = '更新'
            else:
                # 新增
                cursor.execute('''
                    INSERT INTO pay_per_use_features (feature_code, display_name, description, api_endpoint, is_active)
                    VALUES (?, ?, ?, ?, ?)
                ''', (feature_code, display_name, description, api_endpoint, is_active))
                action = '新增'

            db.conn.commit()
            append_system_log('admin', '按次付费功能管理', f'{action}功能 {display_name}')
            return jsonify({'success': True, 'message': f'功能{action}成功'})
        except Exception as e:
            logger.error('保存按次付费功能失败:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/admin/pay-per-use/features/delete/<int:feature_id>', methods=['DELETE'])
    @authenticate_admin
    def admin_delete_pay_per_use_feature(feature_id):
        """删除按次付费功能"""
        try:
            cursor = db.conn.cursor()
            cursor.execute('SELECT display_name FROM pay_per_use_features WHERE id = ?', (feature_id,))
            feature = cursor.fetchone()
            if not feature:
                return jsonify({'success': False, 'message': '功能不存在'}), 404

            cursor.execute('DELETE FROM pay_per_use_features WHERE id = ?', (feature_id,))
            db.conn.commit()
            append_system_log('admin', '按次付费功能管理', f'删除功能 {feature["display_name"]}')
            return jsonify({'success': True, 'message': '功能删除成功'})
        except Exception as e:
            logger.error('删除按次付费功能失败:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/admin/pay-per-use/tiers', methods=['GET'])
    @authenticate_admin
    def admin_get_pay_per_use_tiers():
        """获取按次付费档位列表"""
        try:
            cursor = db.conn.cursor()
            cursor.execute('''
                SELECT * FROM pay_per_use_tiers
                ORDER BY created_time DESC
            ''')
            tiers = cursor.fetchall()
            return jsonify({'success': True, 'data': [dict(row) for row in tiers]})
        except Exception as e:
            logger.error('获取按次付费档位列表失败:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/admin/pay-per-use/tiers/save', methods=['POST'])
    @authenticate_admin
    def admin_save_pay_per_use_tier():
        """保存按次付费档位"""
        try:
            data = request.get_json() or {}
            tier_id = data.get('id')
            tier_code = data.get('tier_code', '').strip()
            display_name = data.get('display_name', '').strip()
            description = data.get('description', '').strip()
            price_per_use = data.get('price_per_use', 0)
            is_active = data.get('is_active', True)

            if not tier_code or not display_name:
                return jsonify({'success': False, 'message': '档位代码和显示名称不能为空'}), 400

            if price_per_use <= 0:
                return jsonify({'success': False, 'message': '单次价格必须大于0'}), 400

            cursor = db.conn.cursor()

            if tier_id:
                # 更新
                cursor.execute('''
                    UPDATE pay_per_use_tiers
                    SET tier_code = ?, display_name = ?, description = ?,
                        price_per_use = ?, is_active = ?, updated_time = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (tier_code, display_name, description, price_per_use, is_active, tier_id))
                action = '更新'
            else:
                # 新增
                cursor.execute('''
                    INSERT INTO pay_per_use_tiers (tier_code, display_name, description, price_per_use, is_active)
                    VALUES (?, ?, ?, ?, ?)
                ''', (tier_code, display_name, description, price_per_use, is_active))
                action = '新增'

            db.conn.commit()
            append_system_log('admin', '按次付费档位管理', f'{action}档位 {display_name}')
            return jsonify({'success': True, 'message': f'档位{action}成功'})
        except Exception as e:
            logger.error('保存按次付费档位失败:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/admin/pay-per-use/tiers/delete/<int:tier_id>', methods=['DELETE'])
    @authenticate_admin
    def admin_delete_pay_per_use_tier(tier_id):
        """删除按次付费档位"""
        try:
            cursor = db.conn.cursor()
            cursor.execute('SELECT display_name FROM pay_per_use_tiers WHERE id = ?', (tier_id,))
            tier = cursor.fetchone()
            if not tier:
                return jsonify({'success': False, 'message': '档位不存在'}), 404

            cursor.execute('DELETE FROM pay_per_use_tiers WHERE id = ?', (tier_id,))
            db.conn.commit()
            append_system_log('admin', '按次付费档位管理', f'删除档位 {tier["display_name"]}')
            return jsonify({'success': True, 'message': '档位删除成功'})
        except Exception as e:
            logger.error('删除按次付费档位失败:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/admin/pay-per-use/codes', methods=['GET'])
    @authenticate_admin
    def admin_get_pay_per_use_codes():
        """获取按次付费兑换码列表"""
        try:
            page = int(request.args.get('page', 1))
            per_page = int(request.args.get('per_page', 20))
            offset = (page - 1) * per_page

            cursor = db.conn.cursor()

            # 获取总数
            cursor.execute('SELECT COUNT(*) as total FROM pay_per_use_codes')
            total = cursor.fetchone()['total']

            # 获取分页数据
            cursor.execute('''
                SELECT ppc.*
                FROM pay_per_use_codes ppc
                ORDER BY ppc.created_time DESC
                LIMIT ? OFFSET ?
            ''', (per_page, offset))

            codes = cursor.fetchall()

            # 处理兑换码数据，解析功能配置
            processed_codes = []
            for code in codes:
                # 将 sqlite3.Row 对象转换为字典
                code_dict = dict(code)

                if code_dict.get('feature_config'):
                    try:
                        import json
                        config = json.loads(code_dict['feature_config'])
                        feature_names = []
                        for feature_id, count in config.items():
                            cursor.execute('SELECT display_name FROM pay_per_use_features WHERE id = ?', (feature_id,))
                            feature = cursor.fetchone()
                            if feature:
                                feature_names.append(f"{dict(feature)['display_name']}:{count}次")
                        code_dict['feature_info'] = ', '.join(feature_names)
                    except:
                        code_dict['feature_info'] = code_dict['feature_config']
                else:
                    code_dict['feature_info'] = ''

                processed_codes.append(code_dict)

            return jsonify({
                'success': True,
                'data': processed_codes,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': total,
                    'pages': (total + per_page - 1) // per_page
                }
            })
        except Exception as e:
            logger.error('获取按次付费兑换码列表失败:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/admin/pay-per-use/codes/generate', methods=['POST'])
    @authenticate_admin
    def admin_generate_pay_per_use_codes():
        """生成按次付费兑换码"""
        try:
            data = request.get_json() or {}
            logger.info(f'收到生成兑换码请求，数据: {data}')  # 调试信息

            feature_config_str = data.get('feature_config', '{}')
            count = data.get('count', 1)
            batch_id = data.get('batch_id', '').strip()
            note = data.get('note', '').strip()
            expires_at = data.get('expires_at')

            logger.info(f'解析参数: feature_config_str={feature_config_str}, count={count}')  # 调试信息

            if count <= 0:
                return jsonify({'success': False, 'message': '生成数量必须大于0'}), 400

            if count > 1000:
                return jsonify({'success': False, 'message': '单次生成数量不能超过1000个'}), 400

            # 解析功能配置
            try:
                feature_config = json.loads(feature_config_str)
                logger.info(f'解析功能配置成功: {feature_config}')  # 调试信息
            except Exception as e:
                logger.error(f'解析功能配置失败: {e}')  # 调试信息
                return jsonify({'success': False, 'message': '功能配置格式错误'}), 400

            if not feature_config:
                return jsonify({'success': False, 'message': '请至少选择一个功能'}), 400

            cursor = db.conn.cursor()

            # 检查所有功能是否存在
            for feature_id in feature_config.keys():
                cursor.execute('SELECT id, feature_code, display_name FROM pay_per_use_features WHERE id = ? AND is_active = 1', (feature_id,))
                feature = cursor.fetchone()
                if not feature:
                    return jsonify({'success': False, 'message': f'功能ID {feature_id}不存在或已禁用'}), 400

            generated_codes = []
            for _ in range(count):
                code = generate_activation_code(12)  # 生成12位兑换码
                cursor.execute('''
                    INSERT INTO pay_per_use_codes (code, feature_config, usage_count, batch_id, note, expires_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (code, feature_config_str, 1, batch_id, note, expires_at))
                generated_codes.append(code)

            db.conn.commit()
            append_system_log('admin', '按次付费兑换码管理', f'生成{count}个兑换码，批次ID: {batch_id}')
            return jsonify({
                'success': True,
                'message': f'成功生成{count}个兑换码',
                'data': generated_codes
            })
        except Exception as e:
            logger.error('生成按次付费兑换码失败:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/admin/pay-per-use/codes/delete/<int:code_id>', methods=['DELETE'])
    @authenticate_admin
    def admin_delete_pay_per_use_code(code_id):
        """删除按次付费兑换码"""
        try:
            cursor = db.conn.cursor()
            cursor.execute('SELECT code FROM pay_per_use_codes WHERE id = ?', (code_id,))
            code_row = cursor.fetchone()
            if not code_row:
                return jsonify({'success': False, 'message': '兑换码不存在'}), 404

            cursor.execute('DELETE FROM pay_per_use_codes WHERE id = ?', (code_id,))
            db.conn.commit()
            append_system_log('admin', '按次付费兑换码管理', f'删除兑换码 {code_row["code"]}')
            return jsonify({'success': True, 'message': '兑换码删除成功'})
        except Exception as e:
            logger.error('删除按次付费兑换码失败:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/admin/pay-per-use/billing', methods=['GET'])
    @authenticate_admin
    def admin_get_pay_per_use_billing():
        """获取按次付费计费账户列表"""
        try:
            page = int(request.args.get('page', 1))
            per_page = int(request.args.get('per_page', 20))
            offset = (page - 1) * per_page
            group_filter = request.args.get('group_number', '').strip()

            cursor = db.conn.cursor()

            # 构建查询条件
            where_clause = ""
            params = []
            if group_filter:
                where_clause = "WHERE ppb.group_number LIKE ?"
                params.append(f'%{group_filter}%')

            # 获取总数
            cursor.execute(f'SELECT COUNT(*) as total FROM pay_per_use_billing ppb {where_clause}', params)
            total = cursor.fetchone()['total']

            # 获取分页数据
            cursor.execute(f'''
                SELECT ppb.*, ppf.display_name as feature_name
                FROM pay_per_use_billing ppb
                LEFT JOIN pay_per_use_features ppf ON ppb.feature_id = ppf.id
                {where_clause}
                ORDER BY ppb.updated_time DESC
                LIMIT ? OFFSET ?
            ''', params + [per_page, offset])

            billing_accounts = cursor.fetchall()

            return jsonify({
                'success': True,
                'data': [dict(row) for row in billing_accounts],
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': total,
                    'pages': (total + per_page - 1) // per_page
                }
            })
        except Exception as e:
            logger.error('获取按次付费计费账户列表失败:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/admin/pay-per-use/billing/adjust', methods=['POST'])
    @authenticate_admin
    def admin_adjust_pay_per_use_billing():
        """调整按次付费计费账户余额"""
        try:
            data = request.get_json() or {}
            account_id = data.get('account_id')
            adjustment = data.get('adjustment', 0)
            reason = data.get('reason', '').strip()

            if not account_id:
                return jsonify({'success': False, 'message': '账户ID不能为空'}), 400

            if adjustment == 0:
                return jsonify({'success': False, 'message': '调整数量不能为0'}), 400

            cursor = db.conn.cursor()

            # 获取账户信息
            cursor.execute('''
                SELECT * FROM pay_per_use_billing
                WHERE id = ?
            ''', (account_id,))
            account = cursor.fetchone()

            if not account:
                return jsonify({'success': False, 'message': '账户不存在'}), 404

            new_remaining = account['remaining_count'] + adjustment
            if new_remaining < 0:
                return jsonify({'success': False, 'message': '调整后余额不能为负数'}), 400

            cursor.execute('''
                UPDATE pay_per_use_billing
                SET remaining_count = ?, updated_time = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (new_remaining, account_id))

            db.conn.commit()
            append_system_log('admin', '按次付费计费管理', f'调整群聊{account["group_number"]}余额 {adjustment} 次，原因: {reason}')
            return jsonify({'success': True, 'message': '余额调整成功'})
        except Exception as e:
            logger.error('调整按次付费计费账户失败:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/admin/pay-per-use/logs', methods=['GET'])
    @authenticate_admin
    def admin_get_pay_per_use_logs():
        """获取按次付费使用日志"""
        try:
            page = int(request.args.get('page', 1))
            per_page = int(request.args.get('per_page', 20))
            offset = (page - 1) * per_page
            group_filter = request.args.get('group_number', '').strip()
            feature_filter = request.args.get('feature_id')
            date_from = request.args.get('date_from', '').strip()
            date_to = request.args.get('date_to', '').strip()

            cursor = db.conn.cursor()

            # 构建查询条件
            where_clause = ""
            params = []

            conditions = []
            if group_filter:
                conditions.append("ppl.group_number LIKE ?")
                params.append(f'%{group_filter}%')
            if feature_filter:
                conditions.append("ppl.feature_id = ?")
                params.append(feature_filter)
            if date_from:
                conditions.append("ppl.request_time >= ?")
                params.append(date_from)
            if date_to:
                conditions.append("ppl.request_time <= ?")
                params.append(date_to + ' 23:59:59')

            if conditions:
                where_clause = "WHERE " + " AND ".join(conditions)

            # 获取总数
            cursor.execute(f'SELECT COUNT(*) as total FROM pay_per_use_logs ppl {where_clause}', params)
            total = cursor.fetchone()['total']

            # 获取分页数据
            cursor.execute(f'''
                SELECT ppl.*, ppf.display_name as feature_name
                FROM pay_per_use_logs ppl
                LEFT JOIN pay_per_use_features ppf ON ppl.feature_id = ppf.id
                {where_clause}
                ORDER BY ppl.request_time DESC
                LIMIT {per_page} OFFSET {offset}
            ''', params)

            logs = cursor.fetchall()

            return jsonify({
                'success': True,
                'data': [dict(row) for row in logs],
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': total,
                    'pages': (total + per_page - 1) // per_page
                }
            })
        except Exception as e:
            logger.error('获取按次付费使用日志失败:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    # ==================== 机器人群聊自动刷新功能 ====================
    
    def _refresh_all_robot_groups_task():
        """自动刷新所有机器人群聊的任务函数"""
        try:
            cursor = db.conn.cursor()
            
            cursor.execute('SELECT * FROM robots WHERE is_active = 1')
            robots = cursor.fetchall()
            
            if not robots:
                logger.info('定期刷新：没有可用的机器人')
                return
            
            total_groups = 0
            failed_robots = []
            success_robots = []
            
            # 不清空所有群聊数据，而是逐个机器人更新
            import requests
            
            for robot in robots:
                robot_id = robot['id']
                api_url = robot['api_url']
                api_token = robot['api_token']
                bot_account = robot['bot_account']
                
                headers = {}
                if api_token:
                    headers['Authorization'] = f'Bearer {api_token}'
                
                try:
                    full_url = api_url + 'get_group_list'
                    
                    response = requests.post(
                        full_url,
                        json={'next_token': ''},
                        headers=headers,
                        timeout=10
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        
                        if result.get('retcode') == 0:
                            groups = result.get('data', [])
                            
                            # 先删除该机器人的现有群聊记录
                            cursor.execute('DELETE FROM robot_groups WHERE robot_id = ?', (robot_id,))
                            
                            # 插入新的群聊数据
                            for group in groups:
                                cursor.execute('''
                                    INSERT INTO robot_groups 
                                    (robot_id, group_id, group_name, member_count, max_member_count, group_remark, last_update)
                                    VALUES (?, ?, ?, ?, ?, ?, ?)
                                ''', (
                                    robot_id,
                                    str(group.get('group_id', '')),
                                    group.get('group_name', '未知群名'),
                                    group.get('member_count', 0),
                                    group.get('max_member_count', 0),
                                    group.get('group_remark', ''),
                                    datetime.now().isoformat()
                                ))
                            
                            success_robots.append({
                                'botAccount': bot_account,
                                'groupCount': len(groups)
                            })
                            total_groups += len(groups)
                            
                        else:
                            failed_robots.append({
                                'botAccount': bot_account,
                                'error': result.get('message', '未知错误')
                            })
                    else:
                        failed_robots.append({
                            'botAccount': bot_account,
                            'error': f'状态码: {response.status_code}'
                        })
                        
                except requests.exceptions.RequestException as e:
                    failed_robots.append({
                        'botAccount': bot_account,
                        'error': str(e)
                    })
            
            db.conn.commit()
            
            # 记录定期刷新日志
            append_system_log('auto', '机器人管理/定期刷新群聊', f'成功机器人{len(success_robots)}个 失败机器人{len(failed_robots)}个 总计群聊{total_groups}个')
            logger.info(f'定期刷新机器人群聊完成: 成功{len(success_robots)}个机器人，失败{len(failed_robots)}个机器人，总计{total_groups}个群聊')
            
        except Exception as e:
            logger.error('定期刷新机器人群聊时出错:', e)
            append_system_log('auto', '机器人管理/定期刷新群聊', f'执行失败: {str(e)}')

    # 启动机器人群聊定期刷新任务
    # QQ群头像代理API (公开访问，用于img标签)
    @app.route('/api/group-avatar/<group_id>', methods=['GET'])
    def get_group_avatar(group_id):
        """获取QQ群头像的代理API"""
        try:
            import requests
            from flask import Response
            import io

            # 验证群号格式
            if not group_id.isdigit():
                return jsonify({'success': False, 'message': '无效的群号'}), 400

            # 尝试多个QQ群头像API
            avatar_urls = [
                f'https://p.qlogo.cn/gh/{group_id}/{group_id}/100',
                f'https://q1.qlogo.cn/g?b=qq&nk={group_id}&s=100',
                f'https://q2.qlogo.cn/headimg_dl?dst_uin={group_id}&spec=100',
                f'https://qlogo.cn/headimg_dl?dst_uin={group_id}&spec=100'
            ]

            for url in avatar_urls:
                try:
                    response = requests.get(url, timeout=10, headers={
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    })

                    if response.status_code == 200 and len(response.content) > 1000:  # 确保不是空图片
                        # 返回图片数据
                        return Response(
                            response.content,
                            mimetype=response.headers.get('content-type', 'image/jpeg'),
                            headers={
                                'Cache-Control': 'public, max-age=3600',  # 缓存1小时
                                'Access-Control-Allow-Origin': '*'
                            }
                        )
                except Exception as e:
                    logger.debug(f'尝试获取群头像失败 {url}: {e}')
                    continue

            # 如果所有API都失败，返回默认头像SVG
            default_avatar = '''<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
                <rect width="100" height="100" fill="#e0e0e0"/>
                <circle cx="50" cy="35" r="15" fill="#999"/>
                <path d="M20 80 Q20 65 35 65 L65 65 Q80 65 80 80 L80 90 L20 90 Z" fill="#999"/>
            </svg>'''

            return Response(
                default_avatar,
                mimetype='image/svg+xml',
                headers={
                    'Cache-Control': 'public, max-age=300',  # 默认头像缓存5分钟
                    'Access-Control-Allow-Origin': '*'
                }
            )

        except Exception as e:
            logger.error(f'获取群头像时出错: {e}')
            # 返回错误占位符
            error_avatar = '''<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
                <rect width="100" height="100" fill="#f0f0f0"/>
                <text x="50" y="50" text-anchor="middle" dy=".3em" fill="#666" font-family="sans-serif" font-size="40">?</text>
            </svg>'''
            return Response(error_avatar, mimetype='image/svg+xml')

    try:
        import threading, time as _time
        if not getattr(app, '_robot_groups_refresh_scheduler_started', False):
            def _robot_groups_refresh_scheduler_loop():
                while True:
                    try:
                        # 检查是否启用了自动刷新
                        enabled = db.get_setting('robot_groups_auto_refresh_enabled', '1')
                        if enabled != '1':
                            _time.sleep(60)  # 如果禁用，每分钟检查一次设置
                            continue

                        # 获取刷新间隔（分钟）
                        interval_str = db.get_setting('robot_groups_refresh_interval_minutes', '60')
                        try:
                            interval_minutes = max(5, int(interval_str))  # 最小5分钟
                        except:
                            interval_minutes = 60

                        interval_seconds = interval_minutes * 60

                        # 执行刷新任务
                        _refresh_all_robot_groups_task()

                        # 等待下次执行
                        _time.sleep(interval_seconds)

                    except Exception as e:
                        logger.warn('机器人群聊定期刷新任务执行异常', e)
                        # 避免热循环，异常时等待5分钟
                        _time.sleep(300)

            t = threading.Thread(target=_robot_groups_refresh_scheduler_loop, name='robot-groups-refresh-scheduler', daemon=True)
            t.start()
            setattr(app, '_robot_groups_refresh_scheduler_started', True)
            logger.info('机器人群聊定期刷新任务已启动')
    except Exception as e:
        logger.warn('启动机器人群聊定期刷新任务失败', e)

    # 背景图片API
    @app.route('/api/background')
    def get_background():
        """获取随机背景图片"""
        try:
            import requests
            # 使用指定的API
            api_url = "https://t.alcy.cc/ycy"

            # 获取图片，设置较短的超时时间
            response = requests.get(api_url, timeout=3, allow_redirects=True)
            if response.status_code == 200:
                # 直接返回图片URL
                return jsonify({
                    'success': True,
                    'url': api_url,
                    'source': 'alcy.cc'
                })

            # 如果API失败，返回默认背景
            return jsonify({
                'success': True,
                'url': 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=1920&h=1080&fit=crop',
                'source': 'default'
            })

        except Exception as e:
            logger.error(f'获取背景图片失败: {e}')
            # 返回默认背景
            return jsonify({
                'success': True,
                'url': 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=1920&h=1080&fit=crop',
                'source': 'default'
            })