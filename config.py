import os
import sqlite3
import secrets
import string
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# --- Configuration ---
PORT = int(os.getenv('PORT', 56228))
ADMIN_PASSWORD = os.getenv('ADMIN_PASSWORD', 'a2678845')
JWT_SECRET = os.getenv('JWT_SECRET', 'your-secret-key')
DB_PATH = os.getenv('DB_PATH', './database.sqlite')

if not all([ADMIN_PASSWORD]):
    print('致命错误: 缺少关键配置 (ADMIN_PASSWORD)。请检查您的 .env 文件或环境变量。')
    exit(1)

# SKU Types
SKUID_10 = '6b9d46742cc311f094855254001e7c00'
SKUID_15 = '590a339e2cbb11f0983452540025c377'

# Logger utility
class Logger:
    @staticmethod
    def info(message, meta=None):
        print(f"[INFO] {message}", meta or '')
    
    @staticmethod
    def warn(message, meta=None):
        print(f"[WARN] {message}", meta or '')
    
    @staticmethod
    def error(message, meta=None):
        if isinstance(meta, Exception):
            print(f"[ERROR] {message}", str(meta))
        else:
            print(f"[ERROR] {message}", meta or '')

logger = Logger()

# Time pool calculation functions
def calculate_group_time_pool(db_conn, group_number, sku_id):
    """
    计算群组的时间池状态 - 正确的顺序消耗逻辑

    时间池逻辑：
    1. 按绑定时间排序，先绑定的激活码先消耗
    2. 只有最早的未过期激活码在消耗时间
    3. 其他激活码保持原始有效期，等待轮到它们
    4. 总剩余时间 = 当前消耗中的激活码剩余时间 + 其他激活码的完整有效期

    Args:
        db_conn: 数据库连接
        group_number: 群号
        sku_id: SKU ID

    Returns:
        dict: 时间池状态信息
    """
    from datetime import datetime, timedelta

    cursor = db_conn.cursor()

    # 获取该群组该SKU的所有激活码，按绑定时间排序
    cursor.execute('''
        SELECT activation_code, expiration_time, owner_username, bind_time
        FROM bindings
        WHERE group_number = ? AND sku_id = ?
        ORDER BY bind_time ASC
    ''', (group_number, sku_id))

    all_codes = cursor.fetchall()

    if not all_codes:
        return {
            'total_remaining_days': 0,
            'total_remaining_seconds': 0,
            'current_expiration': None,
            'final_expiration': None,
            'active_codes': [],
            'expired_codes': [],
            'is_expired': True
        }

    now = datetime.now()
    active_codes = []
    expired_codes = []
    current_consuming_code = None
    waiting_codes = []

    # 处理每个激活码
    for code in all_codes:
        if not code['expiration_time']:
            # 永久激活码 - 如果是第一个未过期的，就是当前消耗中的
            code_info = {
                'activation_code': code['activation_code'],
                'expiration_time': None,
                'owner_username': code['owner_username'],
                'bind_time': code['bind_time'],
                'remaining_seconds': float('inf'),
                'is_permanent': True,
                'status': 'consuming' if not current_consuming_code else 'waiting'
            }
            active_codes.append(code_info)

            if not current_consuming_code:
                current_consuming_code = code_info
            else:
                waiting_codes.append(code_info)
            continue

        # 处理带时区信息的日期格式
        try:
            exp_str = code['expiration_time']
            if exp_str.endswith('Z'):
                exp_str = exp_str[:-1]
            elif exp_str.endswith('.000Z'):
                exp_str = exp_str[:-5]
            expiry_time = datetime.fromisoformat(exp_str)
        except Exception:
            try:
                expiry_time = datetime.strptime(code['expiration_time'].split('.')[0], '%Y-%m-%dT%H:%M:%S') if 'T' in code['expiration_time'] else datetime.fromisoformat(code['expiration_time'])
            except Exception:
                continue  # 跳过无法解析的日期

        # 计算激活码的原始有效期（从绑定时间到到期时间）
        try:
            bind_time_str = code['bind_time']
            if bind_time_str.endswith('Z'):
                bind_time_str = bind_time_str[:-1]
            elif bind_time_str.endswith('.000Z'):
                bind_time_str = bind_time_str[:-5]

            if 'T' in bind_time_str:
                bind_time = datetime.fromisoformat(bind_time_str)
            else:
                bind_time = datetime.strptime(bind_time_str, '%Y-%m-%d %H:%M:%S')
        except Exception:
            bind_time = now  # 默认值

        original_duration_seconds = (expiry_time - bind_time).total_seconds()

        if expiry_time > now:
            # 未过期的激活码
            if not current_consuming_code:
                # 这是第一个未过期的激活码，正在消耗中
                remaining_seconds = (expiry_time - now).total_seconds()
                code_info = {
                    'activation_code': code['activation_code'],
                    'expiration_time': code['expiration_time'],
                    'owner_username': code['owner_username'],
                    'bind_time': code['bind_time'],
                    'remaining_seconds': remaining_seconds,
                    'original_duration_seconds': original_duration_seconds,
                    'is_permanent': False,
                    'status': 'consuming'
                }
                current_consuming_code = code_info
            else:
                # 这是等待中的激活码，保持完整有效期
                code_info = {
                    'activation_code': code['activation_code'],
                    'expiration_time': code['expiration_time'],
                    'owner_username': code['owner_username'],
                    'bind_time': code['bind_time'],
                    'remaining_seconds': original_duration_seconds,  # 使用完整有效期
                    'original_duration_seconds': original_duration_seconds,
                    'is_permanent': False,
                    'status': 'waiting'
                }
                waiting_codes.append(code_info)

            active_codes.append(code_info)
        else:
            # 已过期的激活码
            expired_codes.append({
                'activation_code': code['activation_code'],
                'expiration_time': code['expiration_time'],
                'owner_username': code['owner_username'],
                'bind_time': code['bind_time'],
                'expired_seconds': abs((expiry_time - now).total_seconds()),
                'original_duration_seconds': original_duration_seconds
            })

    # 计算总剩余时间
    total_remaining_seconds = 0
    if current_consuming_code:
        if current_consuming_code['is_permanent']:
            total_remaining_seconds = float('inf')
        else:
            total_remaining_seconds = current_consuming_code['remaining_seconds']
            # 加上所有等待中激活码的完整有效期
            for waiting_code in waiting_codes:
                if waiting_code['is_permanent']:
                    total_remaining_seconds = float('inf')
                    break
                else:
                    total_remaining_seconds += waiting_code['remaining_seconds']

    # 计算最终到期时间
    final_expiration = None
    if total_remaining_seconds == float('inf'):
        final_expiration = None  # 永久
    elif total_remaining_seconds > 0:
        final_expiration = (now + timedelta(seconds=total_remaining_seconds)).isoformat()

    # 计算总剩余天数
    total_remaining_days = 0 if total_remaining_seconds == 0 else (
        float('inf') if total_remaining_seconds == float('inf') else int(total_remaining_seconds / 86400)
    )

    # 当前有效期是正在消耗的激活码的到期时间
    current_expiration = current_consuming_code['expiration_time'] if current_consuming_code else None

    return {
        'total_remaining_days': total_remaining_days,
        'total_remaining_seconds': total_remaining_seconds,
        'current_expiration': current_expiration,
        'final_expiration': final_expiration,
        'active_codes': active_codes,
        'expired_codes': expired_codes,
        'current_consuming_code': current_consuming_code,
        'waiting_codes': waiting_codes,
        'is_expired': len(active_codes) == 0
    }

def get_group_all_time_pools(db_conn, group_number):
    """
    获取群组所有SKU的时间池状态

    Args:
        db_conn: 数据库连接
        group_number: 群号

    Returns:
        dict: {sku_id: time_pool_info}
    """
    cursor = db_conn.cursor()

    # 获取该群组的所有SKU
    cursor.execute('''
        SELECT DISTINCT sku_id
        FROM bindings
        WHERE group_number = ?
    ''', (group_number,))

    sku_ids = [row['sku_id'] for row in cursor.fetchall()]

    result = {}
    for sku_id in sku_ids:
        time_pool = calculate_group_time_pool(db_conn, group_number, sku_id)
        if not time_pool['is_expired']:  # 只返回未过期的SKU
            result[sku_id] = time_pool

    return result

# Database connection manager
class Database:
    def __init__(self, db_path):
        self.db_path = db_path
        self.conn = None
        
    def connect(self):
        try:
            self.conn = sqlite3.connect(self.db_path, check_same_thread=False)
            self.conn.row_factory = sqlite3.Row
            logger.info('SQLite数据库连接已初始化')
            return True
        except Exception as e:
            logger.error('初始化数据库失败:', e)
            return False
    
    def create_tables(self):
        try:
            cursor = self.conn.cursor()
            
            # Users table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT NOT NULL UNIQUE,
                    email TEXT NOT NULL UNIQUE,
                    password TEXT NOT NULL,
                    qq TEXT NOT NULL UNIQUE,
                    register_time TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    is_banned INTEGER DEFAULT 0,
                    ban_reason TEXT,
                    ban_time TEXT
                )
            ''')
            cursor.execute('''
                           PRAGMA table_info(users)
            ''')
            columns = [column[1] for column in cursor.fetchall()]
            if 'is_banned' not in columns:
                cursor.execute('ALTER TABLE users ADD COLUMN is_banned INTEGER DEFAULT 0')
            if 'ban_reason' not in columns:
                cursor.execute('ALTER TABLE users ADD COLUMN ban_reason TEXT')
            if 'ban_time' not in columns:
                cursor.execute('ALTER TABLE users ADD COLUMN ban_time TEXT')
            # Bindings table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS bindings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    activation_code TEXT NOT NULL UNIQUE,
                    sku_id TEXT NOT NULL,
                    group_number TEXT NOT NULL,
                    owner_username TEXT NOT NULL,
                    bind_time TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    expiration_time TEXT,
                    UNIQUE (activation_code, group_number)
                )
            ''')
            
            # Activation codes table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS activation_codes (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    code TEXT NOT NULL UNIQUE,
                    sku_id TEXT NOT NULL,
                    duration_months INTEGER NOT NULL,
                    is_used BOOLEAN DEFAULT 0,
                    used_by TEXT,
                    used_time TEXT,
                    created_time TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    batch_id TEXT,
                    note TEXT
                )
            ''')
            
            # 机器人配置表   
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS robots (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                bot_account TEXT NOT NULL UNIQUE,
                bot_name TEXT NOT NULL,
                api_url TEXT NOT NULL,
                api_token TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_time TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_time TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 群聊信息表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS robot_groups (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                robot_id INTEGER NOT NULL,
                group_id TEXT NOT NULL,
                group_name TEXT NOT NULL,
                member_count INTEGER DEFAULT 0,
                max_member_count INTEGER DEFAULT 0,
                group_remark TEXT,
                last_update TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (robot_id) REFERENCES robots(id) ON DELETE CASCADE,
                UNIQUE(robot_id, group_id)
                )
            ''')
            
            # 功能黑名单定义表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS feature_blacklist_definitions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    display_name TEXT NOT NULL,
                    actual_features TEXT NOT NULL,
                    request_identifiers TEXT NOT NULL,
                    allowed_tiers TEXT NOT NULL,
                    created_time TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_time TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 群组功能黑名单表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS group_feature_blacklist (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    group_number TEXT NOT NULL,
                    feature_id INTEGER NOT NULL,
                    created_time TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (feature_id) REFERENCES feature_blacklist_definitions(id) ON DELETE CASCADE,
                    UNIQUE(group_number, feature_id)
                )
            ''')
            
            # 功能白名单定义表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS feature_whitelist_definitions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    display_name TEXT NOT NULL,
                    actual_features TEXT NOT NULL,
                    request_identifiers TEXT NOT NULL,
                    allowed_tiers TEXT NOT NULL,
                    created_time TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_time TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 群组功能白名单表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS group_feature_whitelist (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    group_number TEXT NOT NULL,
                    feature_id INTEGER NOT NULL,
                    created_time TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (feature_id) REFERENCES feature_whitelist_definitions(id) ON DELETE CASCADE,
                    UNIQUE(group_number, feature_id)
                )
            ''')
            
            # 工单类型表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS ticket_types (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL UNIQUE,
                    description TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    sort_order INTEGER DEFAULT 0,
                    created_time TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_time TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 工单表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS tickets (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ticket_number TEXT NOT NULL UNIQUE,
                    title TEXT NOT NULL,
                    content TEXT NOT NULL,
                    ticket_type_id INTEGER NOT NULL,
                    status TEXT NOT NULL DEFAULT 'open',
                    priority TEXT NOT NULL DEFAULT 'normal',
                    user_id INTEGER NOT NULL,
                    group_number TEXT,
                    assigned_admin_id INTEGER,
                    created_time TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_time TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    closed_time TEXT,
                    closed_by INTEGER,
                    FOREIGN KEY (ticket_type_id) REFERENCES ticket_types(id),
                    FOREIGN KEY (user_id) REFERENCES users(id),
                    FOREIGN KEY (assigned_admin_id) REFERENCES users(id)
                )
            ''')
            
            # 工单回复表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS ticket_replies (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ticket_id INTEGER NOT NULL,
                    user_id INTEGER NOT NULL,
                    content TEXT NOT NULL,
                    is_admin BOOLEAN DEFAULT 0,
                    created_time TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (ticket_id) REFERENCES tickets(id) ON DELETE CASCADE,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                )
            ''')
            
            # 工单附件表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS ticket_attachments (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ticket_id INTEGER,
                    reply_id INTEGER,
                    file_name TEXT NOT NULL,
                    file_path TEXT NOT NULL,
                    file_size INTEGER NOT NULL,
                    file_type TEXT NOT NULL,
                    upload_time TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (ticket_id) REFERENCES tickets(id) ON DELETE CASCADE,
                    FOREIGN KEY (reply_id) REFERENCES ticket_replies(id) ON DELETE CASCADE
                )
            ''')
            
            # 工单状态变更记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS ticket_status_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ticket_id INTEGER NOT NULL,
                    old_status TEXT NOT NULL,
                    new_status TEXT NOT NULL,
                    changed_by INTEGER NOT NULL,
                    change_reason TEXT,
                    changed_time TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (ticket_id) REFERENCES tickets(id) ON DELETE CASCADE,
                    FOREIGN KEY (changed_by) REFERENCES users(id)
                )
            ''')

            # 到期提醒发送记录表（用于避免重复发送）
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS expiry_reminder_sent (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    group_number TEXT NOT NULL,
                    owner_username TEXT NOT NULL,
                    sku_id TEXT NOT NULL,
                    sent_date TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    expiration_date TEXT NOT NULL,
                    reminder_type TEXT NOT NULL DEFAULT 'auto'
                )
            ''')

            # 邮箱验证码表（用于注册/其他用途）
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS email_captchas (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    email TEXT NOT NULL,
                    purpose TEXT NOT NULL,
                    code TEXT NOT NULL,
                    requested_ip TEXT,
                    requested_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    expires_at TEXT NOT NULL,
                    used INTEGER NOT NULL DEFAULT 0
                )
            ''')

            # 按次付费功能管理表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS pay_per_use_features (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    feature_code TEXT NOT NULL UNIQUE,
                    display_name TEXT NOT NULL,
                    description TEXT,
                    api_endpoint TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_time TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_time TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 按次付费档位管理表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS pay_per_use_tiers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    tier_code TEXT NOT NULL UNIQUE,
                    display_name TEXT NOT NULL,
                    description TEXT,
                    price_per_use DECIMAL(10,2) NOT NULL,
                    is_active BOOLEAN DEFAULT 1,
                    created_time TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_time TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 按次付费兑换码管理表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS pay_per_use_codes (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    code TEXT NOT NULL UNIQUE,
                    feature_config TEXT NOT NULL,
                    usage_count INTEGER NOT NULL DEFAULT 1,
                    batch_id TEXT,
                    note TEXT,
                    is_used BOOLEAN DEFAULT 0,
                    used_by_username TEXT,
                    used_by_group TEXT,
                    used_time TEXT,
                    expires_at TEXT,
                    created_time TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 按次付费计费账户表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS pay_per_use_billing (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    group_number TEXT NOT NULL,
                    feature_id INTEGER NOT NULL,
                    remaining_count INTEGER NOT NULL DEFAULT 0,
                    total_purchased INTEGER NOT NULL DEFAULT 0,
                    created_time TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_time TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (feature_id) REFERENCES pay_per_use_features(id) ON DELETE CASCADE,
                    UNIQUE(group_number, feature_id)
                )
            ''')

            # 按次付费使用日志表 - 检查并更新表结构
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='pay_per_use_logs'")
            table_exists = cursor.fetchone()
            
            if table_exists:
                # 检查现有表结构
                cursor.execute("PRAGMA table_info(pay_per_use_logs)")
                existing_columns = [col[1] for col in cursor.fetchall()]
                
                # 如果缺少 request_note 字段，添加它
                if 'request_note' not in existing_columns:
                    try:
                        cursor.execute('ALTER TABLE pay_per_use_logs ADD COLUMN request_note TEXT')
                        logger.info('已为 pay_per_use_logs 表添加 request_note 字段')
                    except Exception as e:
                        logger.warn(f'添加 request_note 字段失败: {e}')
                
                # 如果缺少正确的索引结构，重建表
                if set(existing_columns) != {'id', 'group_number', 'feature_id', 'usage_count', 'request_note', 'request_time'}:
                    # 备份旧数据
                    cursor.execute('CREATE TABLE pay_per_use_logs_backup AS SELECT * FROM pay_per_use_logs')
                    cursor.execute('DROP TABLE pay_per_use_logs')
                    logger.info('已备份并重建 pay_per_use_logs 表')
            
            # 创建新的表结构
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS pay_per_use_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    group_number TEXT NOT NULL,
                    feature_id INTEGER NOT NULL,
                    usage_count INTEGER NOT NULL DEFAULT 1,
                    request_note TEXT,
                    request_time TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (feature_id) REFERENCES pay_per_use_features(id) ON DELETE CASCADE
                )
            ''')
            
            # 如果有备份数据，尝试迁移
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='pay_per_use_logs_backup'")
            if cursor.fetchone():
                try:
                    # 迁移数据，只迁移兼容的字段
                    cursor.execute('''
                        INSERT INTO pay_per_use_logs (group_number, feature_id, usage_count, request_time)
                        SELECT group_number, feature_id, 
                               COALESCE(usage_count, 1) as usage_count,
                               COALESCE(request_time, CURRENT_TIMESTAMP) as request_time
                        FROM pay_per_use_logs_backup
                    ''')
                    cursor.execute('DROP TABLE pay_per_use_logs_backup')
                    logger.info('已成功迁移 pay_per_use_logs 数据')
                except Exception as e:
                    logger.warn(f'迁移数据失败: {e}')
            
            # 修复按次付费计费账户表，添加缺失的 tier_id 字段
            cursor.execute("PRAGMA table_info(pay_per_use_billing)")
            billing_columns = [col[1] for col in cursor.fetchall()]
            if 'tier_id' not in billing_columns:
                try:
                    cursor.execute('ALTER TABLE pay_per_use_billing ADD COLUMN tier_id INTEGER DEFAULT 1')
                    logger.info('已为 pay_per_use_billing 表添加 tier_id 字段')
                except Exception as e:
                    logger.warn(f'添加 tier_id 字段失败: {e}')

            # Create indexes
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_bindings_owner ON bindings(owner_username)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_bindings_expiration ON bindings(expiration_time)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_codes_batch ON activation_codes(batch_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_codes_used ON activation_codes(is_used)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_email_captchas_lookup ON email_captchas(email, purpose, used)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_email_captchas_expiry ON email_captchas(expires_at)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_robot_groups_robot ON robot_groups(robot_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_robot_groups_group ON robot_groups(group_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_group_blacklist_group ON group_feature_blacklist(group_number)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_group_blacklist_feature ON group_feature_blacklist(feature_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_group_whitelist_group ON group_feature_whitelist(group_number)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_group_whitelist_feature ON group_feature_whitelist(feature_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_tickets_user ON tickets(user_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_tickets_status ON tickets(status)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_tickets_type ON tickets(ticket_type_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_ticket_replies_ticket ON ticket_replies(ticket_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_ticket_attachments_ticket ON ticket_attachments(ticket_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_ticket_attachments_reply ON ticket_attachments(reply_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_expiry_reminder_group_owner ON expiry_reminder_sent(group_number, owner_username)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_expiry_reminder_date ON expiry_reminder_sent(sent_date)')

            # 按次付费相关索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_pay_per_use_codes_code ON pay_per_use_codes(code)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_pay_per_use_codes_used ON pay_per_use_codes(is_used)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_pay_per_use_codes_batch ON pay_per_use_codes(batch_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_pay_per_use_billing_group ON pay_per_use_billing(group_number)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_pay_per_use_billing_feature ON pay_per_use_billing(feature_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_pay_per_use_logs_group ON pay_per_use_logs(group_number)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_pay_per_use_logs_feature ON pay_per_use_logs(feature_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_pay_per_use_logs_time ON pay_per_use_logs(request_time)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_pay_per_use_features_code ON pay_per_use_features(feature_code)')


            # 插入默认工单类型
            cursor.execute('''
                INSERT OR IGNORE INTO ticket_types (name, description, sort_order) VALUES 
                ('群聊问题', '关于群聊功能、权限、机器人等问题', 1),
                ('账户问题', '账户登录、注册、密码等问题', 2),
                ('激活码问题', '激活码使用、绑定等问题', 3),
                ('其他问题', '其他相关问题', 4)
            ''')
            
            self.conn.commit()
            logger.info('必要的数据表已创建')
            # 初始化系统设置默认值
            self._ensure_default_settings()
        except Exception as e:
            logger.error('创建数据表时出错:', e)
            raise e

    def _ensure_default_settings(self):
        """确保系统设置表存在并填充默认键值。"""
        try:
            cursor = self.conn.cursor()
            # 系统设置表（KV存储）
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS app_settings (
                    key TEXT PRIMARY KEY,
                    value TEXT
                )
            ''')
            # 系统设置
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS system_settings (
                    key TEXT PRIMARY KEY,
                    value TEXT NOT NULL,
                    updated_time TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 插入默认设置
            default_settings = {
                'admin_email': '<EMAIL>',
                'smtp_host': 'smtp.example.com',
                'smtp_port': '587',
                'smtp_user': '<EMAIL>',
                'smtp_pass': '',
                'smtp_tls': '1',
                'smtp_from': '<EMAIL>',
                'smtp_from_name': '小阡自助系统',
                'site_title': '小阡自助系统',
                'support_contact': '************',
                'site_link': 'https://example.com',
                'tickets_enabled': '1',
                'feature_blacklist_enabled': '1',
                'feature_whitelist_enabled': '1',
                'register_email_verification_enabled': '0',
                'ticket_email_notify_enabled': '1',
                'admin_ticket_email_notify_enabled': '1',
                'admin_reply_every_email_enabled': '0',
                'pre_expiry_renewal_email_enabled': '1',
                'pre_expiry_renewal_days': '3',
                'pre_expiry_renewal_repeat_enabled': '0',
                'email_template_end': 'muban1.html',
                'email_template_captcha': 'muban1.html',
                'email_template_work_order': 'muban1.html',
                'email_template_messages': 'muban1.html',
                'allowed_email_suffixes': '',
                'background_image_api': '',
                'pay_per_use_enabled': '1',
                'robot_groups_auto_refresh_enabled': '1',
                'robot_groups_refresh_interval_minutes': '60'
            }
            for k, v in default_settings.items():
                cursor.execute('INSERT OR IGNORE INTO app_settings (key, value) VALUES (?, ?)', (k, v))

            # 用户表新增：邮件提醒开关
            cursor.execute('PRAGMA table_info(users)')
            user_columns = [c[1] for c in cursor.fetchall()]
            if 'email_notify_enabled' not in user_columns:
                cursor.execute('ALTER TABLE users ADD COLUMN email_notify_enabled INTEGER DEFAULT 1')

            # 工单表新增：管理员提醒标记（用于抑制重复提醒，管理员回复后清零）
            cursor.execute('PRAGMA table_info(tickets)')
            ticket_columns = [c[1] for c in cursor.fetchall()]
            if 'admin_notified_flag' not in ticket_columns:
                cursor.execute('ALTER TABLE tickets ADD COLUMN admin_notified_flag INTEGER DEFAULT 0')
            # 工单表新增：用户提醒标记（用于抑制管理员多次回复重复提醒，用户回复后清零）
            cursor.execute('PRAGMA table_info(tickets)')
            ticket_columns2 = [c[1] for c in cursor.fetchall()]
            if 'user_notified_flag' not in ticket_columns2:
                cursor.execute('ALTER TABLE tickets ADD COLUMN user_notified_flag INTEGER DEFAULT 0')

            # 初始化按次付费默认数据
            cursor.execute('''
                INSERT OR IGNORE INTO pay_per_use_features (feature_code, display_name, description, api_endpoint, is_active) VALUES
                ('translation', '文本翻译', '将文本翻译为指定语言', '/api/pay-per-use/translate', 1),
                ('image_generation', '图片生成', '根据描述生成图片', '/api/pay-per-use/generate-image', 1),
                ('text_summary', '文本摘要', '生成文本摘要', '/api/pay-per-use/summarize', 1),
                ('code_review', '代码审查', 'AI代码审查和建议', '/api/pay-per-use/code-review', 1)
            ''')



            self.conn.commit()
        except Exception as e:
            logger.error('初始化系统设置默认值失败:', e)
            self.conn.rollback()

    def get_setting(self, key: str, default=None):
        try:
            cursor = self.conn.cursor()
            cursor.execute('SELECT value FROM app_settings WHERE key = ?', (key,))
            row = cursor.fetchone()
            return row['value'] if row and row['value'] is not None else default
        except Exception as e:
            # 若表不存在，尝试初始化后重试一次
            if 'no such table' in str(e):
                try:
                    self._ensure_default_settings()
                    cursor = self.conn.cursor()
                    cursor.execute('SELECT value FROM app_settings WHERE key = ?', (key,))
                    row = cursor.fetchone()
                    return row['value'] if row and row['value'] is not None else default
                except Exception as e2:
                    logger.error('读取系统设置失败(重试):', e2)
                    return default
            logger.error('读取系统设置失败:', e)
            return default

    def get_settings(self, keys: list[str]):
        result = {}
        try:
            cursor = self.conn.cursor()
            placeholders = ','.join('?' for _ in keys)
            cursor.execute(f'SELECT key, value FROM app_settings WHERE key IN ({placeholders})', keys)
            rows = cursor.fetchall()
            for row in rows:
                result[row['key']] = row['value']
        except Exception as e:
            if 'no such table' in str(e):
                try:
                    self._ensure_default_settings()
                    cursor = self.conn.cursor()
                    placeholders = ','.join('?' for _ in keys)
                    cursor.execute(f'SELECT key, value FROM app_settings WHERE key IN ({placeholders})', keys)
                    rows = cursor.fetchall()
                    for row in rows:
                        result[row['key']] = row['value']
                except Exception as e2:
                    logger.error('批量读取系统设置失败(重试):', e2)
            else:
                logger.error('批量读取系统设置失败:', e)
        finally:
            # 填补缺省
            for k in keys:
                if k not in result:
                    result[k] = None
        return result

    def set_settings(self, kv: dict):
        try:
            cursor = self.conn.cursor()
            for k, v in kv.items():
                cursor.execute('REPLACE INTO app_settings (key, value) VALUES (?, ?)', (k, str(v) if v is not None else ''))
            self.conn.commit()
            return True
        except Exception as e:
            if 'no such table' in str(e):
                try:
                    self._ensure_default_settings()
                    cursor = self.conn.cursor()
                    for k, v in kv.items():
                        cursor.execute('REPLACE INTO app_settings (key, value) VALUES (?, ?)', (k, str(v) if v is not None else ''))
                    self.conn.commit()
                    return True
                except Exception as e2:
                    logger.error('保存系统设置失败(重试):', e2)
                    self.conn.rollback()
                    return False
            logger.error('保存系统设置失败:', e)
            self.conn.rollback()
            return False

# Utility functions
def get_sku_type(sku_id):
    if sku_id == SKUID_10:
        return '10元档位'
    elif sku_id == SKUID_15:
        return '15元档位'
    else:
        logger.warn(f'遇到未知的 SKU ID: {sku_id}')
        return '未知档位'

def generate_activation_code(length=16):
    """生成指定长度的激活码"""
    characters = string.ascii_uppercase + string.digits
    # 移除容易混淆的字符
    characters = characters.replace('0', '').replace('O', '').replace('I', '').replace('1', '')
    
    # 生成格式化的激活码 (XXXX-XXXX-XXXX-XXXX)
    code_parts = []
    for _ in range(4):
        part = ''.join(secrets.choice(characters) for _ in range(4))
        code_parts.append(part)
    
    return '-'.join(code_parts)

class Config:
    def __init__(self):
        self.load_tiers_config()
    
    def load_tiers_config(self):
        """加载档位配置"""
        try:
            with open('tiers_config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
                self.TIERS = config.get('tiers', [])
                # 创建档位ID到信息的映射
                self.TIER_MAP = {tier['sku_id']: tier for tier in self.TIERS}
                # 创建档位ID到档位名称的映射，兼容旧代码
                self.SKU_ID_TO_NAME = {tier['sku_id']: tier['display_name'] for tier in self.TIERS}
                logger.info(f"成功加载 {len(self.TIERS)} 个档位配置")
        except FileNotFoundError:
            logger.warn("档位配置文件不存在，使用默认配置")
            # 默认档位配置，保持与原系统的SKU ID一致
            self.TIERS = [
                {
                    'id': 'standard',
                    'name': '标准版',
                    'display_name': '10元档位',
                    'sku_id': SKUID_10,
                    'price': 10,
                    'description': '适合个人用户使用',
                    'features': ['基础功能', '标准支持']
                },
                {
                    'id': 'premium',
                    'name': '高级版',
                    'display_name': '15元档位',
                    'sku_id': SKUID_15,
                    'price': 15,
                    'description': '适合专业用户使用',
                    'features': ['所有标准版功能', '高级功能', '优先支持']
                }
            ]
            self.TIER_MAP = {tier['sku_id']: tier for tier in self.TIERS}
            self.SKU_ID_TO_NAME = {tier['sku_id']: tier['display_name'] for tier in self.TIERS}
            # 保存默认配置到文件
            self.save_default_tiers_config()
        except json.JSONDecodeError as e:
            logger.error(f"档位配置文件格式错误: {e}")
            raise
    
    def _rebuild_tier_maps(self):
        """重建内存映射，确保 TIERS 变化后同步"""
        try:
            self.TIER_MAP = {tier['sku_id']: tier for tier in self.TIERS}
            self.SKU_ID_TO_NAME = {tier['sku_id']: tier.get('display_name') or tier.get('name') for tier in self.TIERS}
        except Exception as e:
            logger.error('重建档位映射失败:', e)
    
    def save_default_tiers_config(self):
        """保存默认档位配置到文件"""
        try:
            with open('tiers_config.json', 'w', encoding='utf-8') as f:
                json.dump({'tiers': self.TIERS}, f, ensure_ascii=False, indent=2)
            logger.info("已创建默认档位配置文件")
        except Exception as e:
            logger.error(f"保存默认档位配置失败: {e}")
    
    def save_tiers(self):
        """将当前 TIERS 保存到 tiers_config.json，并重建映射"""
        try:
            with open('tiers_config.json', 'w', encoding='utf-8') as f:
                json.dump({'tiers': self.TIERS}, f, ensure_ascii=False, indent=2)
            self._rebuild_tier_maps()
            logger.info('档位配置已保存')
            return True
        except Exception as e:
            logger.error('保存档位配置失败:', e)
            return False
    
    def set_tiers(self, tiers: list[dict]):
        """替换全部档位，保存并重建映射"""
        self.TIERS = tiers or []
        return self.save_tiers()
    
    def upsert_tier(self, tier: dict):
        """新增或更新单个档位（按 sku_id 区分）。返回 True 成功。"""
        if not tier:
            return False
        sku_id = tier.get('sku_id')
        if not sku_id:
            return False
        # 规范字段名
        normalized = {
            'id': tier.get('id') or tier.get('name') or tier.get('display_name') or sku_id,
            'name': tier.get('name') or tier.get('display_name') or '',
            'display_name': tier.get('display_name') or tier.get('name') or '',
            'sku_id': sku_id,
            'price': tier.get('price', 0),
            'description': tier.get('description', ''),
            'features': tier.get('features') or []
        }
        replaced = False
        for i, t in enumerate(self.TIERS):
            if t.get('sku_id') == sku_id:
                self.TIERS[i] = {**t, **normalized}
                replaced = True
                break
        if not replaced:
            self.TIERS.append(normalized)
        return self.save_tiers()
    
    def delete_tier(self, sku_id: str):
        """删除指定 sku_id 的档位并保存。返回 True/False。"""
        before = len(self.TIERS)
        self.TIERS = [t for t in self.TIERS if t.get('sku_id') != sku_id]
        if len(self.TIERS) == before:
            return False
        return self.save_tiers()
    
    def get_tier_by_sku_id(self, sku_id):
        """根据SKU ID获取档位信息"""
        return self.TIER_MAP.get(sku_id)
    
    def get_tier_name(self, sku_id):
        """根据SKU ID获取档位名称"""
        tier = self.TIER_MAP.get(sku_id)
        if tier:
            return tier['display_name']
        # 兼容旧的SKU ID
        return get_sku_type(sku_id)

# 创建全局配置实例
config = Config()

# --- System log utility ---
def append_system_log(actor: str, module_action: str, result: str = '', *, when: str | None = None):
    """将系统操作/邮件日志写入按天分文件的日志中。

    日志格式：
      操作用户，YYYY-MM-DD HH:MM:SS，操作板块/功能-结果

    文件位置：./log/YYYY-MM-DD.log
    """
    try:
        from datetime import datetime
        os.makedirs('log', exist_ok=True)
        now = datetime.now()
        ts = when or now.strftime('%Y-%m-%d %H:%M:%S')
        day = now.strftime('%Y-%m-%d')
        line = f"{actor}，{ts}，{module_action}-{result}".strip()
        with open(os.path.join('log', f'{day}.log'), 'a', encoding='utf-8') as f:
            f.write(line + '\n')
    except Exception as e:
        # 保底打印，不影响主流程
        logger.warn('写系统日志失败', e)