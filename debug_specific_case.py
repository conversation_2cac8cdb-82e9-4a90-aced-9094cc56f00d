#!/usr/bin/env python3
"""
调试具体的时间问题案例
"""

from datetime import datetime
import sqlite3

def debug_specific_case():
    """调试用户提到的具体案例"""
    print("=" * 60)
    print("调试具体案例")
    print("=" * 60)
    
    # 用户提到的具体时间
    bind_time = "2025/8/13 01:29:44"
    expiry_time = "2025/9/12 01:29:44"
    current_time = "2025/9/10 22:22"
    
    print(f"📅 绑定时间: {bind_time}")
    print(f"⏰ 到期时间: {expiry_time}")
    print(f"🕐 当前时间: {current_time}")
    
    # 解析时间
    try:
        bind_dt = datetime.strptime(bind_time, '%Y/%m/%d %H:%M:%S')
        expiry_dt = datetime.strptime(expiry_time, '%Y/%m/%d %H:%M:%S')
        current_dt = datetime.strptime(current_time, '%Y/%m/%d %H:%M')
        
        print(f"\n时间解析:")
        print(f"  绑定时间: {bind_dt}")
        print(f"  到期时间: {expiry_dt}")
        print(f"  当前时间: {current_dt}")
        
        # 计算时间差
        diff = expiry_dt - current_dt
        print(f"\n时间差计算:")
        print(f"  时间差: {diff}")
        print(f"  总秒数: {diff.total_seconds()}")
        print(f"  天数: {diff.days}")
        print(f"  小时数: {diff.seconds // 3600}")
        print(f"  分钟数: {(diff.seconds % 3600) // 60}")
        
        # 判断是否过期
        is_expired = expiry_dt < current_dt
        print(f"\n过期判断:")
        print(f"  是否过期: {'是' if is_expired else '否'}")
        print(f"  应该显示: {'已过期' if is_expired else f'剩余{diff.days}天{diff.seconds//3600}小时'}")
        
        # 模拟前端JavaScript逻辑
        print(f"\n模拟前端JavaScript逻辑:")
        
        # 模拟API返回的数据
        api_data = {
            'expirationDate': expiry_time,
            'expirationISOString': expiry_dt.isoformat()
        }
        
        print(f"  API返回数据:")
        print(f"    expirationDate: {api_data['expirationDate']}")
        print(f"    expirationISOString: {api_data['expirationISOString']}")
        
        # 前端过期判断逻辑
        js_is_expired = (api_data['expirationDate'] and 
                        not str(api_data['expirationDate']).__contains__('永久') and
                        api_data['expirationISOString'] and 
                        datetime.fromisoformat(api_data['expirationISOString']) < current_dt)
        
        print(f"  前端过期判断: {'已过期' if js_is_expired else '未过期'}")
        
        # 前端剩余天数计算
        if api_data['expirationDate'] and api_data['expirationDate'] != '永久' and api_data['expirationISOString']:
            expiration_time_js = datetime.fromisoformat(api_data['expirationISOString'])
            diff_time = expiration_time_js.timestamp() * 1000 - current_dt.timestamp() * 1000
            diff_days = diff_time / (1000 * 60 * 60 * 24)
            
            print(f"  前端剩余天数: {diff_days:.2f} 天")
            
            # 前端显示逻辑
            if diff_days > 0:
                if diff_days <= 7:
                    display = f"剩余{int(diff_days)}天"
                elif diff_days <= 30:
                    display = f"剩余{int(diff_days)}天"
                else:
                    display = f"剩余{int(diff_days)}天"
            elif diff_days == 0:
                display = "今日到期"
            else:
                display = "已过期"
            
            print(f"  前端应该显示: {display}")
        
        # 验证修复
        print(f"\n🎯 修复验证:")
        if not is_expired and not js_is_expired and diff.total_seconds() > 0:
            print(f"  ✅ 修复成功：正确判断为未过期")
            print(f"  ✅ 应该显示：剩余{diff.days}天")
        else:
            print(f"  ❌ 仍有问题：")
            print(f"    - 后端判断: {'过期' if is_expired else '未过期'}")
            print(f"    - 前端判断: {'过期' if js_is_expired else '未过期'}")
            print(f"    - 时间差: {diff.total_seconds()} 秒")
        
    except Exception as e:
        print(f"❌ 时间解析失败: {e}")

def check_database_for_similar_case():
    """检查数据库中是否有类似的案例"""
    print("\n" + "=" * 60)
    print("检查数据库中的类似案例")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('database.sqlite')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 查找2025年8月绑定，9月到期的记录
        cursor.execute('''
            SELECT activation_code, group_number, owner_username, bind_time, expiration_time
            FROM bindings 
            WHERE bind_time LIKE '%2025-08%' AND expiration_time LIKE '%2025-09%'
            LIMIT 5
        ''')
        
        results = cursor.fetchall()
        
        if not results:
            print("❌ 没有找到8月绑定9月到期的记录")
            
            # 查找任何9月到期的记录
            cursor.execute('''
                SELECT activation_code, group_number, owner_username, bind_time, expiration_time
                FROM bindings 
                WHERE expiration_time LIKE '%2025-09%'
                LIMIT 5
            ''')
            
            results = cursor.fetchall()
            
            if results:
                print(f"找到 {len(results)} 个9月到期的记录:")
            else:
                print("❌ 没有找到任何9月到期的记录")
                return
        else:
            print(f"找到 {len(results)} 个8月绑定9月到期的记录:")
        
        current_time = datetime.now()
        
        for row in results:
            print(f"\n激活码: {row['activation_code']}")
            print(f"群号: {row['group_number']}")
            print(f"用户: {row['owner_username']}")
            print(f"绑定时间: {row['bind_time']}")
            print(f"到期时间: {row['expiration_time']}")
            
            # 应用后端处理逻辑
            expiration_time = row['expiration_time']
            if expiration_time:
                try:
                    # 解析ISO格式的时间
                    exp_str = expiration_time
                    if exp_str.endswith('Z'):
                        exp_str = exp_str[:-1]
                    elif exp_str.endswith('.000Z'):
                        exp_str = exp_str[:-5]
                    
                    if 'T' in exp_str:
                        expiry_dt = datetime.fromisoformat(exp_str)
                    else:
                        expiry_dt = datetime.strptime(exp_str, '%Y-%m-%d %H:%M:%S')
                    
                    # 格式化显示时间
                    expiration_date_display = expiry_dt.strftime('%Y/%m/%d %H:%M:%S')
                    # 提供ISO格式用于前端计算
                    expiration_iso_string = expiry_dt.isoformat()
                    
                    print(f"处理后显示时间: {expiration_date_display}")
                    print(f"ISO格式时间: {expiration_iso_string}")
                    
                    # 计算剩余时间
                    diff = expiry_dt - current_time
                    is_expired = expiry_dt < current_time
                    
                    print(f"当前时间: {current_time}")
                    print(f"剩余时间: {diff}")
                    print(f"是否过期: {'是' if is_expired else '否'}")
                    
                    # 检查是否是类似的案例
                    if "01:29:44" in expiration_date_display or "01:29" in expiration_date_display:
                        print("🎯 这可能是用户提到的类似案例！")
                        if not is_expired:
                            print("✅ 当前逻辑正确：显示未过期")
                        else:
                            print("❌ 当前逻辑错误：显示已过期")
                    
                except Exception as e:
                    print(f'❌ 解析到期时间失败: {e}')
            
            print("-" * 40)
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据库查询失败: {e}")

def test_current_system():
    """测试当前系统的时间处理"""
    print("\n" + "=" * 60)
    print("测试当前系统")
    print("=" * 60)
    
    # 模拟用户案例的数据
    test_data = {
        'bind_time': '2025-08-13T01:29:44.000Z',
        'expiration_time': '2025-09-12T01:29:44.000Z'
    }
    
    print(f"测试数据:")
    print(f"  绑定时间: {test_data['bind_time']}")
    print(f"  到期时间: {test_data['expiration_time']}")
    
    # 应用当前的后端处理逻辑
    expiration_time = test_data['expiration_time']
    expiration_date_display = '永久有效'
    expiration_iso_string = None
    
    if expiration_time:
        try:
            # 解析ISO格式的时间
            exp_str = expiration_time
            if exp_str.endswith('Z'):
                exp_str = exp_str[:-1]
            elif exp_str.endswith('.000Z'):
                exp_str = exp_str[:-5]
            
            if 'T' in exp_str:
                expiry_dt = datetime.fromisoformat(exp_str)
            else:
                expiry_dt = datetime.strptime(exp_str, '%Y-%m-%d %H:%M:%S')
            
            # 格式化显示时间
            expiration_date_display = expiry_dt.strftime('%Y/%m/%d %H:%M:%S')
            # 提供ISO格式用于前端计算
            expiration_iso_string = expiry_dt.isoformat()
            
            print(f"\n后端处理结果:")
            print(f"  显示时间: {expiration_date_display}")
            print(f"  ISO时间: {expiration_iso_string}")
            
            # 模拟用户提到的当前时间
            current_time_str = "2025-09-10 22:22:00"
            current_dt = datetime.strptime(current_time_str, '%Y-%m-%d %H:%M:%S')
            
            # 计算时间差
            diff = expiry_dt - current_dt
            is_expired = expiry_dt < current_dt
            
            print(f"\n时间计算:")
            print(f"  到期时间: {expiry_dt}")
            print(f"  当前时间: {current_dt}")
            print(f"  时间差: {diff}")
            print(f"  是否过期: {'是' if is_expired else '否'}")
            
            # 模拟前端判断
            js_is_expired = (expiration_date_display and 
                            not str(expiration_date_display).__contains__('永久') and
                            expiration_iso_string and 
                            datetime.fromisoformat(expiration_iso_string) < current_dt)
            
            print(f"\n前端判断:")
            print(f"  过期状态: {'已过期' if js_is_expired else '未过期'}")
            
            # 前端剩余天数计算
            if expiration_date_display and expiration_date_display != '永久' and expiration_iso_string:
                expiration_time_js = datetime.fromisoformat(expiration_iso_string)
                diff_time = expiration_time_js.timestamp() * 1000 - current_dt.timestamp() * 1000
                diff_days = diff_time / (1000 * 60 * 60 * 24)
                
                print(f"  剩余天数: {diff_days:.2f} 天")
                
                # 前端显示逻辑
                if diff_days > 0:
                    display = f"剩余{int(diff_days)}天"
                elif diff_days == 0:
                    display = "今日到期"
                else:
                    display = "已过期"
                
                print(f"  应该显示: {display}")
            
            # 最终验证
            print(f"\n🎯 最终验证:")
            if expiration_date_display == '2025/09/12 01:29:44':
                print(f"  ✅ 显示时间正确: {expiration_date_display}")
            else:
                print(f"  ❌ 显示时间错误: {expiration_date_display}")
            
            if not is_expired and not js_is_expired:
                print(f"  ✅ 过期判断正确: 未过期")
            else:
                print(f"  ❌ 过期判断错误: 已过期")
            
            if diff.total_seconds() > 0:
                hours = int(diff.total_seconds() // 3600)
                print(f"  ✅ 剩余时间正确: {diff.days}天{hours}小时")
            else:
                print(f"  ❌ 剩余时间错误: 负数")
            
        except Exception as e:
            print(f'❌ 解析到期时间失败: {e}')

if __name__ == '__main__':
    debug_specific_case()
    check_database_for_similar_case()
    test_current_system()
