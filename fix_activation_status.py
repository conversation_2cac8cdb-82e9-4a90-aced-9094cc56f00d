#!/usr/bin/env python3
"""
修复激活状态的脚本
确保每个群组都有一个激活的绑定
"""

import sqlite3
from config import Database, logger, manage_group_activation

def fix_activation_status():
    """修复所有群组的激活状态"""
    
    # 连接数据库
    db = Database('./database.sqlite')
    if not db.connect():
        print("数据库连接失败")
        return
    
    try:
        cursor = db.conn.cursor()
        
        # 获取所有群组
        cursor.execute('''
            SELECT DISTINCT group_number 
            FROM bindings 
            ORDER BY group_number
        ''')
        
        groups = cursor.fetchall()
        print(f"找到 {len(groups)} 个群组")
        
        success_count = 0
        error_count = 0
        
        for group in groups:
            group_number = group['group_number']
            print(f"\n处理群组: {group_number}")
            
            try:
                # 检查该群组当前的激活状态
                cursor.execute('''
                    SELECT activation_code, is_active, remaining_days, expiration_time
                    FROM bindings 
                    WHERE group_number = ?
                    ORDER BY bind_time ASC
                ''', (group_number,))
                
                bindings = cursor.fetchall()
                active_count = sum(1 for b in bindings if b['is_active'] == 1)
                
                print(f"  群组 {group_number} 有 {len(bindings)} 个绑定，{active_count} 个激活")
                
                if active_count == 0:
                    print(f"  群组 {group_number} 没有激活的绑定，正在修复...")
                    # 运行激活管理逻辑
                    manage_group_activation(db.conn, group_number)
                    
                    # 验证修复结果
                    cursor.execute('''
                        SELECT COUNT(*) as active_count
                        FROM bindings 
                        WHERE group_number = ? AND is_active = 1
                    ''', (group_number,))
                    
                    new_active_count = cursor.fetchone()['active_count']
                    if new_active_count > 0:
                        print(f"  ✅ 群组 {group_number} 修复成功，现在有 {new_active_count} 个激活绑定")
                        success_count += 1
                    else:
                        print(f"  ❌ 群组 {group_number} 修复失败")
                        error_count += 1
                else:
                    print(f"  ✅ 群组 {group_number} 激活状态正常")
                    success_count += 1
                    
            except Exception as e:
                print(f"  ❌ 处理群组 {group_number} 时出错: {e}")
                error_count += 1
        
        print(f"\n修复完成:")
        print(f"  成功: {success_count} 个群组")
        print(f"  失败: {error_count} 个群组")
        
        # 最终统计
        cursor.execute('''
            SELECT 
                COUNT(*) as total_bindings,
                SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_bindings
            FROM bindings
        ''')
        
        stats = cursor.fetchone()
        print(f"\n最终统计:")
        print(f"  总绑定数: {stats['total_bindings']}")
        print(f"  激活绑定数: {stats['active_bindings']}")
        
    except Exception as e:
        print(f"修复过程中出错: {e}")
    finally:
        db.conn.close()

def check_activation_status():
    """检查当前激活状态"""
    
    db = Database('./database.sqlite')
    if not db.connect():
        print("数据库连接失败")
        return
    
    try:
        cursor = db.conn.cursor()
        
        # 按群组统计激活状态
        cursor.execute('''
            SELECT 
                group_number,
                COUNT(*) as total_bindings,
                SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_bindings,
                GROUP_CONCAT(CASE WHEN is_active = 1 THEN activation_code END) as active_codes
            FROM bindings
            GROUP BY group_number
            ORDER BY group_number
            LIMIT 10
        ''')
        
        groups = cursor.fetchall()
        
        print("激活状态检查 (前10个群组):")
        print("-" * 80)
        print(f"{'群号':<15} {'总绑定':<8} {'激活数':<8} {'激活码':<30}")
        print("-" * 80)
        
        for group in groups:
            active_codes = group['active_codes'] or '无'
            print(f"{group['group_number']:<15} {group['total_bindings']:<8} {group['active_bindings']:<8} {active_codes:<30}")
        
    except Exception as e:
        print(f"检查过程中出错: {e}")
    finally:
        db.conn.close()

if __name__ == '__main__':
    print("=" * 50)
    print("激活状态修复工具")
    print("=" * 50)
    
    print("\n1. 检查当前激活状态:")
    check_activation_status()
    
    print("\n2. 修复激活状态:")
    fix_activation_status()
    
    print("\n3. 再次检查激活状态:")
    check_activation_status()
