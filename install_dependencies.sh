#!/bin/bash

# Python5-Data 项目依赖安装脚本
# 解决 blinker 包冲突和外部管理环境问题

echo "开始安装 Python5-Data 项目依赖..."

# 检查是否有 requirements.txt 文件
if [ ! -f "requirements.txt" ]; then
    echo "错误: 找不到 requirements.txt 文件"
    exit 1
fi

echo "检测到的依赖文件:"
cat requirements.txt

echo ""
echo "开始安装依赖..."

# 首先尝试强制重新安装 blinker 包（解决版本冲突）
echo "1. 解决 blinker 包版本冲突..."
pip install --break-system-packages --force-reinstall --no-deps blinker==1.9.0

if [ $? -eq 0 ]; then
    echo "✅ blinker 包安装成功"
else
    echo "❌ blinker 包安装失败，但继续尝试安装其他依赖"
fi

echo ""
echo "2. 安装所有项目依赖..."

# 安装所有依赖，使用 --break-system-packages 解决外部管理环境问题
pip install --break-system-packages -r requirements.txt

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ 所有依赖安装成功！"
    echo ""
    echo "已安装的包版本:"
    echo "- Flask: $(pip show flask | grep Version | cut -d' ' -f2)"
    echo "- flask-cors: $(pip show flask-cors | grep Version | cut -d' ' -f2)"
    echo "- bcrypt: $(pip show bcrypt | grep Version | cut -d' ' -f2)"
    echo "- PyJWT: $(pip show PyJWT | grep Version | cut -d' ' -f2)"
    echo "- python-dotenv: $(pip show python-dotenv | grep Version | cut -d' ' -f2)"
    echo "- requests: $(pip show requests | grep Version | cut -d' ' -f2)"
    echo "- blinker: $(pip show blinker | grep Version | cut -d' ' -f2)"
    echo ""
    echo "现在可以运行服务器了:"
    echo "  python3 server.py"
else
    echo ""
    echo "❌ 依赖安装过程中出现错误"
    echo ""
    echo "常见解决方案:"
    echo "1. 确保有足够的权限"
    echo "2. 检查网络连接"
    echo "3. 尝试使用虚拟环境:"
    echo "   python3 -m venv venv"
    echo "   source venv/bin/activate"
    echo "   pip install -r requirements.txt"
    exit 1
fi

echo ""
echo "🎉 依赖安装完成！系统已准备就绪。"
