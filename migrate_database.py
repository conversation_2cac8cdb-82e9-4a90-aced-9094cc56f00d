#!/usr/bin/env python3
"""
数据库迁移脚本
将database.sqlite中的数据适配到后端期望的数据库结构
"""

import sqlite3
import os
import shutil
from datetime import datetime
import secrets
import string

def backup_database(db_path):
    """备份原数据库"""
    backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy2(db_path, backup_path)
    print(f"数据库已备份到: {backup_path}")
    return backup_path

def generate_activation_code(length=16):
    """生成指定长度的激活码"""
    characters = string.ascii_uppercase + string.digits
    # 移除容易混淆的字符
    characters = characters.replace('0', '').replace('O', '').replace('I', '').replace('1', '')
    
    # 生成格式化的激活码 (XXXX-XXXX-XXXX-XXXX)
    code_parts = []
    for _ in range(4):
        part = ''.join(secrets.choice(characters) for _ in range(4))
        code_parts.append(part)
    
    return '-'.join(code_parts)

def migrate_users_table(conn):
    """迁移用户表"""
    print("正在迁移用户表...")
    cursor = conn.cursor()
    
    # 检查现有用户表结构
    cursor.execute("PRAGMA table_info(users)")
    existing_columns = [col[1] for col in cursor.fetchall()]
    print(f"现有用户表字段: {existing_columns}")
    
    # 添加缺失的字段
    if 'id' not in existing_columns:
        # 如果没有id字段，需要重建表
        print("重建用户表以添加id字段...")
        cursor.execute("ALTER TABLE users RENAME TO users_old")
        
        # 创建新的用户表
        cursor.execute('''
            CREATE TABLE users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL UNIQUE,
                email TEXT NOT NULL UNIQUE,
                password TEXT NOT NULL,
                qq TEXT NOT NULL UNIQUE,
                register_time TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                is_banned INTEGER DEFAULT 0,
                ban_reason TEXT,
                ban_time TEXT
            )
        ''')
        
        # 迁移数据，为每个用户生成一个QQ号（临时）
        cursor.execute("SELECT username, email, password, register_time FROM users_old")
        old_users = cursor.fetchall()
        
        for i, user in enumerate(old_users, 1):
            # 生成临时QQ号（基于用户名hash）
            temp_qq = str(hash(user[0]) % 9000000000 + 1000000000)
            cursor.execute('''
                INSERT INTO users (username, email, password, qq, register_time)
                VALUES (?, ?, ?, ?, ?)
            ''', (user[0], user[1], user[2], temp_qq, user[3]))
        
        # 删除旧表
        cursor.execute("DROP TABLE users_old")
        print(f"用户表重建完成，迁移了 {len(old_users)} 个用户")
    else:
        # 添加缺失的字段
        if 'qq' not in existing_columns:
            cursor.execute('ALTER TABLE users ADD COLUMN qq TEXT')
            # 为现有用户生成临时QQ号
            cursor.execute("SELECT id, username FROM users WHERE qq IS NULL")
            users_without_qq = cursor.fetchall()
            for user in users_without_qq:
                temp_qq = str(hash(user[1]) % 9000000000 + 1000000000)
                cursor.execute("UPDATE users SET qq = ? WHERE id = ?", (temp_qq, user[0]))
            print(f"为 {len(users_without_qq)} 个用户添加了临时QQ号")
        
        if 'is_banned' not in existing_columns:
            cursor.execute('ALTER TABLE users ADD COLUMN is_banned INTEGER DEFAULT 0')
        if 'ban_reason' not in existing_columns:
            cursor.execute('ALTER TABLE users ADD COLUMN ban_reason TEXT')
        if 'ban_time' not in existing_columns:
            cursor.execute('ALTER TABLE users ADD COLUMN ban_time TEXT')
    
    conn.commit()
    print("用户表迁移完成")

def migrate_bindings_table(conn):
    """迁移绑定表"""
    print("正在迁移绑定表...")
    cursor = conn.cursor()
    
    # 检查现有绑定表结构
    cursor.execute("PRAGMA table_info(bindings)")
    existing_columns = [col[1] for col in cursor.fetchall()]
    print(f"现有绑定表字段: {existing_columns}")
    
    # 重建绑定表以匹配后端期望的结构
    cursor.execute("ALTER TABLE bindings RENAME TO bindings_old")
    
    # 创建新的绑定表
    cursor.execute('''
        CREATE TABLE bindings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            activation_code TEXT NOT NULL UNIQUE,
            sku_id TEXT NOT NULL,
            group_number TEXT NOT NULL,
            owner_username TEXT NOT NULL,
            bind_time TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
            expiration_time TEXT,
            UNIQUE (activation_code, group_number)
        )
    ''')
    
    # 迁移数据
    cursor.execute('''
        SELECT id, order_number, sku_id, group_number, owner, bind_time, expiration_time
        FROM bindings_old
    ''')
    old_bindings = cursor.fetchall()
    
    for binding in old_bindings:
        # 使用order_number作为activation_code，owner作为owner_username
        cursor.execute('''
            INSERT INTO bindings (activation_code, sku_id, group_number, owner_username, bind_time, expiration_time)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (binding[1], binding[2], binding[3], binding[4], binding[5], binding[6]))
    
    # 删除旧表
    cursor.execute("DROP TABLE bindings_old")
    
    conn.commit()
    print(f"绑定表迁移完成，迁移了 {len(old_bindings)} 个绑定记录")

def create_activation_codes_table(conn):
    """创建激活码表"""
    print("正在创建激活码表...")
    cursor = conn.cursor()
    
    # 创建激活码表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS activation_codes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            code TEXT NOT NULL UNIQUE,
            sku_id TEXT NOT NULL,
            duration_months INTEGER NOT NULL,
            is_used BOOLEAN DEFAULT 0,
            used_by TEXT,
            used_time TEXT,
            created_time TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
            batch_id TEXT,
            note TEXT
        )
    ''')
    
    # 从绑定表中生成激活码记录
    cursor.execute('''
        SELECT DISTINCT activation_code, sku_id, owner_username, bind_time
        FROM bindings
    ''')
    bindings = cursor.fetchall()
    
    batch_id = datetime.now().strftime('%Y%m%d%H%M%S') + "_migrated"
    
    for binding in bindings:
        activation_code = binding[0]
        sku_id = binding[1]
        used_by = binding[2]
        used_time = binding[3]
        
        # 根据SKU ID确定持续时间（月）
        duration_months = 12  # 默认12个月
        if sku_id == '6b9d46742cc311f094855254001e7c00':  # 10元档位
            duration_months = 12
        elif sku_id == '590a339e2cbb11f0983452540025c377':  # 15元档位
            duration_months = 12
        
        cursor.execute('''
            INSERT OR IGNORE INTO activation_codes 
            (code, sku_id, duration_months, is_used, used_by, used_time, created_time, batch_id, note)
            VALUES (?, ?, ?, 1, ?, ?, ?, ?, ?)
        ''', (activation_code, sku_id, duration_months, used_by, used_time, used_time, batch_id, "从旧数据迁移"))
    
    conn.commit()
    print(f"激活码表创建完成，生成了 {len(bindings)} 个激活码记录")

def create_missing_tables(conn):
    """创建后端期望的其他表"""
    print("正在创建其他必要的表...")
    cursor = conn.cursor()
    
    # 创建app_settings表（如果不存在）
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS app_settings (
            key TEXT PRIMARY KEY,
            value TEXT
        )
    ''')
    
    # 创建system_settings表（如果不存在）
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS system_settings (
            key TEXT PRIMARY KEY,
            value TEXT NOT NULL,
            updated_time TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    conn.commit()
    print("其他必要的表创建完成")

def main():
    """主函数"""
    db_path = "database.sqlite"
    
    if not os.path.exists(db_path):
        print(f"错误: 数据库文件 {db_path} 不存在")
        return
    
    print("开始数据库迁移...")
    
    # 备份数据库
    backup_path = backup_database(db_path)
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        
        # 执行迁移
        migrate_users_table(conn)
        migrate_bindings_table(conn)
        create_activation_codes_table(conn)
        create_missing_tables(conn)
        
        # 关闭连接
        conn.close()
        
        print("数据库迁移完成！")
        print(f"原数据库已备份到: {backup_path}")
        print("请检查迁移结果，如有问题可以从备份恢复")
        
    except Exception as e:
        print(f"迁移过程中出现错误: {e}")
        print(f"可以从备份文件恢复: {backup_path}")
        raise

if __name__ == "__main__":
    main()
