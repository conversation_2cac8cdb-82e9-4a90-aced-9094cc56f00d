/* iOS 美学风格 - 后台管理面板 */

/* 基础样式 */
* {
    box-sizing: border-box;
}

:root {
    /* iOS 色彩系统 */
    --ios-blue: #007AFF;
    --ios-blue-light: #5AC8FA;
    --ios-blue-dark: #0051D5;
    --ios-gray: #8E8E93;
    --ios-gray-light: #F2F2F7;
    --ios-gray-dark: #1C1C1E;
    --ios-background: #F2F2F7;
    --ios-surface: #FFFFFF;
    --ios-surface-secondary: #F9F9F9;
    --ios-border: #E5E5EA;
    --ios-text-primary: #000000;
    --ios-text-secondary: #8E8E93;
    --ios-red: #FF3B30;
    --ios-green: #34C759;
    --ios-orange: #FF9500;
    --ios-purple: #AF52DE;
    
    /* iOS 阴影 */
    --ios-shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.04);
    --ios-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    --ios-shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.12);
    
    /* iOS 圆角 */
    --ios-radius-sm: 8px;
    --ios-radius: 12px;
    --ios-radius-lg: 16px;
    --ios-radius-xl: 20px;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Inter', 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: 0;
    padding: 0;
    min-height: 100vh;
    color: var(--ios-text-primary);
    font-size: 16px;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    position: relative;
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
}

/* 当有背景图片时的样式 */
body.has-background {
    background-blend-mode: overlay;
}

/* 背景图片样式 */
body.has-background {
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    background-repeat: no-repeat;
}

body.has-background::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.3);
    z-index: -1;
}

/* 表单和面板过渡动画 */
.form-section,
.initial-view {
    transition: opacity 0.5s ease-in-out, max-height 0.7s ease-in-out, transform 0.5s ease-in-out;
    transform-origin: top;
}

.form-section:not(.active),
.initial-view:not(.active) {
    opacity: 0;
    max-height: 0;
    overflow: hidden;
    transform: scaleY(0.95) translateY(-10px);
    pointer-events: none;
}

.form-section.active,
.initial-view.active {
    opacity: 1;
    max-height: 1000px;
    transform: scaleY(1) translateY(0);
}

/* 模态框动画 */
.modal-content {
    animation: modalOpenAnimation 0.4s ease-out;
}

@keyframes modalOpenAnimation {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 加载器样式 */
.loader {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    animation: spin 1s linear infinite;
    display: inline-block;
    margin-right: 8px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}



/* 毛玻璃效果 */
.glass-effect {
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 动画效果 */
@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.slide-up {
    animation: slideUp 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

.scale-in {
    animation: scaleIn 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 加载动画 */
.admin-loader {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 卡片样式 */
.ios-card {
    background: rgba(255, 255, 255, 0.85);
    border-radius: var(--ios-radius-lg);
    box-shadow: var(--ios-shadow);
    border: 1px solid rgba(229, 229, 234, 0.6);
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.ios-card:hover {
    box-shadow: var(--ios-shadow-lg);
    transform: translateY(-2px);
}

/* 按钮样式 */
.ios-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 20px;
    border-radius: var(--ios-radius);
    font-weight: 600;
    font-size: 16px;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
    white-space: nowrap;
}

.ios-button:active {
    transform: scale(0.98);
}

.ios-button-primary {
    background: var(--ios-blue);
    color: white;
}

.ios-button-primary:hover {
    background: var(--ios-blue-dark);
    box-shadow: 0 4px 16px rgba(0, 122, 255, 0.3);
}

.ios-button-secondary {
    background: rgba(255, 255, 255, 0.8);
    color: var(--ios-blue);
    border: 1px solid var(--ios-border);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.ios-button-secondary:hover {
    background: rgba(255, 255, 255, 0.95);
    border-color: var(--ios-blue);
}

/* 小按钮样式 */
.ios-button-small {
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    min-height: 36px;
    gap: 6px;
    white-space: nowrap;
}

.ios-button-small:hover {
    transform: translateY(-1px);
    box-shadow: var(--ios-shadow);
}

.ios-button-small:active {
    transform: translateY(0);
    box-shadow: var(--ios-shadow-sm);
}

.ios-button-danger {
    background: var(--ios-red);
    color: white;
}

.ios-button-danger:hover {
    background: #D70015;
    box-shadow: 0 4px 16px rgba(255, 59, 48, 0.3);
}

.ios-button-small {
    padding: 8px 16px;
    font-size: 14px;
    border-radius: var(--ios-radius-sm);
}

/* Tab 导航样式 */
.ios-tabs {
    display: flex;
    background: rgba(255, 255, 255, 0.8);
    border-radius: var(--ios-radius);
    padding: 4px;
    box-shadow: var(--ios-shadow-sm);
    margin-bottom: 24px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.ios-tab {
    flex: 1;
    padding: 12px 16px;
    text-align: center;
    border-radius: var(--ios-radius-sm);
    font-weight: 500;
    color: var(--ios-text-secondary);
    transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    cursor: pointer;
    border: none;
    background: none;
    white-space: nowrap;
}

.ios-tab.active {
    background: var(--ios-blue);
    color: white;
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
}

.ios-tab:not(.active):hover {
    background: var(--ios-gray-light);
    color: var(--ios-text-primary);
}

/* 表格样式 */
.ios-table-container {
    background: rgba(255, 255, 255, 0.85);
    border-radius: var(--ios-radius-lg);
    overflow: hidden;
    box-shadow: var(--ios-shadow);
    border: 1px solid rgba(229, 229, 234, 0.6);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
}

/* 档位标签样式 */
.tier-badge {
    white-space: nowrap;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

/* 激活码详情样式 */
.activation-code-container {
    min-width: 120px;
}

.activation-code-toggle {
    white-space: nowrap;
}

.activation-details {
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        max-height: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        max-height: 200px;
        transform: translateY(0);
    }
}

.ios-table {
    width: 100%;
    border-collapse: collapse;
}

.ios-table th {
    background: var(--ios-surface-secondary);
    padding: 16px 20px;
    font-weight: 600;
    font-size: 14px;
    color: var(--ios-text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 1px solid var(--ios-border);
}

.ios-table td {
    padding: 16px 20px;
    border-bottom: 1px solid var(--ios-border);
    vertical-align: top;
}

.ios-table tr:last-child td {
    border-bottom: none;
}

.ios-table tr:hover {
    background: var(--ios-surface-secondary);
}

/* 输入框样式 */
.ios-input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--ios-border);
    border-radius: var(--ios-radius);
    font-size: 16px;
    background: var(--ios-surface);
    transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.ios-input:focus {
    outline: none;
    border-color: var(--ios-blue);
    box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1);
}

/* 标签样式 */
.ios-label {
    display: block;
    font-weight: 600;
    font-size: 14px;
    color: var(--ios-text-primary);
    margin-bottom: 8px;
}

/* 状态徽章 */
.ios-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.ios-badge-success {
    background: rgba(52, 199, 89, 0.1);
    color: var(--ios-green);
}

.ios-badge-warning {
    background: rgba(255, 149, 0, 0.1);
    color: var(--ios-orange);
}

.ios-badge-danger {
    background: rgba(255, 59, 48, 0.1);
    color: var(--ios-red);
}

.ios-badge-info {
    background: rgba(0, 122, 255, 0.1);
    color: var(--ios-blue);
}

/* 模态框样式 */
.ios-modal-backdrop {
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

.ios-modal {
    background: var(--ios-surface);
    border-radius: var(--ios-radius-xl);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    max-height: 90vh;
    overflow-y: auto;
    margin: 20px;
}

/* Toast 通知样式 */
.ios-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--ios-surface);
    border-radius: var(--ios-radius);
    box-shadow: var(--ios-shadow-lg);
    padding: 16px 20px;
    z-index: 1000;
    transform: translateX(100%);
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border: 1px solid var(--ios-border);
    min-width: 300px;
}

.ios-toast.show {
    transform: translateX(0);
}

.ios-toast.success {
    border-left: 4px solid var(--ios-green);
}

.ios-toast.error {
    border-left: 4px solid var(--ios-red);
}

/* 群头像样式 */
.ios-avatar {
    width: 40px;
    height: 40px;
    border-radius: var(--ios-radius);
    object-fit: cover;
    border: 2px solid var(--ios-border);
    box-shadow: var(--ios-shadow-sm);
}

/* 更大的群头像样式 */
.ios-group-avatar {
    width: 56px;
    height: 56px;
    border-radius: var(--ios-radius-lg);
    object-fit: cover;
    border: 3px solid var(--ios-border);
    box-shadow: var(--ios-shadow);
    transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.ios-group-avatar:hover {
    transform: scale(1.05);
    box-shadow: var(--ios-shadow-lg);
}

/* 激活码卡片样式 */
.activation-code-card {
    background: var(--ios-surface-secondary);
    border: 1px solid var(--ios-border);
    border-radius: var(--ios-radius);
    padding: 12px;
    margin-bottom: 8px;
    transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.activation-code-card:hover {
    background: #F0F0F5;
    transform: translateY(-1px);
    box-shadow: var(--ios-shadow-sm);
}

.activation-code-card.consuming {
    background: rgba(255, 149, 0, 0.05);
    border-color: var(--ios-orange);
}

.activation-code-card.waiting {
    background: rgba(0, 122, 255, 0.05);
    border-color: var(--ios-blue);
}

/* 页面标题样式 */
.ios-page-title {
    font-size: 32px;
    font-weight: 700;
    color: var(--ios-text-primary);
    text-align: center;
    margin-bottom: 32px;
    letter-spacing: -0.5px;
}

/* 登录卡片样式 */
.ios-login-card {
    background: rgba(255, 255, 255, 0.9);
    border-radius: var(--ios-radius-xl);
    box-shadow: var(--ios-shadow-lg);
    padding: 32px;
    max-width: 400px;
    margin: 0 auto;
    border: 1px solid rgba(229, 229, 234, 0.6);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

/* 主面板样式 */
.ios-main-panel {
    background: rgba(255, 255, 255, 0.85);
    border-radius: var(--ios-radius-xl);
    box-shadow: var(--ios-shadow);
    border: 1px solid rgba(229, 229, 234, 0.6);
    overflow: hidden;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

/* 档位徽章样式 */
.tier-badge {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    background: linear-gradient(135deg, var(--ios-blue) 0%, var(--ios-purple) 100%);
    color: white;
    box-shadow: var(--ios-shadow-sm);
}

/* 操作按钮组 */
.action-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
}

.action-buttons button {
    padding: 6px 12px;
    border-radius: var(--ios-radius-sm);
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border: none;
    cursor: pointer;
}

.action-buttons button:hover {
    transform: translateY(-1px);
    box-shadow: var(--ios-shadow-sm);
}

.action-buttons .text-blue-600 {
    color: var(--ios-blue);
}

.action-buttons .text-red-600 {
    color: var(--ios-red);
}

.action-buttons .text-emerald-700 {
    color: var(--ios-green);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .ios-modal {
        margin: 10px;
        border-radius: var(--ios-radius-lg);
    }

    .ios-button {
        padding: 14px 20px;
        font-size: 16px;
    }

    .ios-table th,
    .ios-table td {
        padding: 12px 16px;
    }

    .ios-page-title {
        font-size: 28px;
        margin-bottom: 24px;
    }

    .ios-login-card {
        padding: 24px;
        margin: 16px;
    }
}
