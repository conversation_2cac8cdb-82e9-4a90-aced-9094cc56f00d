/* 基础样式 */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    background-attachment: fixed;
}
/* 加载动画 */
.admin-loader {
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    animation: spin 1s linear infinite;
    display: inline-block;
}
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
/* 动画效果 */
.slide-down {
    animation: slideDown 0.5s ease-out;
}
.fade-in {
    animation: fadeIn 0.8s ease-out;
}
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}
/* Tab样式 */
.tab-button {
    whitespace: nowrap;
    padding: 0.75rem 1rem 0.5rem;
    border-bottom: 2px solid transparent;
    font-weight: 500;
    font-size: 0.875rem;
    color: #6b7280;
    transition: all 0.3s ease;
}
.tab-button:hover {
    color: #4f46e5;
}
.tab-button.active {
    color: #4f46e5;
    border-bottom-color: #4f46e5;
}
.tab-content {
    animation: fadeIn 0.3s ease-out;
}

/* 按次付费子标签样式 */
.pay-per-use-subtab {
    whitespace: nowrap;
    padding: 0.5rem 1rem;
    border-bottom: 2px solid transparent;
    font-weight: 500;
    font-size: 0.875rem;
    color: #6b7280;
    transition: all 0.3s ease;
    cursor: pointer;
}
.pay-per-use-subtab:hover {
    color: #059669;
    background-color: #f0fdf4;
}
.pay-per-use-subtab.active {
    color: #059669;
    border-bottom-color: #059669;
    background-color: #f0fdf4;
}
.pay-per-use-subcontent {
    animation: fadeIn 0.3s ease-out;
}
/* 表格样式 */
.responsive-table {
    width: 100%;
    border-collapse: collapse;
}

/* 通用表格容器类 */
.admin-table-container {
    min-width: 100%;
    border-collapse: collapse;
    height: 100%;
}

.admin-table-container thead {
    background-color: #f9fafb;
}

.admin-table-container th {
    padding: 0.75rem 1rem;
    text-align: left;
    font-size: 0.75rem;
    font-weight: 500;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-bottom: 1px solid #e5e7eb;
}

.admin-table-container td {
    padding: 0.75rem 1rem;
    border-top: 1px solid #e5e7eb;
}

.admin-table-container tbody tr:hover {
    background-color: #f9fafb;
}

.admin-table-container tbody tr {
    border-bottom: 1px solid #e5e7eb;
}

/* 移动端响应式 - 通用表格 */
@media (max-width: 768px) {
    .admin-table-container thead,
    .responsive-table thead {
        display: none;
    }
    
    .admin-table-container tr,
    .responsive-table tr {
        display: block;
        border: 1px solid #e5e7eb;
        margin-bottom: 0.625rem;
        border-radius: 0.375rem;
        background-color: #fff;
    }
    
    .admin-table-container td,
    .responsive-table td {
        display: flex;
        padding: 0.625rem;
        border: none;
        position: relative;
    }
    
    .admin-table-container td:before,
    .responsive-table td:before {
        content: attr(data-label);
        font-weight: 600;
        margin-right: 0.5rem;
        min-width: 6rem;
        color: #6b7280;
    }
    
    .admin-table-container td:not(:last-child),
    .responsive-table td:not(:last-child) {
        border-bottom: 1px solid #f3f4f6;
    }
}
/* 群聊表格特殊样式 */
#groupsTable {
    table-layout: fixed;
}
#groupsTable th:nth-child(1), 
#groupsTable td:nth-child(1) { width: 50px; } /* 选择框 */
#groupsTable th:nth-child(2), 
#groupsTable td:nth-child(2) { width: 120px; } /* 群号 */
#groupsTable th:nth-child(3), 
#groupsTable td:nth-child(3) { width: 60px; } /* 群头像 */
#groupsTable th:nth-child(4), 
#groupsTable td:nth-child(4) { 
    width: 200px; 
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
} /* 群名称 */
#groupsTable th:nth-child(5), 
#groupsTable td:nth-child(5) { width: 100px; } /* 成员数 */
#groupsTable th:nth-child(6), 
#groupsTable td:nth-child(6) { 
    width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
} /* 备注 */
#groupsTable th:nth-child(7), 
#groupsTable td:nth-child(7) { width: 80px; } /* 状态 */
#groupsTable th:nth-child(8), 
#groupsTable td:nth-child(8) { 
    width: 180px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
} /* 所属BOT账号 */
#groupsTable th:nth-child(9), 
#groupsTable td:nth-child(9) { width: 80px; } /* 操作 */

/* 绑定管理表格特殊样式 */
#bindingsTable {
    table-layout: fixed;
}
#bindingsTable th:nth-child(1), 
#bindingsTable td:nth-child(1) { width: 60px; } /* 序号 */
#bindingsTable th:nth-child(2), 
#bindingsTable td:nth-child(2) { width: 120px; } /* 群号 */
#bindingsTable th:nth-child(3), 
#bindingsTable td:nth-child(3) { width: 60px; } /* 群头像 */
#bindingsTable th:nth-child(4), 
#bindingsTable td:nth-child(4) { 
    width: 120px; /* 激活码列变小 */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
#bindingsTable th:nth-child(5), 
#bindingsTable td:nth-child(5) { 
    width: 180px; /* 档位列变宽 */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
#bindingsTable th:nth-child(6), 
#bindingsTable td:nth-child(6) { width: 120px; } /* 所属用户 */
#bindingsTable th:nth-child(7), 
#bindingsTable td:nth-child(7) { width: 120px; } /* 用户QQ */
#bindingsTable th:nth-child(8), 
#bindingsTable td:nth-child(8) { width: 160px; } /* 到期时间 */
#bindingsTable th:nth-child(9), 
#bindingsTable td:nth-child(9) { width: 200px; } /* 操作 */
/* 绑定管理表格移动端响应式处理 */
@media (max-width: 1024px) {
    /* 平板端隐藏某些列 */
    #groupsTable th:nth-child(6), 
    #groupsTable td:nth-child(6),
    #groupsTable th:nth-child(8), 
    #groupsTable td:nth-child(8) {
        display: none;
    }
    
    /* 绑定管理表格在平板端的优化 */
    #bindingsTable th:nth-child(6), 
    #bindingsTable td:nth-child(6),
    #bindingsTable th:nth-child(7), 
    #bindingsTable td:nth-child(7) {
        display: none;
    }
    
    /* 调整剩余列的宽度 */
    #bindingsTable th:nth-child(4), 
    #bindingsTable td:nth-child(4) { width: 140px; }
    #bindingsTable th:nth-child(5), 
    #bindingsTable td:nth-child(5) { width: 160px; }
    #bindingsTable th:nth-child(8), 
    #bindingsTable td:nth-child(8) { width: 140px; }
}
@media (max-width: 768px) {
    /* 手机端群聊表格卡片式布局 */
    #groupsTable tbody tr {
        display: block;
        border: 1px solid #e5e7eb;
        margin-bottom: 1rem;
        border-radius: 0.5rem;
        background-color: #fff;
        padding: 0.75rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    
    #groupsTable td {
        display: flex;
        padding: 0.5rem 0;
        border: none;
        align-items: center;
    }
    
    #groupsTable td[data-label="选择"] {
        position: absolute;
        top: 0.75rem;
        right: 0.75rem;
    }
    
    #groupsTable td[data-label="群头像"] {
        position: absolute;
        top: 0.75rem;
        left: 3rem;
    }
    
    #groupsTable td[data-label="群号"],
    #groupsTable td[data-label="群名称"] {
        font-weight: 600;
        padding-left: 5rem;
    }
    
    #groupsTable td[data-label="状态"] {
        position: absolute;
        top: 0.75rem;
        right: 3rem;
    }
    
    #groupsTable td[data-label="备注"],
    #groupsTable td[data-label="所属BOT账号"] {
        display: block;
        width: 100%;
    }
    
    #groupsTable td[data-label="操作"] {
        justify-content: center;
        margin-top: 0.5rem;
        padding-top: 0.75rem;
        border-top: 1px solid #f3f4f6;
    }
    
    #groupsTable td:before {
        font-size: 0.75rem;
        color: #9ca3af;
        min-width: 5rem;
    }
    
    /* 手机端绑定管理表格保持表格布局，仅隐藏部分列 */
    #bindingsTable th:nth-child(6), 
    #bindingsTable td:nth-child(6),
    #bindingsTable th:nth-child(7), 
    #bindingsTable td:nth-child(7) {
        display: none;
    }
    
    /* 调整剩余列宽度适应小屏幕 */
    #bindingsTable th:nth-child(1), 
    #bindingsTable td:nth-child(1) { width: 40px; } /* 序号 */
    #bindingsTable th:nth-child(2), 
    #bindingsTable td:nth-child(2) { width: 80px; } /* 群号 */
    #bindingsTable th:nth-child(3), 
    #bindingsTable td:nth-child(3) { width: 40px; } /* 群头像 */
    #bindingsTable th:nth-child(4), 
    #bindingsTable td:nth-child(4) { width: 100px; } /* 激活码 */
    #bindingsTable th:nth-child(5), 
    #bindingsTable td:nth-child(5) { width: 120px; } /* 档位 */
    #bindingsTable th:nth-child(8), 
    #bindingsTable td:nth-child(8) { width: 100px; } /* 到期时间 */
    #bindingsTable th:nth-child(9), 
    #bindingsTable td:nth-child(9) { width: 80px; } /* 操作 */
    
    /* 激活码按钮在移动端的适配 */
    .activation-code-link {
        font-size: 0.75rem;
        padding: 0.5rem 1rem;
        border-radius: 0.75rem;
    }
    
    /* 档位徽章在移动端的适配 */
    .tier-badge {
        font-size: 0.75rem;
        padding: 0.5rem 1rem;
        border-radius: 0.75rem;
    }
    
    /* 群头像在移动端的适配 */
    .group-avatar {
        width: 32px;
        height: 32px;
    }
    
    /* 搜索栏在移动端的适配 */
    #bindingsSearchInput {
        width: 100% !important;
        max-width: 300px;
        margin-bottom: 0.5rem;
    }
}
/* 状态徽章 */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}
.status-badge.used {
    background-color: #fee2e2;
    color: #991b1b;
}
.status-badge.unused {
    background-color: #d1fae5;
    color: #065f46;
}
/* 操作按钮 */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}
.action-buttons button {
    font-size: 0.875rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    transition: all 0.2s;
}
.action-buttons button:hover {
    transform: translateY(-1px);
}
/* 模态框样式 */
.modal-content-admin {
    animation: modalSlideIn 0.3s ease-out;
}
@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}
/* 分页样式 */
.pagination-button {
    padding: 0.5rem 1rem;
    border: 1px solid #e5e7eb;
    background-color: white;
    color: #374151;
    font-size: 0.875rem;
    transition: all 0.2s;
}
.pagination-button:hover:not(:disabled) {
    background-color: #f9fafb;
    border-color: #d1d5db;
}
.pagination-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}
.pagination-button.active {
    background-color: #4f46e5;
    color: white;
    border-color: #4f46e5;
}
/* 兑换码样式 */
.code-display {
    font-family: 'Courier New', Courier, monospace;
    background-color: #f3f4f6;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
}
.code-list {
    max-height: 400px;
    overflow-y: auto;
    padding: 1rem;
    background-color: #f9fafb;
    border-radius: 0.5rem;
}
.code-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    border-bottom: 1px solid #e5e7eb;
}
.code-item:last-child {
    border-bottom: none;
}

/* 激活码查看按钮样式优化 */
.activation-code-link {
    display: inline-flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    border-radius: 0.5rem;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.activation-code-link:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
    text-decoration: none;
    color: white;
}

.activation-code-link:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

/* 群头像样式优化 */
.group-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    flex-shrink: 0;
    border: 2px solid #e5e7eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.group-avatar:hover {
    border-color: #3b82f6;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
    transform: scale(1.05);
}

/* 群头像占位符样式 */
.group-avatar-placeholder {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #f3f4f6;
    border: 2px solid #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    transition: all 0.2s ease;
}

.group-avatar-placeholder:hover {
    background-color: #e5e7eb;
    border-color: #3b82f6;
    transform: scale(1.05);
}

/* 档位徽章样式优化 - 统一风格 */
.tier-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 600;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    transition: all 0.2s ease;
}

.tier-badge.unified {
    background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
    color: white;
}

.tier-badge.unified:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(99, 102, 241, 0.3);
}
/* 群聊统计样式 */
#botGroupStats {
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}
#botStatsContent > div {
    text-align: center;
    transition: transform 0.2s;
}
#botStatsContent > div:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
/* 广播消息文本框 */
#broadcastMessage {
    resize: vertical;
    min-height: 100px;
}
/* 特殊优化：极小屏幕 */
@media (max-width: 480px) {
    .container {
        padding: 0.5rem;
    }
    
    .tab-button {
        font-size: 0.75rem;
        padding: 0.5rem;
    }
    
    /* 群聊表格进一步简化 */
    #groupsTable td[data-label="成员数"] {
        display: none;
    }
    
    #groupsTable td[data-label="群名称"] {
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}