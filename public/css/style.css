/* style.css - 用户前端样式文件 */

body {
    font-family: 'Inter', sans-serif;
    /* 背景图地址 */
    background-image: url('https://t.alcy.cc/ycy');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
}

/* 工单回复/详情内容的强制断行与排版保护 */
.prose .text-gray-700,
.ticket-content,
#ticketReplies .text-gray-700 {
    word-break: break-word;
    overflow-wrap: anywhere;
    line-height: 1.7;
}

/* 回复卡片左右留白，避免大图贴边 */
#ticketReplies img,
.ticket-content img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    display: block;
}

/* 防止图片过高撑开：限制单张最大高度，允许点击原图另开 */
#ticketReplies img,
.ticket-content img {
    max-height: 70vh;
}

/* 代码/链接的可滚动与断行优化 */
.ticket-content code,
.ticket-content pre,
.ticket-content a {
    word-break: break-all;
}

.ticket-content {
    overflow-x: auto;
    max-width: 100%;
}

/* 避免长不可断字符串把卡片撑宽 */
.reply-card {
    overflow-wrap: anywhere;
}

.body-overlay::before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.3);
    z-index: -1;
}

.form-section,
.initial-view {
    transition: opacity 0.5s ease-in-out, max-height 0.7s ease-in-out, transform 0.5s ease-in-out;
    transform-origin: top;
}

.form-section:not(.active),
.initial-view:not(.active),
.form-section.hidden,
.initial-view.hidden {
    opacity: 0 !important;
    max-height: 0 !important;
    overflow: hidden !important;
    transform: scaleY(0.95) translateY(-10px) !important;
    pointer-events: none !important;
    display: none !important;
    visibility: hidden !important;
    height: 0 !important;
}

.form-section.active,
.initial-view.active {
    opacity: 1 !important;
    max-height: none !important; /* 取消高度上限，避免内容溢出 */
    transform: scaleY(1) translateY(0) !important;
    display: block !important; /* 强制显示 */
    visibility: visible !important;
    height: auto !important;
    overflow: visible !important;
    pointer-events: auto !important;
}

.modal-content {
    animation: modalOpenAnimation 0.4s ease-out;
}

@keyframes modalOpenAnimation {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.95);
    }

    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.loader {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    animation: spin 1s linear infinite;
    display: inline-block;
    margin-right: 8px;
}

/* 小型加载器 - 用于按钮内 */
button .loader {
    width: 16px;
    height: 16px;
    border-width: 2px;
    margin-right: 0;
    margin-left: 8px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.order-card {
    transition: all 0.3s ease;
}

.order-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 按钮悬停效果 */
button {
    transition: all 0.3s ease;
}

button:hover {
    transform: translateY(-1px);
}

button:active {
    transform: scale(0.98);
}

/* 输入框聚焦效果 */
input:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 成功消息样式 */
.success-message {
    color: #059669;
    background-color: #d1fae5;
    border: 1px solid #6ee7b7;
}

/* 错误消息样式 */
.error-message {
    color: #dc2626;
    background-color: #fee2e2;
    border: 1px solid #fca5a5;
}

/* 信息消息样式 */
.info-message {
    color: #1e40af;
    background-color: #dbeafe;
    border: 1px solid #93c5fd;
}

/* 过期标签样式 */
.expired-badge {
    background-color: #fee2e2;
    color: #991b1b;
    padding: 0.125rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
}

/* 修改密码模态框样式 */
#changePasswordModal .modal-content {
    max-width: 28rem;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.98);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 功能黑名单模态框样式 */
#featureBlacklistModal .modal-content {
    animation: slideIn 0.3s ease-out;
}

/* 功能切换开关样式 */
.feature-toggle {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
}

.feature-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

/* 模态框内的表单样式 */
.modal-content form {
    margin-top: 1rem;
}

.modal-content form input {
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.modal-content form input:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 错误信息样式 */
#changePasswordError {
    min-height: 1.25rem;
    color: #dc2626;
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

/* 按钮组样式 */
.modal-content .flex.justify-end {
    border-top: 1px solid #e5e7eb;
    padding-top: 1rem;
    margin-top: 1.5rem;
}

/* 响应式调整 */
@media (max-width: 640px) {
    .container {
        margin: 1rem;
        padding: 1.5rem;
    }
    
    h1 {
        font-size: 1.875rem;
    }
    
    h2 {
        font-size: 1.5rem;
    }
    
    .form-section.active,
    .initial-view.active {
        max-height: none; /* 移动端同样不限制高度 */
    }
    
    /* 移动端模态框调整 */
    .modal-content {
        margin: 1rem;
        max-height: 90vh;
        overflow-y: auto;
    }
    
    #changePasswordModal .modal-content,
    #featureBlacklistModal .modal-content {
        width: calc(100% - 2rem);
    }
}

/* 模态框遮罩层 */
#resultModal,
#changePasswordModal,
#featureBlacklistModal {
    backdrop-filter: blur(4px);
}

/* 用户面板卡片样式 */
.user-info-card {
    background: linear-gradient(135deg, #f5f7fa 0%, #e9ecef 100%);
}

/* 激活记录卡片悬停效果 */
.order-card {
    border: 1px solid #e5e7eb;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.order-card:hover {
    border-color: #d1d5db;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* 链接样式 */
a {
    transition: color 0.2s ease;
}

/* 表单标签样式 */
label {
    font-weight: 500;
}

/* 占位符样式 */
::placeholder {
    color: #9ca3af;
    opacity: 1;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* 加载状态下的按钮样式 */
button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

button:disabled:hover {
    transform: none;
}

/* 小提示文本样式 */
.text-xs {
    line-height: 1.5;
}

/* 账户信息区域样式 */
.bg-gray-100 {
    background-color: rgba(243, 244, 246, 0.8);
}

/* 功能黑名单项目样式 */
.feature-card {
    transition: all 0.2s ease;
}

.feature-card:hover {
    background-color: #f9fafb;
    border-color: #d1d5db;
}

/* 密码输入框的显示/隐藏按钮样式 */
.password-toggle {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #6b7280;
}

.password-toggle:hover {
    color: #374151;
}

/* 表单验证状态样式 */
input:invalid {
    border-color: #ef4444;
}

input:valid {
    border-color: #10b981;
}

/* 模态框关闭按钮优化 */
.modal-content button[id*="close"] {
    transition: all 0.2s ease;
}

.modal-content button[id*="close"]:hover {
    background-color: #f3f4f6;
    border-radius: 0.375rem;
}

/* 功能开关样式优化 */
.peer-checked\:bg-red-600 {
    background-color: #dc2626;
}

.peer-checked\:bg-green-600 {
    background-color: #059669;
}

/* 提交按钮加载状态 */
button[type="submit"] .button-text {
    display: inline-flex;
    align-items: center;
}

button[type="submit"].loading .button-text {
    margin-right: 0.5rem;
}

/* 模态框内容最大高度限制 */
.modal-content {
    max-height: calc(100vh - 4rem);
    overflow-y: auto;
}

/* 平滑滚动 */
.modal-content::-webkit-scrollbar {
    width: 6px;
}

.modal-content::-webkit-scrollbar-track {
    background: transparent;
}

.modal-content::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 3px;
}

.modal-content::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

/* 按次付费标签页样式 */
.pay-per-use-user-tab {
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
    cursor: pointer;
    background: #f9fafb;
    color: #6b7280;
    border: none;
}

.pay-per-use-user-tab.active {
    border-bottom-color: #3B82F6;
    color: #3B82F6;
    font-weight: 600;
    background: white;
}

.pay-per-use-user-tab:hover:not(.active) {
    background: #f3f4f6;
    color: #4b5563;
}

.pay-per-use-user-content {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    max-height: 0 !important;
    overflow: hidden !important;
}

.pay-per-use-user-content:not(.hidden) {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: auto !important;
    max-height: none !important;
    overflow: visible !important;
}

.pay-per-use-user-content.active-content {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: auto !important;
    max-height: none !important;
    overflow: visible !important;
}

/* 按次付费界面响应式调整 */
@media (max-width: 640px) {
    .pay-per-use-user-tab {
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
    }
}

/* 按次付费表单样式优化 */
#payPerUseRedeemForm {
    max-width: 100%;
}

#userGroupsContent {
    max-height: 200px;
    overflow-y: auto;
}

#userGroupsContent > div {
    cursor: pointer;
    transition: all 0.2s ease;
}

#userGroupsContent > div:hover {
    background-color: #F9FAFB;
}

/* 按次付费余额显示样式 */
.pay-per-use-balance-item {
    border: 1px solid #E5E7EB;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 0.75rem;
    background-color: #FFFFFF;
}

.pay-per-use-balance-item h4 {
    margin-bottom: 0.5rem;
    color: #374151;
}

.pay-per-use-balance-item p {
    margin-bottom: 0.25rem;
    color: #6B7280;
}

/* 按次付费按钮样式 */
.pay-per-use-button {
    transition: all 0.3s ease;
}

.pay-per-use-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 群聊选择卡片样式优化 */
#userGroupsContent > div {
    transition: all 0.3s ease;
}

#userGroupsContent > div:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

#userGroupsContent > div.ring-2 {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}
