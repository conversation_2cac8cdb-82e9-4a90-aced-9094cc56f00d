<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自助服务 - 激活码绑定与查询</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/style.css?v=5">
</head>

<body class="flex items-center justify-center min-h-screen p-4">
    <div class="body-overlay"></div>
    <div class="container bg-white/90 backdrop-blur-sm p-6 sm:p-8 md:p-10 rounded-xl shadow-2xl w-full max-w-md transform transition-all duration-500 overflow-y-auto max-h-screen">
        <h1 class="text-3xl font-bold text-center text-slate-800 mb-8">自助服务系统</h1>

        <div class="initial-view active" id="initialView">
            <p class="text-center text-gray-700 mb-6">请选择您要进行的操作：</p>
            <div class="space-y-4">
                <button id="showLoginButton"
                    class="w-full bg-purple-600 hover:bg-purple-700 text-white font-semibold py-3 px-4 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 ease-in-out transform hover:-translate-y-0.5 flex items-center justify-center space-x-2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                    </svg>
                    <span>登录账户</span>
                </button>
                <button id="showRegisterButton"
                    class="w-full bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-3 px-4 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 ease-in-out transform hover:-translate-y-0.5 flex items-center justify-center space-x-2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z" />
                    </svg>
                    <span>注册账户</span>
                </button>
            </div>
        </div>

        <div class="form-section" id="bindSection">
            <h2 class="text-2xl font-semibold text-center text-slate-700 mb-6">绑定激活码</h2>
            <form id="bindForm" class="space-y-5">
                <div>
                    <label for="bindOrderNumber" class="block text-sm font-medium text-gray-700 mb-1">激活码</label>
                    <input type="text" id="bindOrderNumber" name="bindOrderNumber" placeholder="请输入您的激活码或兑换码" required autocomplete="one-time-code"
                        class="mt-1 block w-full px-4 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors duration-300 bg-white/70">
                    <p class="mt-1 text-xs text-gray-500">激活码可以是管理员分配的兑换码</p>
                </div>
                <div>
                    <label for="bindGroupNumber" class="block text-sm font-medium text-gray-700 mb-1">绑定群号</label>
                    <input type="text" id="bindGroupNumber" name="bindGroupNumber" placeholder="请输入您要绑定的QQ群号" required autocomplete="off"
                        class="mt-1 block w-full px-4 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors duration-300 bg-white/70">
                </div>
                <div class="flex flex-col sm:flex-row sm:space-x-3 space-y-3 sm:space-y-0 pt-2">
                    <button type="submit" id="bindButton"
                        class="w-full sm:w-auto flex-grow bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2.5 px-4 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 ease-in-out transform hover:-translate-y-0.5 flex items-center justify-center">
                        <span class="button-text">确认绑定</span>
                        <span class="loader hidden"></span>
                    </button>
                    <button type="button" id="backFromBind"
                        class="w-full sm:w-auto bg-gray-200 hover:bg-gray-300 text-gray-700 font-semibold py-2.5 px-4 rounded-lg shadow hover:shadow-md transition-all duration-300 ease-in-out">
                        返回
                    </button>
                </div>
            </form>
        </div>

        <!-- 登录表单部分 -->
        <div class="form-section" id="loginSection">
            <h2 class="text-2xl font-semibold text-center text-slate-700 mb-6">用户登录</h2>
            <form id="loginForm" class="space-y-5">
                <div>
                    <label for="loginUsername" class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                    <input type="text" id="loginUsername" name="loginUsername" placeholder="请输入您的用户名" required autocomplete="username"
                        class="mt-1 block w-full px-4 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm transition-colors duration-300 bg-white/70">
                </div>
                <div>
                    <label for="loginPassword" class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                    <input type="password" id="loginPassword" name="loginPassword" placeholder="请输入您的密码" required autocomplete="current-password"
                        class="mt-1 block w-full px-4 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm transition-colors duration-300 bg-white/70">
                </div>
                <div class="flex flex-col sm:flex-row sm:space-x-3 space-y-3 sm:space-y-0 pt-2">
                    <button type="submit" id="loginButton"
                        class="w-full sm:w-auto flex-grow bg-purple-600 hover:bg-purple-700 text-white font-semibold py-2.5 px-4 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 ease-in-out transform hover:-translate-y-0.5 flex items-center justify-center">
                        <span class="button-text">登录</span>
                        <span class="loader hidden"></span>
                    </button>
                    <button type="button" id="backFromLogin"
                        class="w-full sm:w-auto bg-gray-200 hover:bg-gray-300 text-gray-700 font-semibold py-2.5 px-4 rounded-lg shadow hover:shadow-md transition-all duration-300 ease-in-out">
                        返回
                    </button>
                </div>
                <div class="text-center text-sm text-gray-600 mt-4">
                    <p>还没有账户？<a href="#" id="switchToRegister" class="text-purple-600 hover:text-purple-800 font-medium">立即注册</a></p>
                </div>
            </form>
        </div>

        <!-- 用户登录后的主界面 -->
        <div class="form-section" id="userMainSection">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-semibold text-slate-700">欢迎回来，<span id="userDisplayName"></span></h2>
                <button id="logoutButton" class="text-red-600 hover:text-red-800 font-medium">退出登录</button>
            </div>
            
            <!-- 功能导航 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
                <button id="showBindingsButton" class="p-4 bg-blue-50 hover:bg-blue-100 rounded-lg border border-blue-200 transition-colors">
                    <div class="flex items-center space-x-3">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                        <div class="text-left">
                            <div class="font-medium text-blue-900">我的绑定</div>
                            <div class="text-sm text-blue-600">查看和管理群聊绑定</div>
                        </div>
                    </div>
                </button>
                <button id="showUserInfoButton" class="p-4 bg-gray-50 hover:bg-gray-100 rounded-lg border border-gray-200 transition-colors">
                    <div class="flex items-center space-x-3">
                        <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.121 17.804A7 7 0 0112 15a7 7 0 016.879 2.804M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        <div class="text-left">
                            <div class="font-medium text-gray-900">用户信息</div>
                            <div class="text-sm text-gray-600">查看资料与邮件提醒</div>
                        </div>
                    </div>
                </button>
                
                <button id="showTicketsButton" class="p-4 bg-green-50 hover:bg-green-100 rounded-lg border border-green-200 transition-colors">
                    <div class="flex items-center space-x-3">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <div class="text-left">
                            <div class="font-medium text-green-900">工单系统</div>
                            <div class="text-sm text-green-600">提交问题工单和查看进度</div>
                        </div>
                    </div>
                </button>
                <button id="showBindButton" class="p-4 bg-indigo-50 hover:bg-indigo-100 rounded-lg border border-indigo-200 transition-colors">
                    <div class="flex items-center space-x-3">
                        <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                        </svg>
                        <div class="text-left">
                            <div class="font-medium text-indigo-900">绑定激活码</div>
                            <div class="text-sm text-indigo-600">将激活码绑定到新的群聊</div>
                        </div>
                    </div>
                </button>

                <!-- 按次付费功能按钮（只在有激活绑定时显示） -->
                <button id="showPayPerUseButton" class="p-4 bg-green-50 hover:bg-green-100 rounded-lg border border-green-200 transition-colors hidden pay-per-use-button">
                    <div class="flex items-center space-x-3">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                        <div class="text-left">
                            <div class="font-medium text-green-900">按次付费查询</div>
                            <div class="text-sm text-green-600">查看按次付费余额和使用兑换码</div>
                        </div>
                    </div>
                </button>
            </div>
        </div>

        <!-- 我的绑定区域 -->
        <div class="form-section" id="bindingsSection">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-semibold text-slate-700">我的绑定</h2>
                <button id="backToMainButton" class="text-blue-600 hover:text-blue-800 font-medium">返回主菜单</button>
            </div>
            
            <div id="bindingsList" class="space-y-4">
                <!-- 绑定记录将在这里动态显示 -->
            </div>
            
            <div id="noBindingsMessage" class="text-center py-8 text-gray-500 hidden">
                <svg class="w-16 h-16 mx-auto text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <p class="text-lg">您还没有任何绑定记录</p>
                <p class="text-sm">请先绑定激活码到群聊</p>
            </div>
        </div>

        <!-- 工单系统区域 -->
        <div class="form-section" id="ticketsSection">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-semibold text-slate-700">工单系统</h2>
                <button id="backToMainFromTicketsButton" class="text-blue-600 hover:text-blue-800 font-medium">返回主菜单</button>
            </div>
            
            <!-- 工单操作按钮 -->
            <div class="flex flex-wrap gap-4 mb-6">
                <button id="createTicketButton" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    创建新工单
                </button>
                <button id="refreshTicketsButton" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    刷新列表
                </button>
            </div>
            
            <!-- 工单列表 -->
            <div id="ticketsList" class="space-y-4">
                <!-- 工单列表将在这里动态显示 -->
            </div>
            
            <div id="noTicketsMessage" class="text-center py-8 text-gray-500 hidden">
                <svg class="w-16 h-16 mx-auto text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <p class="text-lg">您还没有创建任何工单</p>
                <p class="text-sm">点击"创建新工单"开始</p>
            </div>
        </div>

        <!-- 创建工单表单 -->
        <div class="form-section" id="createTicketSection">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-semibold text-slate-700">创建新工单</h2>
                <button id="backToTicketsButton" class="text-blue-600 hover:text-blue-800 font-medium">返回工单列表</button>
            </div>
            
            <form id="createTicketForm" class="space-y-5">
                <div>
                    <label for="ticketTitle" class="block text-sm font-medium text-gray-700 mb-1">工单标题 *</label>
                    <input type="text" id="ticketTitle" name="ticketTitle" placeholder="请简要描述您的问题" required maxlength="100" autocomplete="off"
                        class="mt-1 block w-full px-4 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors duration-300 bg-white/70">
                    <p class="mt-1 text-xs text-gray-500">标题长度不能超过100个字符</p>
                </div>
                
                <div>
                    <label for="ticketType" class="block text-sm font-medium text-gray-700 mb-1">工单类型 *</label>
                    <select id="ticketType" name="ticketType" required
                        class="mt-1 block w-full px-4 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors duration-300 bg-white/70">
                        <option value="">请选择工单类型</option>
                    </select>
                </div>
                
                <div>
                    <label for="ticketPriority" class="block text-sm font-medium text-gray-700 mb-1">优先级</label>
                    <select id="ticketPriority" name="ticketPriority"
                        class="mt-1 block w-full px-4 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors duration-300 bg-white/70">
                        <option value="low">低</option>
                        <option value="normal" selected>普通</option>
                        <option value="high">高</option>
                    </select>
                </div>
                
                <div>
                    <label for="ticketGroup" class="block text-sm font-medium text-gray-700 mb-1">相关群号（可选）</label>
                    <input type="text" id="ticketGroup" name="ticketGroup" placeholder="如果是群聊相关问题，请输入群号" autocomplete="off"
                        class="mt-1 block w-full px-4 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors duration-300 bg-white/70">
                </div>
                
                <div>
                    <label for="ticketContent" class="block text-sm font-medium text-gray-700 mb-1">问题描述 *</label>
                    <textarea id="ticketContent" name="ticketContent" rows="6" placeholder="请详细描述您遇到的问题，支持Markdown语法" required maxlength="2000" autocomplete="off"
                        class="mt-1 block w-full px-4 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors duration-300 bg-white/70 resize-none"></textarea>
                    <p class="mt-1 text-xs text-gray-500">支持Markdown语法，内容长度不能超过2000个字符</p>
                </div>
                
                <div class="flex flex-col sm:flex-row sm:space-x-3 space-y-3 sm:space-y-0 pt-2">
                    <button type="submit" id="submitTicketButton"
                        class="w-full sm:w-auto flex-grow bg-green-600 hover:bg-green-700 text-white font-semibold py-2.5 px-4 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 ease-in-out transform hover:-translate-y-0.5 flex items-center justify-center">
                        <span class="button-text">提交工单</span>
                        <span class="loader hidden"></span>
                    </button>
                    <button type="button" id="cancelTicketButton"
                        class="w-full sm:w-auto bg-gray-200 hover:bg-gray-300 text-gray-700 font-semibold py-2.5 px-4 rounded-lg shadow hover:shadow-md transition-all duration-300 ease-in-out">
                        取消
                    </button>
                </div>
            </form>
        </div>

        <!-- 工单详情区域 -->
        <div class="form-section" id="ticketDetailSection">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-semibold text-slate-700">工单详情</h2>
                <button id="backToTicketsFromDetailButton" class="text-blue-600 hover:text-blue-800 font-medium">返回工单列表</button>
            </div>
            
            <div id="ticketDetailContent" class="space-y-6">
                <!-- 工单详情将在这里动态显示 -->
            </div>
        </div>

        <!-- 工单回复区域 -->
        <div class="form-section" id="ticketReplySection">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-semibold text-slate-700">工单回复</h2>
                <button id="backToTicketDetailButton" class="text-blue-600 hover:text-blue-800 font-medium">返回工单详情</button>
            </div>
            
            <div id="ticketReplies" class="space-y-4 mb-6">
                <!-- 工单回复将在这里动态显示 -->
            </div>
            
            <form id="replyForm" class="space-y-4">
                <div>
                    <label for="replyContent" class="block text-sm font-medium text-gray-700 mb-1">回复内容</label>
                    <textarea id="replyContent" name="replyContent" rows="4" placeholder="请输入您的回复，支持Markdown语法" maxlength="2000" autocomplete="off"
                        class="mt-1 block w-full px-4 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors duration-300 bg-white/70 resize-none"></textarea>
                    <p class="mt-1 text-xs text-gray-500">支持Markdown语法，内容长度不能超过2000个字符</p>
                </div>
                
                <div class="flex flex-col sm:flex-row sm:space-x-3 space-y-3 sm:space-y-0">
                    <button type="submit" id="submitReplyButton"
                        class="w-full sm:w-auto flex-grow bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2.5 px-4 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 ease-in-out transform hover:-translate-y-0.5 flex items-center justify-center">
                        <span class="button-text">提交回复</span>
                        <span class="loader hidden"></span>
                    </button>
                    <button type="button" id="closeTicketButton"
                        class="w-full sm:w-auto bg-red-600 hover:bg-red-700 text-white font-semibold py-2.5 px-4 rounded-lg shadow hover:shadow-md transition-all duration-300 ease-in-out">
                        关闭工单
                    </button>
                </div>
            </form>
        </div>

        <!-- 注册表单部分 -->
        <div class="form-section" id="registerSection">
            <h2 class="text-2xl font-semibold text-center text-slate-700 mb-6">用户注册</h2>
            <form id="registerForm" class="space-y-5">
                <div>
                    <label for="registerUsername" class="block text-sm font-medium text-gray-700">用户名</label>
                    <input type="text" id="registerUsername" name="username" required minlength="3" maxlength="20" autocomplete="username"
                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                </div>
                <div>
                    <label for="registerEmail" class="block text-sm font-medium text-gray-700">电子邮箱</label>
                    <input type="email" id="registerEmail" name="email" required autocomplete="email"
                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                </div>
                <!-- 邮箱验证码（在开启开关时显示） -->
                <div id="registerCaptchaRow" class="hidden">
                    <label class="block text-sm font-medium text-gray-700">邮箱验证码</label>
                    <div class="mt-1 flex gap-2">
                        <input type="text" id="registerEmailCaptcha" placeholder="请输入邮箱验证码"
                            class="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        <button type="button" id="sendRegisterCaptchaButton"
                            class="px-3 py-2 bg-gray-700 hover:bg-gray-800 text-white rounded-md">获取验证码</button>
                    </div>
                    <p id="registerCaptchaHint" class="mt-1 text-xs text-gray-500">开启“注册需邮箱验证”后需要输入验证码</p>
                </div>
                <!-- 添加QQ号输入框 -->
                <div>
                    <label for="registerQQ" class="block text-sm font-medium text-gray-700">QQ号</label>
                    <input type="text" id="registerQQ" name="qq" required pattern="[0-9]{5,11}" autocomplete="off"
                        placeholder="请输入您的QQ号"
                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    <p class="mt-1 text-xs text-gray-500">QQ号将用于验证机器人加群权限</p>
                </div>
                <div>
                    <label for="registerPassword" class="block text-sm font-medium text-gray-700">密码</label>
                    <input type="password" id="registerPassword" name="password" required minlength="6" autocomplete="new-password"
                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                </div>
                <div>
                    <label for="registerConfirmPassword" class="block text-sm font-medium text-gray-700">确认密码</label>
                    <input type="password" id="registerConfirmPassword" name="confirmPassword" required minlength="6" autocomplete="new-password"
                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                </div>
                <div class="flex flex-col sm:flex-row sm:space-x-3 space-y-3 sm:space-y-0 pt-2">
                    <button type="submit" id="registerButton"
                        class="w-full sm:w-auto flex-grow bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2.5 px-4 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 ease-in-out transform hover:-translate-y-0.5 flex items-center justify-center">
                        <span class="button-text">注册</span>
                        <span class="loader hidden"></span>
                    </button>
                    <button type="button" id="backFromRegister"
                        class="w-full sm:w-auto bg-gray-200 hover:bg-gray-300 text-gray-700 font-semibold py-2.5 px-4 rounded-lg shadow hover:shadow-md transition-all duration-300 ease-in-out">
                        返回
                    </button>
                </div>
                <div class="text-center text-sm text-gray-600 mt-4">
                    <p>已有账户？<a href="#" id="switchToLogin" class="text-indigo-600 hover:text-indigo-800 font-medium">立即登录</a></p>
                </div>
            </form>
        </div>

        <!-- 用户面板部分 -->
        <div class="form-section" id="userPanel">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-semibold text-slate-700">用户中心</h2>
                <div class="flex items-center">
                    <span id="userWelcome" class="text-sm text-gray-600 mr-4">欢迎，用户</span>
                    <button id="logoutButton" class="text-sm text-red-600 hover:text-red-800 font-medium">退出登录</button>
                </div>
            </div>
            <div class="bg-gray-100 rounded-lg p-4 mb-6">
                <div class="flex items-center justify-between mb-3">
                    <h3 class="font-medium text-gray-800">账户信息</h3>
                    <button onclick="showChangePasswordModal()" 
                            class="text-sm bg-blue-600 hover:bg-blue-700 text-white font-medium py-1 px-3 rounded transition-colors">
                        修改密码
                    </button>
                </div>
                <div class="space-y-2 text-sm">
                    <p><span class="text-gray-600">用户名:</span> <span id="profileUsername" class="font-medium">-</span></p>
                    <p><span class="text-gray-600">邮箱:</span> <span id="profileEmail" class="font-medium">-</span></p>
                    <p><span class="text-gray-600">QQ:</span> <span id="profileQQ" class="font-medium">-</span></p>
                    <p><span class="text-gray-600">注册时间:</span> <span id="profileRegisterTime" class="font-medium">-</span></p>
                </div>
            </div>
            
            <!-- 激活记录列表部分 -->
            <div class="mb-6">
                <div class="flex items-center justify-between mb-3">
                    <h3 class="font-medium text-gray-800">我的激活记录</h3>
                    <div id="ordersLoading" class="loader"></div>
                </div>
                <div id="userOrdersList" class="space-y-3 mt-3">
                    <!-- 激活记录列表将通过JavaScript动态添加 -->
                    <div class="text-center text-gray-500 py-4" id="noOrdersMessage">加载中...</div>
                </div>
            </div>
            
            <div class="space-y-4">
                <button id="userPanelBindButton"
                    class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 ease-in-out transform hover:-translate-y-0.5 flex items-center justify-center space-x-2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z"
                            clip-rule="evenodd" />
                    </svg>
                    <span>绑定新激活码</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 操作结果模态框 -->
    <div id="resultModal"
        class="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center p-4 z-50 hidden transition-opacity duration-300 ease-in-out"
        aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div class="modal-content bg-white rounded-lg shadow-xl overflow-hidden max-w-lg w-full p-6 transform transition-all duration-300 ease-out">
            <div class="flex justify-between items-start">
                <h3 id="modalTitle" class="text-xl font-semibold text-gray-900">操作结果</h3>
                <button type="button" id="closeModalButton" class="text-gray-400 hover:text-gray-600 transition-colors">
                    <span class="sr-only">关闭</span>
                    <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="mt-4">
                <div id="modalBody" class="text-sm text-gray-700 space-y-2">
                </div>
                <div id="modalError" class="mt-3 text-sm text-red-600 font-medium hidden">
                </div>
            </div>
            <div class="mt-6 flex justify-end">
                <button type="button" id="modalOkButton"
                    class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm transition-colors duration-300">
                    好的
                </button>
            </div>
        </div>
    </div>

    <!-- 功能黑名单管理模态框 -->
    <div id="featureBlacklistModal"
        class="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center p-4 z-50 hidden transition-opacity duration-300 ease-in-out"
        aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div class="modal-content bg-white rounded-lg shadow-xl overflow-hidden max-w-2xl w-full p-6 transform transition-all duration-300 ease-out">
            <div class="flex justify-between items-start mb-4">
                <h3 class="text-xl font-semibold text-gray-900">功能黑名单管理</h3>
                <button type="button" id="closeFeatureModal" class="text-gray-400 hover:text-gray-600 transition-colors">
                    <span class="sr-only">关闭</span>
                    <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            
            <div class="mb-4">
                <p class="text-sm text-gray-600">群号: <span id="blacklistGroupNumber" class="font-medium"></span></p>
            </div>
            
            <div id="featureListLoading" class="text-center py-4">
                <div class="loader"></div>
                <p class="text-sm text-gray-500 mt-2">加载中...</p>
            </div>
            
            <div id="featureList" class="space-y-3 hidden max-h-96 overflow-y-auto">
                <!-- 功能列表将在这里动态生成 -->
            </div>
            
            <div class="mt-6 flex justify-end">
                <button type="button" id="closeFeatureModalButton"
                    class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-2 px-4 rounded-md shadow-sm transition-colors duration-300">
                    关闭
                </button>
            </div>
        </div>
    </div>

    <!-- 修改密码模态框 -->
    <div id="changePasswordModal"
        class="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center p-4 z-50 hidden transition-opacity duration-300 ease-in-out"
        aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div class="modal-content bg-white rounded-lg shadow-xl overflow-hidden max-w-md w-full p-6 transform transition-all duration-300 ease-out">
            <div class="flex justify-between items-start mb-4">
                <h3 class="text-xl font-semibold text-gray-900">修改密码</h3>
                <button type="button" id="closePasswordModal" class="text-gray-400 hover:text-gray-600 transition-colors">
                    <span class="sr-only">关闭</span>
                    <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            
            <form id="changePasswordForm" class="space-y-4">
                <div>
                    <label for="oldPassword" class="block text-sm font-medium text-gray-700 mb-1">旧密码</label>
                    <input type="password" id="oldPassword" name="oldPassword" required autocomplete="current-password"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                </div>
                
                <div>
                    <label for="newPassword" class="block text-sm font-medium text-gray-700 mb-1">新密码</label>
                    <input type="password" id="newPassword" name="newPassword" required minlength="6" autocomplete="new-password"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                    <p class="mt-1 text-xs text-gray-500">密码长度至少6位</p>
                </div>
                
                <div>
                    <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-1">确认新密码</label>
                    <input type="password" id="confirmPassword" name="confirmPassword" required autocomplete="new-password"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                </div>
                
                <div>
                    <label for="qqVerify" class="block text-sm font-medium text-gray-700 mb-1">验证QQ号</label>
                    <input type="text" id="qqVerify" name="qqVerify" required autocomplete="off"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        placeholder="请输入绑定的QQ号进行验证">
                </div>
                
                <div id="changePasswordError" class="text-sm text-red-600 min-h-[1.25rem]" aria-live="polite"></div>
                
                <div class="flex justify-end space-x-3 pt-3">
                    <button type="button" id="cancelPasswordChange"
                        class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors">
                        取消
                    </button>
                    <button type="submit" id="submitPasswordChange"
                        class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors flex items-center">
                        <span class="button-text">确认修改</span>
                        <span class="loader hidden ml-2"></span>
                    </button>
                </div>
            </form>
        </div>

        <!-- 按次付费查询界面 -->
        <div class="form-section" id="payPerUseSection" style="background: white; padding: 30px; margin: 20px; border: 2px solid #e5e7eb; border-radius: 8px; min-height: 400px;">
            <div style="text-align: center; margin-bottom: 30px;">
                <h2 style="font-size: 28px; color: #374151; margin-bottom: 20px;">🔥 按次付费功能查询 🔥</h2>
                <button onclick="showUserMainSection()" style="background: #6b7280; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 14px;">
                    ← 返回主页
                </button>
            </div>

            <!-- 按次付费子标签 -->
            <div style="display: flex; border-bottom: 2px solid #e5e7eb; margin-bottom: 20px;">
                <button id="payPerUseBalanceTab" style="flex: 1; padding: 12px; text-align: center; background: #3b82f6; color: white; border: none; cursor: pointer; font-weight: bold;">
                    余额查询
                </button>
                <button id="payPerUseLogsTab" style="flex: 1; padding: 12px; text-align: center; background: #e5e7eb; color: #374151; border: none; cursor: pointer;">
                    使用记录
                </button>
                <button id="payPerUseRedeemTab" style="flex: 1; padding: 12px; text-align: center; background: #e5e7eb; color: #374151; border: none; cursor: pointer;">
                    兑换码使用
                </button>
            </div>

            <!-- 余额查询内容 -->
            <div id="payPerUseBalanceContent" style="display: block; background: #f9fafb; padding: 20px; border-radius: 8px; min-height: 200px;">
                <div id="payPerUseBalanceList">
                    <!-- 动态填充余额数据 -->
                    <div style="text-align: center; padding: 40px; color: #6b7280;">
                        <p style="font-size: 18px; margin-bottom: 10px;">📊 余额查询</p>
                        <p>暂无按次付费功能余额数据</p>
                    </div>
                </div>
            </div>

            <!-- 使用记录内容 -->
            <div id="payPerUseLogsContent" style="display: none; background: #f9fafb; padding: 20px; border-radius: 8px; min-height: 200px;">
                <div style="text-align: center; padding: 40px; color: #6b7280;">
                    <p style="font-size: 18px; margin-bottom: 10px;">📋 使用记录</p>
                    <p>暂无按次付费功能使用记录</p>
                </div>
            </div>

            <!-- 兑换码使用内容 -->
            <div id="payPerUseRedeemContent" style="display: none; background: #f9fafb; padding: 20px; border-radius: 8px; min-height: 200px;">
                <div style="text-align: center; padding: 40px; color: #6b7280;">
                    <p style="font-size: 18px; margin-bottom: 10px;">🎫 兑换码使用</p>
                    <p>暂无按次付费兑换码功能</p>
                </div>
            </div>
        </div>

    <!-- 操作结果模态框 -->
    <div id="resultModal"
                        <input type="text" id="payPerUseLogsGroupFilter" placeholder="群号筛选"
                               class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <input type="date" id="payPerUseLogsDateFrom" class="px-3 py-2 border border-gray-300 rounded-md">
                        <input type="date" id="payPerUseLogsDateTo" class="px-3 py-2 border border-gray-300 rounded-md">
                        <button id="searchPayPerUseLogsUser" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">
                            搜索
                        </button>
                    </div>
                    <div id="payPerUseLogsList" class="space-y-2 max-h-96 overflow-y-auto">
                        <!-- 动态填充使用记录 -->
                    </div>
                    <div class="mt-4 flex justify-between items-center">
                        <div id="payPerUseLogsPaginationInfo" class="text-sm text-gray-600"></div>
                        <div class="flex gap-2">
                            <button id="payPerUseLogsPrevPage" class="px-3 py-1 bg-gray-200 hover:bg-gray-300 rounded">上一页</button>
                            <button id="payPerUseLogsNextPage" class="px-3 py-1 bg-gray-200 hover:bg-gray-300 rounded">下一页</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 兑换码使用内容 -->
            <div id="payPerUseRedeemContent" class="pay-per-use-user-content hidden">
                <div class="text-center mb-6">
                    <p class="text-gray-600 mb-4">登录后可使用按次付费兑换码为您的群聊充值</p>
                    <button id="loginForPayPerUseRedeem" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg">
                        登录使用兑换码
                    </button>
                </div>
                <div id="payPerUseRedeemData" class="hidden">
                    <form id="payPerUseRedeemForm" class="space-y-4">
                        <div>
                            <label for="redeemCode" class="block text-sm font-medium text-gray-700 mb-1">兑换码</label>
                            <input type="text" id="redeemCode" name="redeemCode" placeholder="请输入您的按次付费兑换码"
                                   required autocomplete="off"
                                   class="w-full px-4 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors duration-300 bg-white/70">
                            <p class="mt-1 text-xs text-gray-500">兑换码可以是管理员分配的按次付费专用兑换码</p>
                        </div>
                        <div>
                            <label for="redeemGroupNumber" class="block text-sm font-medium text-gray-700 mb-1">绑定群号</label>
                            <input type="text" id="redeemGroupNumber" name="redeemGroupNumber" placeholder="请输入您要充值的群号"
                                   required autocomplete="off"
                                   class="w-full px-4 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors duration-300 bg-white/70">
                            <p class="mt-1 text-xs text-gray-500">只能为您拥有的群聊充值按次付费功能</p>
                        </div>
                        <div class="flex flex-col sm:flex-row sm:space-x-3 space-y-3 sm:space-y-0 pt-2">
                            <button type="submit" id="redeemButton"
                                    class="w-full sm:w-auto flex-grow bg-green-600 hover:bg-green-700 text-white font-semibold py-2.5 px-4 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 ease-in-out transform hover:-translate-y-0.5 flex items-center justify-center">
                                <span class="button-text">确认兑换</span>
                                <span class="loader hidden"></span>
                            </button>
                        </div>
                    </form>

                    <!-- 用户拥有的群聊列表 -->
                    <div id="userGroupsList" class="mt-6">
                        <h3 class="text-lg font-medium text-gray-800 mb-3">您的群聊列表</h3>
                        <div id="userGroupsContent" class="space-y-2">
                            <!-- 动态填充用户拥有的群聊 -->
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex justify-end mt-6">
                <button type="button" id="backFromPayPerUse"
                    class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors">
                    返回
                </button>
            </div>
        </div>
    </div>

    <script src="js/app.js?v=11"></script>
    <!-- 图片预览模态框（用户端与后台统一体验） -->
    <div id="imagePreviewModal" class="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-[200] hidden">
        <div class="relative max-w-5xl max-h-[90vh] w-[90vw]">
            <button id="closeImagePreview" class="absolute -top-10 right-0 text-white bg-black/50 hover:bg-black/70 px-3 py-1 rounded">关闭</button>
            <img id="imagePreviewImg" src="" alt="预览" class="w-full h-auto max-h-[90vh] object-contain rounded shadow-lg" />
        </div>
    </div>
</body>
</html>
