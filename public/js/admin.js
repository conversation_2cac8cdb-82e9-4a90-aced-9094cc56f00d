// 全局变量
let currentAdminPassword = '';
let bindingsRefreshTimer = null;

// 加载背景图片
async function loadBackground() {
    try {
        const response = await fetch('/api/background');
        const data = await response.json();

        if (data.success && data.url) {
            document.body.style.backgroundImage = `url(${data.url})`;
            document.body.classList.add('has-background');
            console.log('背景图片加载成功:', data.source);
        }
    } catch (error) {
        console.error('加载背景图片失败:', error);
    }
}



// DOM 元素
const passwordSection = document.getElementById('password-section');
const mainPanel = document.getElementById('main-panel');
const passwordForm = document.getElementById('password-form');
const adminPasswordInput = document.getElementById('admin-password');
const passwordError = document.getElementById('password-error');
const loginButton = document.getElementById('loginButton');
const changeBackgroundBtn = document.getElementById('changeBackgroundBtn');

// Tab相关元素
const bindingsTab = document.getElementById('bindingsTab');
const codesTab = document.getElementById('codesTab');
const robotsTab = document.getElementById('robotsTab');
// const groupsTab = document.getElementById('groupsTab'); // 已移至独立模块
// const featureWhitelistTab = document.getElementById('featureWhitelistTab'); // 已移至 feature-whitelist.js 模块
// const featureBlacklistTab = document.getElementById('featureBlacklistTab'); // 已移至 feature-blacklist.js 模块
const ticketsTab = document.getElementById('ticketsTab');
const ticketTypesTab = document.getElementById('ticketTypesTab');
const settingsTab = document.getElementById('settingsTab');
// const tiersTab = document.getElementById('tiersTab'); // 已移至 tiers.js 模块
const systemLogsTab = document.getElementById('systemLogsTab');
// const payPerUseTab = document.getElementById('payPerUseTab'); // 已移至 pay-per-use.js 模块

const bindingsContent = document.getElementById('bindingsContent');
const codesContent = document.getElementById('codesContent');
const robotsContent = document.getElementById('robotsContent');
// const groupsContent = document.getElementById('groupsContent'); // 已移至独立模块
// const featureWhitelistContent = document.getElementById('featureWhitelistContent'); // 已移至独立模块
const ticketsContent = document.getElementById('ticketsContent');
const ticketTypesContent = document.getElementById('ticketTypesContent');
const settingsContent = document.getElementById('settingsContent');
// const tiersContent = document.getElementById('tiersContent'); // 已移至 tiers.js 模块
const systemLogsContent = document.getElementById('systemLogsContent');
// const payPerUseContent = document.getElementById('payPerUseContent'); // 已移至 pay-per-use.js 模块
// const systemLogsContainer = document.getElementById('systemLogsContainer'); // 已移至 system-logs.js 模块
// const systemLogsDays = document.getElementById('systemLogsDays'); // 已移至 system-logs.js 模块
// const refreshSystemLogsButton = document.getElementById('refreshSystemLogsButton'); // 已移至 system-logs.js 模块

// 表格和加载元素



// 机器人管理元素 - 已移至独立模块

// 群聊管理元素 - 已移至独立模块



// 模态框相关元素

// Toast 通知
const toastNotification = document.getElementById('toast-notification');
const toastMessage = document.getElementById('toast-message');

// 用户管理元素
// 用户管理相关元素已移至users.js模块



// 系统设置元素 - 已移至 settings.js 模块

// 绑定列表刷新定时器
// let selectedGroupIds = new Set(); // 已移至独立模块
// let tiersConfig = []; // 已移至 tiers.js 模块
// let settingsCache = {}; // 已移至 settings.js 模块

// 辅助函数
function escapeHtml(unsafe) {
    if (typeof unsafe !== 'string') return unsafe;
    return unsafe
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
}
function showToast(message, type = 'info') {
    if (!toastMessage || !toastNotification) return;

    toastMessage.textContent = message;

    // 重置所有样式类
    toastNotification.className = 'ios-toast';

    // 根据类型设置样式
    if (type === 'success') {
        toastNotification.classList.add('success');
    } else if (type === 'error') {
        toastNotification.classList.add('error');
    }

    // 显示通知
    toastNotification.classList.add('show');

    // 3秒后自动隐藏
    setTimeout(() => {
        toastNotification.classList.remove('show');
    }, 3000);
}

function showModal(title, content, confirmText = '确定', cancelText = '取消', onConfirm = null, onCancel = null) {
    // 创建模态框HTML
    const modalHtml = `
        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" id="custom-modal">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">${title}</h3>
                    <div class="text-gray-700 mb-6">${content}</div>
                    <div class="flex justify-end space-x-3">
                        <button id="modal-cancel" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">${cancelText}</button>
                        <button id="modal-confirm" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md">${confirmText}</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    const modal = document.getElementById('custom-modal');
    const cancelBtn = document.getElementById('modal-cancel');
    const confirmBtn = document.getElementById('modal-confirm');

    // 绑定事件
    cancelBtn.addEventListener('click', () => {
        modal.remove();
        if (onCancel) onCancel();
    });

    confirmBtn.addEventListener('click', () => {
        modal.remove();
        if (onConfirm) onConfirm();
    });

    // ESC键关闭
    document.addEventListener('keydown', function escHandler(e) {
        if (e.key === 'Escape') {
            modal.remove();
            document.removeEventListener('keydown', escHandler);
            if (onCancel) onCancel();
        }
    });
}

function showLoading(button) {
    if (!button) return;
    try {
        const buttonText = button.querySelector('.button-text');
        const loader = button.querySelector('.admin-loader');
        if (buttonText && loader) {
            buttonText.classList.add('hidden');
            loader.classList.remove('hidden');
        }
        button.disabled = true;
        button.classList.add('opacity-70', 'cursor-not-allowed');
    } catch (e) {
        // 兼容无子元素按钮
        button.disabled = true;
    }
}

function hideLoading(button) {
    if (!button) return;
    try {
        const buttonText = button.querySelector('.button-text');
        const loader = button.querySelector('.admin-loader');
        if (buttonText && loader) {
            buttonText.classList.remove('hidden');
            loader.classList.add('hidden');
        }
        button.disabled = false;
        button.classList.remove('opacity-70', 'cursor-not-allowed');
    } catch (e) {
        button.disabled = false;
    }
}

// Tab切换函数
function switchTab(tabName) {
    // 隐藏所有Tab内容
    const allContents = document.querySelectorAll('.tab-content');
    allContents.forEach(content => {
        content.classList.add('hidden');
    });

    // 移除所有Tab按钮的active类
    const allTabs = document.querySelectorAll('[id*="Tab"]');
    allTabs.forEach(tab => {
        tab.classList.remove('active');
    });

    // 显示选中的Tab内容
    let targetContentId = tabName + 'Content';

    // 特殊处理一些Tab名称
    switch (tabName) {
        case 'system-logs':
            targetContentId = 'systemLogsContent';
            break;
        case 'feature-blacklist':
            targetContentId = 'featureBlacklistContent';
            break;
        case 'feature-whitelist':
            targetContentId = 'featureWhitelistContent';
            break;
        case 'ticket-types':
            targetContentId = 'ticketTypesContent';
            break;
        case 'pay-per-use':
            targetContentId = 'payPerUseContent';
            break;
    }

    const targetContent = document.getElementById(targetContentId);
    if (targetContent) {
        targetContent.classList.remove('hidden');
        console.log(`显示内容区域: ${targetContentId}`);
    } else {
        console.error(`未找到内容区域: ${targetContentId}`);
    }

    // 激活选中的Tab按钮
    let targetTabId = tabName + 'Tab';

    // 特殊处理一些Tab名称
    switch (tabName) {
        case 'system-logs':
            targetTabId = 'systemLogsTab';
            break;
        case 'feature-blacklist':
            targetTabId = 'featureBlacklistTab';
            break;
        case 'feature-whitelist':
            targetTabId = 'featureWhitelistTab';
            break;
        case 'ticket-types':
            targetTabId = 'ticketTypesTab';
            break;
        case 'pay-per-use':
            targetTabId = 'payPerUseTab';
            break;
    }

    const targetTab = document.getElementById(targetTabId);
    if (targetTab) {
        targetTab.classList.add('active');
        console.log(`激活Tab按钮: ${targetTabId}`);
    } else {
        console.error(`未找到Tab按钮: ${targetTabId}`);
    }

    // 根据Tab名称执行特定逻辑
    switch (tabName) {
        case 'bindings':
            fetchBindings();
            break;
        case 'codes':
            if (typeof window.fetchActivationCodes === 'function') {
                window.fetchActivationCodes();
            }
            break;
        case 'robots':
            if (window.RobotsModule && window.RobotsModule.fetchRobots) {
                window.RobotsModule.fetchRobots();
            }
            break;
        case 'users':
            if (typeof onUsersTabClick === 'function') {
                onUsersTabClick();
            }
            break;
        case 'tiers':
            if (window.TiersModule) {
                window.TiersModule.loadTiersConfig();
            }
            break;
        case 'feature-blacklist':
            if (typeof onFeatureBlacklistTabClick === 'function') {
                onFeatureBlacklistTabClick();
            }
            break;
        case 'feature-whitelist':
            if (typeof onFeatureWhitelistTabClick === 'function') {
                onFeatureWhitelistTabClick();
            }
            break;
        case 'settings':
            if (window.SettingsModule && window.SettingsModule.onTabSwitch) {
                window.SettingsModule.onTabSwitch();
            }
            break;
        case 'system-logs':
            if (window.SystemLogsModule && window.SystemLogsModule.onTabSwitch) {
                window.SystemLogsModule.onTabSwitch('system-logs');
            }
            break;
        case 'tickets':
            if (window.TicketManagement && window.TicketManagement.onTabSwitch) {
                window.TicketManagement.onTabSwitch('tickets');
            }
            break;
        case 'ticket-types':
            if (window.TicketTypesModule) {
                window.TicketTypesModule.loadTicketTypes();
            }
            break;
        case 'pay-per-use':
            console.log('切换到按次付费标签页');
            // 按次付费模块会自动处理子标签切换和数据加载
            // 不需要额外的处理，按次付费模块会处理自己的Tab切换逻辑
            break;
    }
}

// 身份验证函数
async function verifyAdminPassword(password) {
    try {
        const response = await fetch('/admin/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ password: password })
        });

        if (response.ok) {
            const result = await response.json();
            if (result.success) {
                currentAdminPassword = password; // 保存密码到内存
                window.currentAdminPassword = password; // 设置全局密码供其他模块使用
                try { sessionStorage.setItem('adminPassword', password); } catch (e) { }
                // 验证成功后重新初始化档位管理模块
                if (window.TiersModule) {
                    console.log('登录成功，重新初始化档位管理模块');
                    window.TiersModule.init();
                    window.TiersModule.setGlobalVars(currentAdminPassword);
                    window.TiersModule.loadTiersConfig();
                }
                return true;
            }
        }
        return false;
    } catch (error) {
        console.error('验证密码时出错:', error);
        throw error;
    }
}

// 辅助函数
async function fetchAndApplyAdminTitle() {
    try {
        if (!currentAdminPassword) return;
        const resp = await fetch('/admin/settings', { headers: { 'X-Admin-Password': currentAdminPassword } });
        const result = await resp.json().catch(() => ({ success: false }));
        if (resp.ok && result && result.success) {
            const data = result.data || {};
            setAdminDocumentTitle(data.site_title || '');
        }
    } catch { }
}

function setAdminDocumentTitle(title) {
    if (title) {
        document.title = `${title} - 后台管理`;
    } else {
        document.title = '后台管理面板';
    }
}

// Tab切换事件监听器
if (bindingsTab) {
    bindingsTab.addEventListener('click', () => switchTab('bindings'));
}

if (codesTab) {
    codesTab.addEventListener('click', () => switchTab('codes'));
}

if (robotsTab) {
    robotsTab.addEventListener('click', () => switchTab('robots'));
}

if (ticketsTab) {
    ticketsTab.addEventListener('click', () => switchTab('tickets'));
}

if (ticketTypesTab) {
    ticketTypesTab.addEventListener('click', () => switchTab('ticket-types'));
}

if (settingsTab) {
    settingsTab.addEventListener('click', () => switchTab('settings'));
}

if (systemLogsTab) {
    systemLogsTab.addEventListener('click', () => switchTab('systemLogs'));
}

if (document.getElementById('tiersTab')) {
    document.getElementById('tiersTab').addEventListener('click', () => switchTab('tiers'));
}

if (document.getElementById('usersTab')) {
    document.getElementById('usersTab').addEventListener('click', () => switchTab('users'));
}

if (document.getElementById('featureBlacklistTab')) {
    document.getElementById('featureBlacklistTab').addEventListener('click', () => switchTab('feature-blacklist'));
}

if (document.getElementById('featureWhitelistTab')) {
    document.getElementById('featureWhitelistTab').addEventListener('click', () => switchTab('feature-whitelist'));
}

// payPerUseTab 事件监听器已移至 pay-per-use.js 模块

// 登录表单事件监听器
document.addEventListener('DOMContentLoaded', function () {
    // 确保所有模块都加载完成后才绑定登录事件
    setTimeout(() => {
        if (passwordForm) {
            passwordForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                const password = adminPasswordInput.value;

                showLoading(loginButton);
                passwordError.textContent = '';

                try {
                    const isValid = await verifyAdminPassword(password);
                    if (isValid) {
                        console.log('登录验证成功，开始切换界面');
                        passwordSection.classList.add('hidden');
                        mainPanel.classList.remove('hidden');
                        console.log('界面切换完成');

                        // 登录后应用标题
                        console.log('开始获取管理员标题');
                        try {
                            await fetchAndApplyAdminTitle();
                            console.log('管理员标题获取完成');
                        } catch (error) {
                            console.error('获取管理员标题失败:', error);
                        }

                        console.log('开始获取绑定列表');
                        try {
                            await fetchBindings();
                            console.log('绑定列表获取完成');
                        } catch (error) {
                            console.error('获取绑定列表失败:', error);
                        }

                        // 更新工单管理模块的管理员密码
                        if (window.TicketManagement && window.TicketManagement.setGlobalVars) {
                            window.TicketManagement.setGlobalVars(currentAdminPassword);
                        }
                        // 更新工单类型模块的管理员密码
                        if (window.TicketTypesModule && window.TicketTypesModule.setGlobalVars) {
                            window.TicketTypesModule.setGlobalVars(currentAdminPassword);
                        }
                        // 更新系统设置模块的管理员密码
                        if (window.SettingsModule && window.SettingsModule.setGlobalVars) {
                            window.SettingsModule.setGlobalVars(currentAdminPassword);
                        }
                        // 更新档位管理模块的管理员密码
                        if (window.TiersModule && window.TiersModule.setGlobalVars) {
                            window.TiersModule.setGlobalVars(currentAdminPassword);
                        }
                        // 更新系统日志模块的管理员密码
                        if (window.SystemLogsModule && window.SystemLogsModule.setGlobalVars) {
                            window.SystemLogsModule.setGlobalVars(currentAdminPassword);
                        }

                        // 更新用户管理模块的管理员密码
                        if (typeof window.currentAdminPassword !== 'undefined') {
                            window.currentAdminPassword = currentAdminPassword;
                        }

                        // 取消自动刷新 - 改为手动刷新以提升用户体验
                        if (bindingsRefreshTimer) {
                            clearInterval(bindingsRefreshTimer);
                            bindingsRefreshTimer = null;
                        }
                        // 不再设置自动刷新，用户可以通过刷新按钮手动刷新

                        // 系统日志相关事件绑定已移至 system-logs.js 模块
                    } else {
                        passwordError.textContent = '密码错误，请重试。';
                    }
                } catch (error) {
                    console.error('登录验证失败:', error);
                    passwordError.textContent = '验证失败，请稍后重试。';
                } finally {
                    hideLoading(loginButton);
                }
            });

            // 页面卸载时清理定时器
            window.addEventListener('beforeunload', () => {
                if (bindingsRefreshTimer) {
                    clearInterval(bindingsRefreshTimer);
                }
            });
        }
    }, 100); // 延迟100ms确保所有模块都加载完成
});

// ==================== 按次付费功能管理 ====================
// 按次付费功能已移至独立文件 pay-per-use.js

// ==================== 模块初始化 ====================
// 初始化所有模块
document.addEventListener('DOMContentLoaded', () => {
    // 等待绑定管理模块加载完成后初始化
    if (typeof initBindingsModule === 'function') {
        initBindingsModule();
    }

    // 等待兑换码管理模块加载完成后初始化
    if (typeof initCodesModule === 'function') {
        initCodesModule();
    }

    // 等待按次付费管理模块加载完成后初始化
    if (typeof initPayPerUseModule === 'function') {
        initPayPerUseModule();
    }

    // 初始化机器人模块
    if (window.RobotsModule) {
        window.RobotsModule.init();
        window.RobotsModule.setGlobalVars(currentAdminPassword);
    }

    // 初始化群聊列表模块
    if (window.GroupsModule) {
        window.GroupsModule.init();
    }

    // 初始化工单管理模块
    if (window.TicketManagement) {
        window.TicketManagement.init();
        window.TicketManagement.setGlobalVars(currentAdminPassword);
    }

    // 初始化工单类型模块
    if (window.TicketTypesModule) {
        window.TicketTypesModule.init();
        window.TicketTypesModule.setGlobalVars(currentAdminPassword);
    }

    // 初始化档位管理模块
    if (window.TiersModule) {
        window.TiersModule.init();
        window.TiersModule.setGlobalVars(currentAdminPassword);
    }

    // 初始化系统日志模块
    if (window.SystemLogsModule) {
        window.SystemLogsModule.init();
        window.SystemLogsModule.setGlobalVars(currentAdminPassword);
    }



    // 初始化系统设置模块
    if (window.SettingsModule) {
        window.SettingsModule.init();
        window.SettingsModule.setGlobalVars(currentAdminPassword);
    }

    // 初始化用户管理模块
    if (typeof initUsersModule === 'function') {
        // 先设置全局密码
        if (typeof window.currentAdminPassword !== 'undefined') {
            window.currentAdminPassword = currentAdminPassword;
        }
        initUsersModule();
    }

    // 功能黑名单和功能白名单模块会自动初始化

    // 加载背景图片
    loadBackground();

});

// 暴露switchTab函数给其他模块使用
window.switchTab = switchTab;