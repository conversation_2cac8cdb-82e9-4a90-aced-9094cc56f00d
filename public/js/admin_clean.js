// DOM 元素
const passwordSection = document.getElementById('password-section');
const mainPanel = document.getElementById('main-panel');
const passwordForm = document.getElementById('password-form');
const adminPasswordInput = document.getElementById('admin-password');
const passwordError = document.getElementById('password-error');
const loginButton = document.getElementById('loginButton');

// Tab相关元素
const bindingsTab = document.getElementById('bindingsTab');
const codesTab = document.getElementById('codesTab');
const robotsTab = document.getElementById('robotsTab');
// const groupsTab = document.getElementById('groupsTab'); // 已移至独立模块
// const featureWhitelistTab = document.getElementById('featureWhitelistTab'); // 已移至独立模块
const ticketsTab = document.getElementById('ticketsTab');
const ticketTypesTab = document.getElementById('ticketTypesTab');
const settingsTab = document.getElementById('settingsTab');
// const tiersTab = document.getElementById('tiersTab'); // 已移至 tiers.js 模块
const systemLogsTab = document.getElementById('systemLogsTab');
const payPerUseTab = document.getElementById('payPerUseTab');

const bindingsContent = document.getElementById('bindingsContent');
const codesContent = document.getElementById('codesContent');
const robotsContent = document.getElementById('robotsContent');
// const groupsContent = document.getElementById('groupsContent'); // 已移至独立模块
// const featureWhitelistContent = document.getElementById('featureWhitelistContent'); // 已移至独立模块
const ticketsContent = document.getElementById('ticketsContent');
const ticketTypesContent = document.getElementById('ticketTypesContent');
const settingsContent = document.getElementById('settingsContent');
// const tiersContent = document.getElementById('tiersContent'); // 已移至 tiers.js 模块
const systemLogsContent = document.getElementById('systemLogsContent');
const payPerUseContent = document.getElementById('payPerUseContent');
// const systemLogsContainer = document.getElementById('systemLogsContainer'); // 已移至 system-logs.js 模块
// const systemLogsDays = document.getElementById('systemLogsDays'); // 已移至 system-logs.js 模块
// const refreshSystemLogsButton = document.getElementById('refreshSystemLogsButton'); // 已移至 system-logs.js 模块

// 表格和加载元素



// 机器人管理元素 - 已移至独立模块

// 群聊管理元素 - 已移至独立模块



// 模态框相关元素

// Toast 通知
const toastNotification = document.getElementById('toast-notification');
const toastMessage = document.getElementById('toast-message');

// 用户管理元素
// 用户管理相关元素已移至users.js模块



// 系统设置元素 - 已移至 settings.js 模块

// 全局变量
let currentAdminPassword = null; // 使用内存变量代替 localStorage
// let selectedGroupIds = new Set(); // 已移至独立模块
// let tiersConfig = []; // 已移至 tiers.js 模块
// let settingsCache = {}; // 已移至 settings.js 模块

// 辅助函数
function escapeHtml(unsafe) {
    if (typeof unsafe !== 'string') return unsafe;
    return unsafe
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
}
function showToast(message, type = 'info') {
    if (!toastMessage || !toastNotification) return;

    toastMessage.textContent = message;

    // 重置所有样式类
    toastNotification.className = 'fixed bottom-5 right-5 py-3 px-5 rounded-lg shadow-lg transition-opacity duration-300 ease-in-out z-[100]';

    // 根据类型设置样式
    if (type === 'success') {
        toastNotification.classList.add('bg-green-600', 'text-white');
    } else if (type === 'error') {
        toastNotification.classList.add('bg-red-600', 'text-white');
    } else {
        toastNotification.classList.add('bg-gray-800', 'text-white');
    }

    // 显示通知
    toastNotification.style.opacity = '1';
    toastNotification.style.display = 'block';

    // 3秒后自动隐藏
    setTimeout(() => {
        toastNotification.style.opacity = '0';
        setTimeout(() => {
            toastNotification.style.display = 'none';
        }, 300);
    }, 3000);
}

function showModal(title, content, confirmText = '确定', cancelText = '取消', onConfirm = null, onCancel = null) {
    // 创建模态框HTML
    const modalHtml = `
        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" id="custom-modal">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">${title}</h3>
                    <div class="text-gray-700 mb-6">${content}</div>
                    <div class="flex justify-end space-x-3">
                        <button id="modal-cancel" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">${cancelText}</button>
                        <button id="modal-confirm" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md">${confirmText}</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    const modal = document.getElementById('custom-modal');
    const cancelBtn = document.getElementById('modal-cancel');
    const confirmBtn = document.getElementById('modal-confirm');

    // 绑定事件
    cancelBtn.addEventListener('click', () => {
        modal.remove();
        if (onCancel) onCancel();
    });

    confirmBtn.addEventListener('click', () => {
        modal.remove();
        if (onConfirm) onConfirm();
    });

    // ESC键关闭
    document.addEventListener('keydown', function escHandler(e) {
        if (e.key === 'Escape') {
            modal.remove();
            document.removeEventListener('keydown', escHandler);
            if (onCancel) onCancel();
        }
    });
}

function showLoading(button) {
    if (!button) return;
    try {
        const buttonText = button.querySelector('.button-text');
        const loader = button.querySelector('.admin-loader');
        if (buttonText && loader) {
            buttonText.classList.add('hidden');
            loader.classList.remove('hidden');
        }
        button.disabled = true;
        button.classList.add('opacity-70', 'cursor-not-allowed');
    } catch (e) {
        // 兼容无子元素按钮
        button.disabled = true;
    }
}

function hideLoading(button) {
    if (!button) return;
    try {
        const buttonText = button.querySelector('.button-text');
        const loader = button.querySelector('.admin-loader');
        if (buttonText && loader) {
            buttonText.classList.remove('hidden');
            loader.classList.add('hidden');
        }
        button.disabled = false;
        button.classList.remove('opacity-70', 'cursor-not-allowed');
    } catch (e) {
        button.disabled = false;
    }
}

// Tab切换函数
function switchTab(tabName) {
    // 隐藏所有Tab内容
    const allContents = document.querySelectorAll('.tab-content');
    allContents.forEach(content => {
        content.classList.add('hidden');
    });

    // 移除所有Tab按钮的active类
    const allTabs = document.querySelectorAll('[id*="Tab"]');
    allTabs.forEach(tab => {
        tab.classList.remove('active');
    });

    // 显示选中的Tab内容
    const targetContent = document.getElementById(tabName + 'Content');
    if (targetContent) {
        targetContent.classList.remove('hidden');
    }

    // 激活选中的Tab按钮
    const targetTab = document.getElementById(tabName + 'Tab');
    if (targetTab) {
        targetTab.classList.add('active');
    }
}

// 身份验证函数
async function verifyAdminPassword(password) {
    try {
        const response = await fetch('/admin/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ password: password })
        });

        if (response.ok) {
            const result = await response.json();
            if (result.success) {
                currentAdminPassword = password; // 保存密码到内存
                window.currentAdminPassword = password; // 设置全局密码供其他模块使用
                try { sessionStorage.setItem('adminPassword', password); } catch (e) {}
                // 验证成功后重新初始化档位管理模块
                if (window.TiersModule) {
                    console.log('登录成功，重新初始化档位管理模块');
                    window.TiersModule.init();
                    window.TiersModule.setGlobalVars(currentAdminPassword);
                    window.TiersModule.loadTiersConfig();
                }
                return true;
            }
        }
        return false;
    } catch (error) {
        console.error('验证密码时出错:', error);
        throw error;
    }
}

// 辅助函数
async function fetchAndApplyAdminTitle() {
    try {
        if (!currentAdminPassword) return;
        const resp = await fetch('/admin/settings', { headers: { 'X-Admin-Password': currentAdminPassword } });
        const result = await resp.json().catch(() => ({ success: false }));
        if (resp.ok && result && result.success) {
            const data = result.data || {};
            setAdminDocumentTitle(data.site_title || '');
        }
    } catch {}
}

function setAdminDocumentTitle(title) {
    if (title) {
        document.title = `${title} - 后台管理`;
    } else {
        document.title = '后台管理面板';
    }
}

// Tab切换事件监听器
if (bindingsTab) {
    bindingsTab.addEventListener('click', () => switchTab('bindings'));
}

if (codesTab) {
    codesTab.addEventListener('click', () => switchTab('codes'));
}

if (robotsTab) {
    robotsTab.addEventListener('click', () => switchTab('robots'));
}

if (ticketsTab) {
    ticketsTab.addEventListener('click', () => switchTab('tickets'));
}

if (ticketTypesTab) {
    ticketTypesTab.addEventListener('click', () => switchTab('ticket-types'));
}

if (settingsTab) {
    settingsTab.addEventListener('click', () => switchTab('settings'));
}

if (systemLogsTab) {
    systemLogsTab.addEventListener('click', () => switchTab('systemLogs'));
}

if (payPerUseTab) {
    payPerUseTab.addEventListener('click', () => switchTab('pay-per-use'));
}

// 登录表单事件监听器
document.addEventListener('DOMContentLoaded', function() {
    // 确保所有模块都加载完成后才绑定登录事件
    setTimeout(() => {
        if (passwordForm) {
            passwordForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                const password = adminPasswordInput.value;

                showLoading(loginButton);
                passwordError.textContent = '';

                try {
                    const isValid = await verifyAdminPassword(password);
                    if (isValid) {
                        console.log('登录验证成功，开始切换界面');
                        passwordSection.classList.add('hidden');
                        mainPanel.classList.remove('hidden');
                        console.log('界面切换完成');

                        // 登录后应用标题
                        console.log('开始获取管理员标题');
                        try {
                            await fetchAndApplyAdminTitle();
                            console.log('管理员标题获取完成');
                        } catch (error) {
                            console.error('获取管理员标题失败:', error);
                        }

                        console.log('开始获取绑定列表');
                        try {
                            await fetchBindings();
                            console.log('绑定列表获取完成');
                        } catch (error) {
                            console.error('获取绑定列表失败:', error);
                        }

                        // 更新工单管理模块的管理员密码
                        if (window.TicketManagement && window.TicketManagement.setGlobalVars) {
                            window.TicketManagement.setGlobalVars(currentAdminPassword);
                        }
                        // 更新工单类型模块的管理员密码
                        if (window.TicketTypesModule && window.TicketTypesModule.setGlobalVars) {
                            window.TicketTypesModule.setGlobalVars(currentAdminPassword);
                        }
                        // 更新系统设置模块的管理员密码
                        if (window.SettingsModule && window.SettingsModule.setGlobalVars) {
                            window.SettingsModule.setGlobalVars(currentAdminPassword);
                        }
                        // 更新档位管理模块的管理员密码
                        if (window.TiersModule && window.TiersModule.setGlobalVars) {
                            window.TiersModule.setGlobalVars(currentAdminPassword);
                        }
                        // 更新系统日志模块的管理员密码
                        if (window.SystemLogsModule && window.SystemLogsModule.setGlobalVars) {
                            window.SystemLogsModule.setGlobalVars(currentAdminPassword);
                        }

                        // 启动定时刷新绑定列表（每30秒）
                        if (bindingsRefreshTimer) {
                            clearInterval(bindingsRefreshTimer);
                        }
                        bindingsRefreshTimer = setInterval(() => {
                            // 只在绑定管理Tab激活时刷新
                            if (bindingsTab.classList.contains('active')) {
                                fetchBindings();
                            }
                        }, 30000); // 30秒

                        // 系统日志相关事件绑定已移至 system-logs.js 模块
                    } else {
                        passwordError.textContent = '密码错误，请重试。';
                    }
                } catch (error) {
                    console.error('登录验证失败:', error);
                    passwordError.textContent = '验证失败，请稍后重试。';
                } finally {
                    hideLoading(loginButton);
                }
            });

            // 页面卸载时清理定时器
            window.addEventListener('beforeunload', () => {
                if (bindingsRefreshTimer) {
                    clearInterval(bindingsRefreshTimer);
                }
            });
        }
    }, 100); // 延迟100ms确保所有模块都加载完成
});

// ==================== 按次付费功能管理 ====================
// 按次付费功能已移至独立文件 pay-per-use.js

// ==================== 模块初始化 ====================
// 初始化所有模块
document.addEventListener('DOMContentLoaded', () => {
    // 等待绑定管理模块加载完成后初始化
    if (typeof initBindingsModule === 'function') {
        initBindingsModule();
    }

    // 等待兑换码管理模块加载完成后初始化
    if (typeof initCodesModule === 'function') {
        initCodesModule();
    }

    // 等待按次付费管理模块加载完成后初始化
    if (typeof initPayPerUseModule === 'function') {
        initPayPerUseModule();
    }
});

// 暴露switchTab函数给其他模块使用
window.switchTab = switchTab;