// 绑定管理模块
// 依赖：需要全局变量 tiersConfig, currentAdminPassword, showToast, showModal, showLoading, hideLoading

// DOM 元素 (这些变量在admin.js中定义，全局可用)
let bindingsTableBody;
let loadingMessage;
let noRecordsMessage;
let bindingsTable;
let bindingsSearchInput;
let bindingStatusFilter;
let bindingTierFilter;
let refreshBindingsButton;
let clearBindingsFiltersButton;
let bindingModal;
let bindingModalTitle;
let bindingForm;
let closeBindingModal;
let cancelBindingButton;
let saveBindingButton;
let bindingModalError;
let addBindingButton;
let checkExpirationsButton;

let originalBindingsData = [];

// 初始化绑定管理模块
function initBindingsModule() {
    // 获取DOM元素 (bindingsContent已在admin.js中定义)
    bindingsTableBody = document.getElementById('bindings-table-body');
    loadingMessage = document.getElementById('loadingMessage');
    noRecordsMessage = document.getElementById('noRecordsMessage');
    bindingsTable = document.getElementById('bindingsTable');
    bindingsSearchInput = document.getElementById('bindingsSearchInput');
    bindingStatusFilter = document.getElementById('bindingStatusFilter');
    bindingTierFilter = document.getElementById('bindingTierFilter');
    refreshBindingsButton = document.getElementById('refreshBindingsButton');
    clearBindingsFiltersButton = document.getElementById('clearBindingsFiltersButton');
    bindingModal = document.getElementById('bindingModal');
    bindingModalTitle = document.getElementById('bindingModalTitle');
    bindingForm = document.getElementById('bindingForm');
    closeBindingModal = document.getElementById('closeBindingModal');
    cancelBindingButton = document.getElementById('cancelBindingButton');
    saveBindingButton = document.getElementById('saveBindingButton');
    bindingModalError = document.getElementById('bindingModalError');
    addBindingButton = document.getElementById('addBindingButton');
    checkExpirationsButton = document.getElementById('checkExpirationsButton');

    // 绑定事件监听器
    if (refreshBindingsButton) {
        refreshBindingsButton.addEventListener('click', fetchBindings);
    }
    if (clearBindingsFiltersButton) {
        clearBindingsFiltersButton.addEventListener('click', clearBindingsFilters);
    }
    if (bindingsSearchInput) {
        bindingsSearchInput.addEventListener('input', applyBindingsFilters);
    }
    if (bindingStatusFilter) {
        bindingStatusFilter.addEventListener('change', applyBindingsFilters);
    }
    if (bindingTierFilter) {
        bindingTierFilter.addEventListener('change', applyBindingsFilters);
    }
    if (addBindingButton) {
        addBindingButton.addEventListener('click', openAddBindingModal);
    }

    if (bindingForm) {
        bindingForm.addEventListener('submit', saveBinding);
    }
    if (closeBindingModal) {
        closeBindingModal.addEventListener('click', () => bindingModal.classList.add('hidden'));
    }

    // 永久有效复选框事件
    const permanentCheckbox = document.getElementById('modalPermanentExpiration');
    const daysInput = document.getElementById('modalExpirationDays');
    if (permanentCheckbox && daysInput) {
        permanentCheckbox.addEventListener('change', function () {
            daysInput.disabled = this.checked;
            if (this.checked) {
                daysInput.value = '';
            } else {
                daysInput.value = 30; // 默认30天
            }
        });
    }
    if (cancelBindingButton) {
        cancelBindingButton.addEventListener('click', () => bindingModal.classList.add('hidden'));
    }


}

// 获取绑定列表
async function fetchBindings() {
    loadingMessage.classList.remove('hidden');
    noRecordsMessage.classList.add('hidden');
    bindingsTable.classList.add('hidden');
    bindingsTableBody.innerHTML = '';

    try {
        const response = await fetch('/admin/bindings', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Admin-Password': currentAdminPassword
            }
        });

        if (!response.ok) {
            const errorResult = await response.json().catch(() => ({ message: '获取绑定列表失败' }));
            throw new Error(errorResult.message || `HTTP error ${response.status}`);
        }

        const result = await response.json();
        if (result.success) {
            // 存储原始数据
            originalBindingsData = result.data || [];
            // 填充档位过滤器选项
            populateBindingTierFilter();
            // 应用当前的搜索和过滤条件
            applyBindingsFilters();
        } else {
            throw new Error(result.message || '获取到的数据格式不正确');
        }
    } catch (error) {
        console.error('获取绑定列表时出错:', error);
        showToast(`错误: ${error.message}`, 5000);
    } finally {
        loadingMessage.classList.add('hidden');
    }
}

// 填充档位过滤器选项
function populateBindingTierFilter() {
    if (!bindingTierFilter) return;

    // 获取所有唯一的档位
    const uniqueTiers = [...new Set(originalBindingsData.map(binding => binding.skuId))];

    // 清空现有选项（保留"全部档位"选项）
    bindingTierFilter.innerHTML = '<option value="">全部档位</option>';

    // 添加档位选项
    uniqueTiers.forEach(skuId => {
        const tierName = getTierDisplayName(skuId);
        const option = document.createElement('option');
        option.value = skuId;
        option.textContent = tierName;
        bindingTierFilter.appendChild(option);
    });
}

// 应用搜索和过滤条件
function applyBindingsFilters() {
    const searchText = bindingsSearchInput ? bindingsSearchInput.value.toLowerCase().trim() : '';
    const statusFilter = bindingStatusFilter ? bindingStatusFilter.value : '';
    const tierFilter = bindingTierFilter ? bindingTierFilter.value : '';

    let filteredData = [...originalBindingsData];

    // 应用搜索过滤
    if (searchText) {
        filteredData = filteredData.filter(binding => {
            const searchFields = [
                binding.groupNumber || '',
                binding.owner || '',
                binding.ownerQQ || '',
                getTierDisplayName(binding.skuId),
                ...(binding.allCodes || [binding.orderNumber])
            ].map(field => String(field).toLowerCase());

            return searchFields.some(field => field.includes(searchText));
        });
    }

    // 应用状态过滤
    if (statusFilter) {
        filteredData = filteredData.filter(binding => {
            if (statusFilter === 'expired') {
                // 已过期：明确有到期时间且已经过期（ttl为-2或通过时间判断确实过期）
                if (binding.ttl === -2) return true; // 后端明确标记为已过期
                if (binding.ttl === -1 || binding.ttl === null) return false; // 永久绑定不算过期
                if (binding.expirationISOString && binding.expirationDate !== '永久') {
                    return new Date(binding.expirationISOString).getTime() < Date.now();
                }
                return false;
            } else if (statusFilter === 'active') {
                // 有效：永久绑定或未过期的时间绑定
                if (binding.ttl === -1 || binding.ttl === null) return true; // 永久绑定
                if (binding.ttl === -2) return false; // 明确已过期
                if (binding.expirationDate === '永久') return true; // 显示为永久
                if (binding.expirationISOString) {
                    return new Date(binding.expirationISOString).getTime() >= Date.now();
                }
                return true; // 默认认为有效
            }
            return true;
        });
    }

    // 应用档位过滤
    if (tierFilter) {
        filteredData = filteredData.filter(binding => binding.skuId === tierFilter);
    }

    // 显示过滤后的结果
    populateBindingsTable({ success: true, data: filteredData });
}

// 清空所有过滤条件
function clearBindingsFilters() {
    if (bindingsSearchInput) bindingsSearchInput.value = '';
    if (bindingStatusFilter) bindingStatusFilter.value = '';
    if (bindingTierFilter) bindingTierFilter.value = '';
    applyBindingsFilters();
}

// 填充绑定表格
function populateBindingsTable(data) {
    bindingsTableBody.innerHTML = '';

    // 检查数据结构
    const bindings = data.data || data;

    if (!bindings || bindings.length === 0) {
        noRecordsMessage.classList.remove('hidden');
        bindingsTable.classList.add('hidden');
        return;
    }

    noRecordsMessage.classList.add('hidden');
    bindingsTable.classList.remove('hidden');

    // 按剩余天数排序（从小到大）
    bindings.sort((a, b) => {
        // 计算剩余天数的函数
        function calculateRemainingDays(binding) {
            if (!binding.expirationISOString || binding.expirationDate === '永久') {
                return Infinity; // 永久有效的排在最后
            }

            const expirationTime = new Date(binding.expirationISOString);
            const now = new Date();
            const diffTime = expirationTime.getTime() - now.getTime();
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            return diffDays;
        }

        const remainingDaysA = calculateRemainingDays(a);
        const remainingDaysB = calculateRemainingDays(b);

        // 已过期的排在最前面（负数）
        if (remainingDaysA < 0 && remainingDaysB >= 0) return -1;
        if (remainingDaysA >= 0 && remainingDaysB < 0) return 1;

        // 都是已过期的，按过期时间排序（越早过期的越靠前）
        if (remainingDaysA < 0 && remainingDaysB < 0) {
            return remainingDaysA - remainingDaysB;
        }

        // 正常情况下按剩余天数从小到大排序
        return remainingDaysA - remainingDaysB;
    });

    bindings.forEach((binding, index) => {
        const row = document.createElement('tr');
        // 过期判断：优先使用后端 ttl（-2 为已过期）；回退使用 expirationISOString 与当前时间比较
        const isExpired = (typeof binding.ttl === 'number' && binding.ttl === -2) ||
            (!!binding.expirationISOString && new Date(binding.expirationISOString).getTime() < Date.now());

        // 计算剩余天数（优先使用时间池数据）
        let remainingDaysHtml = '';
        let timePoolInfoHtml = '';

        if (binding.isTimePool && binding.totalRemainingDays !== undefined) {
            // 使用时间池的总剩余天数
            const totalDays = binding.totalRemainingDays;

            if (totalDays === -1) {
                // -1 表示永久
                remainingDaysHtml = ` <span class="ml-1 px-2 py-0.5 text-xs rounded-full bg-blue-100 text-blue-700">永久</span>`;
            } else if (totalDays > 0) {
                if (totalDays <= 7) {
                    remainingDaysHtml = ` <span class="ml-1 px-2 py-0.5 text-xs rounded-full bg-orange-100 text-orange-700">总剩余${totalDays}天</span>`;
                } else if (totalDays <= 30) {
                    remainingDaysHtml = ` <span class="ml-1 px-2 py-0.5 text-xs rounded-full bg-yellow-100 text-yellow-700">总剩余${totalDays}天</span>`;
                } else {
                    remainingDaysHtml = ` <span class="ml-1 px-2 py-0.5 text-xs rounded-full bg-green-100 text-green-700">总剩余${totalDays}天</span>`;
                }
            } else {
                remainingDaysHtml = ` <span class="ml-1 px-2 py-0.5 text-xs rounded-full bg-red-100 text-red-700">已过期</span>`;
            }

            // 添加时间池信息
            if (binding.activeCodesCount > 1) {
                timePoolInfoHtml = ` <span class="ml-1 px-2 py-0.5 text-xs rounded-full bg-purple-100 text-purple-700">${binding.activeCodesCount}个激活码</span>`;
            }
        } else if (binding.expirationDate && binding.expirationDate !== '永久' && binding.expirationISOString) {
            // 回退到原有逻辑
            const expirationTime = new Date(binding.expirationISOString);
            const now = new Date();
            const diffTime = expirationTime.getTime() - now.getTime();
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            if (diffDays > 0) {
                if (diffDays <= 7) {
                    remainingDaysHtml = ` <span class="ml-1 px-2 py-0.5 text-xs rounded-full bg-orange-100 text-orange-700">剩余${diffDays}天</span>`;
                } else if (diffDays <= 30) {
                    remainingDaysHtml = ` <span class="ml-1 px-2 py-0.5 text-xs rounded-full bg-yellow-100 text-yellow-700">剩余${diffDays}天</span>`;
                } else {
                    remainingDaysHtml = ` <span class="ml-1 px-2 py-0.5 text-xs rounded-full bg-green-100 text-green-700">剩余${diffDays}天</span>`;
                }
            } else if (diffDays === 0) {
                remainingDaysHtml = ` <span class="ml-1 px-2 py-0.5 text-xs rounded-full bg-red-100 text-red-700">今日到期</span>`;
            }
        }

        let expirationHtml = `${binding.expirationDate}${remainingDaysHtml}`;
        if (isExpired) {
            expirationHtml += ' <span class="ml-1 px-2 py-0.5 text-xs rounded-full bg-red-100 text-red-700">已过期</span>';
        }

        // 构建激活码详细信息
        let codesDetailHtml = '';
        if (binding.isTimePool && binding.activeCodes) {
            // 显示所有激活码的详细信息，区分正在消耗和等待中的状态
            const activeCodesHtml = binding.activeCodes.map(code => {
                const bindTime = new Date(code.bind_time).toLocaleString('zh-CN');
                const expirationTime = code.is_permanent ? '永久' :
                    (code.expiration_time ? new Date(code.expiration_time).toLocaleString('zh-CN') : '未知');
                const remainingDays = code.is_permanent ? '永久' :
                    (code.remaining_days > 0 ? `${code.remaining_days}天` : '已过期');

                // 根据状态选择不同的样式和图标
                let statusIcon, statusText, bgColor, borderColor, statusColor;
                if (code.status === 'consuming') {
                    statusIcon = '🔥';
                    statusText = '正在消耗';
                    bgColor = 'bg-orange-50';
                    borderColor = 'border-orange-200';
                    statusColor = 'text-orange-700';
                } else if (code.status === 'waiting') {
                    statusIcon = '⏸️';
                    statusText = '等待中';
                    bgColor = 'bg-blue-50';
                    borderColor = 'border-blue-200';
                    statusColor = 'text-blue-700';
                } else {
                    statusIcon = '✅';
                    statusText = '有效';
                    bgColor = 'bg-green-50';
                    borderColor = 'border-green-200';
                    statusColor = 'text-green-700';
                }

                return `
                    <div class="activation-code-card ${code.status === 'consuming' ? 'consuming' : 'waiting'}">
                        <div class="flex justify-between items-start mb-1">
                            <div class="font-mono font-bold text-gray-800">${code.activation_code}</div>
                            <div class="flex items-center gap-1">
                                <div class="ios-badge ${code.status === 'consuming' ? 'ios-badge-warning' : 'ios-badge-info'}">${statusIcon} ${statusText}</div>
                                <button onclick="deleteSpecificBinding('${code.activation_code}', '${binding.skuId}')"
                                        class="ios-button-small ios-button-danger ml-1"
                                        title="删除此激活码">
                                    <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div class="text-gray-600">👤 ${code.owner_username}</div>
                        <div class="text-gray-600">📅 绑定: ${bindTime}</div>
                        <div class="text-gray-600">⏰ 到期: ${expirationTime}</div>
                        <div class="font-semibold">⏳ 剩余: ${remainingDays}</div>
                        ${code.status === 'waiting' ? '<div class="text-xs text-gray-500 mt-1">💡 等待前面的激活码消耗完毕</div>' : ''}
                    </div>
                `;
            }).join('');

            const expiredCodesHtml = binding.expiredCodes ? binding.expiredCodes.map(code => {
                const bindTime = new Date(code.bind_time).toLocaleString('zh-CN');
                const expirationTime = new Date(code.expiration_time).toLocaleString('zh-CN');

                return `
                    <div class="mb-2 p-2 bg-red-50 border border-red-200 rounded text-xs">
                        <div class="font-mono font-bold text-red-800">${code.activation_code}</div>
                        <div class="text-gray-600 mt-1">👤 ${code.owner_username}</div>
                        <div class="text-gray-600">📅 绑定: ${bindTime}</div>
                        <div class="text-gray-600">⏰ 到期: ${expirationTime}</div>
                        <div class="text-red-700 font-semibold">❌ 已过期</div>
                    </div>
                `;
            }).join('') : '';

            codesDetailHtml = activeCodesHtml + expiredCodesHtml;
        }

        // 构建贡献者信息
        let contributorsHtml = '';
        if (binding.contributors) {
            contributorsHtml = binding.contributors.map(contributor =>
                `<div class="text-xs mb-1">👤 ${contributor.username}<br>📱 ${contributor.qq}</div>`
            ).join('');
        }

        row.innerHTML = `
            <td class="px-4 py-3 text-sm" data-label="序号">${index + 1}</td>
            <td class="px-4 py-3 text-sm" data-label="群组信息">
                <div class="flex items-center gap-3">
                    <img src="/api/group-avatar/${binding.groupNumber}"
                         alt="群头像"
                         class="ios-group-avatar"
                         onerror="this.src='data:image/svg+xml,%3Csvg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22%3E%3Crect width=%22100%22 height=%22100%22 fill=%22%23e0e0e0%22/%3E%3Ccircle cx=%2250%22 cy=%2235%22 r=%2215%22 fill=%22%23999%22/%3E%3Cpath d=%22M20 80 Q20 65 35 65 L65 65 Q80 65 80 80 L80 90 L20 90 Z%22 fill=%22%23999%22/%3E%3C/svg%3E'">
                    <div class="flex flex-col">
                        <span class="font-semibold text-gray-900">${binding.groupNumber}</span>
                    </div>
                </div>
            </td>
            <td class="px-4 py-3 text-sm" data-label="激活码详情">
                <div class="max-w-sm max-h-64 overflow-y-auto">
                    ${codesDetailHtml || `
                        <span class="activation-code-link cursor-pointer"
                              onclick="showGroupActivationCodes('${binding.groupNumber}', ${JSON.stringify(binding.allCodes || []).replace(/"/g, '&quot;')})">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            查看激活码
                        </span>
                    `}
                </div>
            </td>
            <td class="px-4 py-3 text-sm" data-label="档位">
                <span class="tier-badge unified">
                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm4 3a1 1 0 100-2 1 1 0 000 2zm5-1a1 1 0 11-2 0 1 1 0 012 0z" clip-rule="evenodd"></path>
                    </svg>
                    ${getTierDisplayName(binding.skuId)}
                </span>
            </td>

            <td class="px-4 py-3 text-sm" data-label="到期时间">${expirationHtml}${timePoolInfoHtml}</td>
            <td class="px-4 py-3 text-sm text-center" data-label="操作">
                <div class="action-buttons">
                    <button onclick="openEditBindingModal('${binding.orderNumber || binding.id}', '${binding.skuId}', '${binding.groupNumber}', '${binding.expirationISOString || ''}', '${binding.owner || 'admin'}')"
                            class="ios-button-small ios-button-secondary">编辑</button>
                    <button onclick="deleteAllBindingsForGroup('${binding.groupNumber}', '${binding.skuId}')"
                            class="ios-button-small ios-button-danger">删除全部</button>

                </div>
            </td>
        `;
        if (isExpired) {
            row.classList.add('bg-red-50');
            row.setAttribute('data-expired', '1');
        }
        bindingsTableBody.appendChild(row);
    });
}

// 切换激活码详情显示
function toggleActivationDetails(groupNumber) {
    const chevron = document.getElementById(`chevron-${groupNumber}`);
    const details = document.getElementById(`details-${groupNumber}`);

    if (details.classList.contains('hidden')) {
        details.classList.remove('hidden');
        chevron.style.transform = 'rotate(90deg)';
    } else {
        details.classList.add('hidden');
        chevron.style.transform = 'rotate(0deg)';
    }
}



// 打开编辑绑定模态框
function openEditBindingModal(orderNumber, skuId, groupNumber, expirationISOString) {
    bindingModalTitle.textContent = '编辑绑定';
    document.getElementById('originalOrderNumber').value = orderNumber;
    document.getElementById('originalSkuId').value = skuId;
    document.getElementById('modalOrderNumber').value = orderNumber;
    document.getElementById('modalGroupNumber').value = groupNumber;
    document.getElementById('modalSkuId').value = skuId;

    // 设置到期时间
    const permanentCheckbox = document.getElementById('modalPermanentExpiration');
    const daysInput = document.getElementById('modalExpirationDays');

    if (!expirationISOString || expirationISOString === 'null') {
        permanentCheckbox.checked = true;
        daysInput.disabled = true;
        daysInput.value = '';
    } else {
        permanentCheckbox.checked = false;
        daysInput.disabled = false;
        // 计算剩余天数
        try {
            const expiryDate = new Date(expirationISOString);
            const today = new Date();
            const diffTime = expiryDate - today;
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            daysInput.value = Math.max(1, diffDays);
        } catch (e) {
            daysInput.value = 30; // 默认30天
        }
    }

    bindingModal.classList.remove('hidden');
}

// 删除特定激活码
async function deleteSpecificBinding(activationCode, skuId) {
    if (!confirm(`确定要删除激活码 ${activationCode} 吗？\n\n注意：删除后该激活码的时间将从时间池中移除。`)) {
        return;
    }

    try {
        const response = await fetch('/admin/bindings/delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Admin-Password': currentAdminPassword
            },
            body: JSON.stringify({ orderNumber: activationCode, skuId })
        });

        if (!response.ok) {
            const errorResult = await response.json().catch(() => ({ message: '删除失败' }));
            throw new Error(errorResult.message || `HTTP error ${response.status}`);
        }

        const result = await response.json();
        if (result.success) {
            showToast('激活码删除成功');
            fetchBindings();
        } else {
            throw new Error(result.message || '删除失败');
        }
    } catch (error) {
        console.error('删除激活码时出错:', error);
        showToast(`错误: ${error.message}`, 5000);
    }
}

// 删除群组的所有绑定
async function deleteAllBindingsForGroup(groupNumber, skuId) {
    if (!confirm(`确定要删除群组 ${groupNumber} 的所有 ${getTierDisplayName(skuId)} 激活码吗？\n\n这将删除该群组该档位的所有激活码！`)) {
        return;
    }

    try {
        const response = await fetch('/admin/bindings/delete-group', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Admin-Password': currentAdminPassword
            },
            body: JSON.stringify({ groupNumber, skuId })
        });

        if (!response.ok) {
            const errorResult = await response.json().catch(() => ({ message: '删除失败' }));
            throw new Error(errorResult.message || `HTTP error ${response.status}`);
        }

        const result = await response.json();
        if (result.success) {
            showToast('群组绑定删除成功');
            fetchBindings();
        } else {
            throw new Error(result.message || '删除失败');
        }
    } catch (error) {
        console.error('删除群组绑定时出错:', error);
        showToast(`错误: ${error.message}`, 5000);
    }
}

// 保留原有的删除函数以兼容其他地方的调用
async function deleteBinding(orderNumber, skuId) {
    return await deleteSpecificBinding(orderNumber, skuId);
}

// 打开添加绑定模态框
function openAddBindingModal() {
    bindingModalTitle.textContent = '添加新绑定';
    bindingForm.reset();
    document.getElementById('originalOrderNumber').value = '';
    document.getElementById('originalSkuId').value = '';
    document.getElementById('modalPermanentExpiration').checked = false;
    document.getElementById('modalExpirationDays').disabled = false;
    document.getElementById('modalExpirationDays').value = 30; // 默认30天
    bindingModal.classList.remove('hidden');
}

// 手动发送到期提醒（单条绑定）
async function sendExpiryReminder(groupNumber, skuId, owner) {
    if (!confirm('确认向该绑定的用户发送到期提醒邮件？')) return;
    try {
        const resp = await fetch('/admin/bindings/send-expiry-reminder', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Admin-Password': currentAdminPassword
            },
            body: JSON.stringify({ groupNumber, skuId, owner })
        });
        const result = await resp.json().catch(() => ({ success: false }));
        if (!resp.ok || !result.success) throw new Error(result.message || '发送失败');
        showToast('到期提醒已发送', 'success');
    } catch (e) {
        console.error('发送到期提醒失败:', e);
        showToast(e.message || '发送失败', 'error');
    }
}



// 保存绑定
async function saveBinding(e) {
    e.preventDefault();

    const originalOrderNumber = document.getElementById('originalOrderNumber').value;
    let orderNumber = document.getElementById('modalOrderNumber').value.trim();
    const groupNumber = document.getElementById('modalGroupNumber').value.trim();
    const skuId = document.getElementById('modalSkuId').value;
    const isPermanent = document.getElementById('modalPermanentExpiration').checked;
    const expirationDays = document.getElementById('modalExpirationDays').value;

    // 如果激活码为空，自动生成
    if (!orderNumber) {
        orderNumber = generateActivationCode();
        document.getElementById('modalOrderNumber').value = orderNumber;
    }

    if (!groupNumber || !skuId) {
        bindingModalError.textContent = '请填写群号和选择档位';
        return;
    }

    if (!isPermanent && (!expirationDays || expirationDays < 1)) {
        bindingModalError.textContent = '请输入有效的天数（大于0）';
        return;
    }

    showLoading(saveBindingButton);
    bindingModalError.textContent = '';

    try {
        const isEdit = !!originalOrderNumber;
        const url = isEdit ? '/admin/bindings/update' : '/admin/bindings/add';

        // 计算到期时间
        let expirationTimestamp = null;
        if (!isPermanent && expirationDays) {
            const expiryDate = new Date();
            expiryDate.setDate(expiryDate.getDate() + parseInt(expirationDays));
            // 设置为当天的23:59:59
            expiryDate.setHours(23, 59, 59, 999);
            expirationTimestamp = expiryDate.toISOString();
        }

        const body = {
            orderNumber,
            groupNumber,
            skuId,
            isPermanent,
            expirationTimestamp
        };

        if (isEdit) {
            body.originalOrderNumber = originalOrderNumber;
            body.originalSkuId = document.getElementById('originalSkuId').value;
            body.newOrderNumber = orderNumber;
            body.newSkuId = skuId;
            body.newGroupNumber = groupNumber;
        }

        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Admin-Password': currentAdminPassword
            },
            body: JSON.stringify(body)
        });

        if (!response.ok) {
            const errorResult = await response.json().catch(() => ({ message: '保存失败' }));
            throw new Error(errorResult.message || `HTTP error ${response.status}`);
        }

        const result = await response.json();
        if (result.success) {
            showToast(isEdit ? '更新成功' : '添加成功');
            bindingModal.classList.add('hidden');
            fetchBindings();
        } else {
            throw new Error(result.message || '保存失败');
        }
    } catch (error) {
        console.error('保存绑定时出错:', error);
        bindingModalError.textContent = error.message;
    } finally {
        hideLoading(saveBindingButton);
    }
}

// 生成激活码
function generateActivationCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 16; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

// 显示群组的所有激活码
function showGroupActivationCodes(groupNumber, allCodes) {
    const codesList = allCodes.map((code, index) => `
        <div class="flex items-center justify-between p-3 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg mb-2 hover:from-blue-50 hover:to-blue-100 transition-all duration-200">
            <div class="flex items-center">
                <span class="flex items-center justify-center w-6 h-6 bg-blue-500 text-white text-xs font-bold rounded-full mr-3">${index + 1}</span>
                <code class="font-mono text-sm bg-white px-2 py-1 rounded border">${code}</code>
            </div>
            <button onclick="copyToClipboard('${code}')" class="text-blue-600 hover:text-blue-800 transition-colors duration-200" title="复制激活码">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                </svg>
            </button>
        </div>
    `).join('');

    const modalContent = `
        <div class="max-h-96 overflow-y-auto">
            <div class="mb-4 p-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg">
                <h3 class="text-lg font-semibold flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                    </svg>
                    群号 ${groupNumber} 的激活码历史
                </h3>
                <p class="text-blue-100 text-sm mt-1">该群组累计使用了 ${allCodes.length} 个激活码</p>
            </div>
            <div class="space-y-2">
                ${codesList}
            </div>
        </div>
    `;

    showModal('群组激活码列表', modalContent);
}

// 复制到剪贴板的函数
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showToast('激活码已复制到剪贴板', 2000);
    }).catch(err => {
        console.error('复制失败:', err);
        showToast('复制失败，请手动选择复制', 3000);
    });
}

// 获取档位显示名称
function getTierDisplayName(skuId) {
    if (window.TiersModule && window.TiersModule.getTiersConfig) {
        const tiersConfig = window.TiersModule.getTiersConfig();
        const tier = tiersConfig.find(t => t.sku_id === skuId);
        return tier ? tier.display_name : '未知档位';
    }
    return '未知档位';
}

// 暴露给全局
window.fetchBindings = fetchBindings;
window.openEditBindingModal = openEditBindingModal;
window.deleteBinding = deleteBinding;
window.sendExpiryReminder = sendExpiryReminder;
window.showGroupActivationCodes = showGroupActivationCodes;
window.copyToClipboard = copyToClipboard;
window.getTierDisplayName = getTierDisplayName;

// 导出模块初始化函数
window.initBindingsModule = initBindingsModule;
