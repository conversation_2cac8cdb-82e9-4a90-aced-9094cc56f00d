// 兑换码管理模块
// 依赖：需要全局变量 tiersConfig, currentAdminPassword, showToast, showModal, showLoading, hideLoading

// DOM 元素 (这些变量在admin.js中定义，全局可用)
let codesTableBody;
let codesLoadingMessage;
let codesTable;
let codesSearchInput;
let codeStatusFilter;
let codesPerPage;
let refreshCodesButton;
let codesPagination;
let exportCodesButton;
let exportSelectedCodesButton;
let selectAllCodes;
let deleteSelectedCodesButton;
let selectedCodesCount;

let selectedCodeIds = new Set();
let currentCodesPage = 1;
let createdCodes = [];

// 创建兑换码模态框相关元素
let createCodesModal;
let createCodesForm;
let closeCreateCodesModal;
let cancelCreateCodesButton;
let confirmCreateCodesButton;
let createCodesError;

// 创建结果模态框相关元素
let codesResultModal;
let codesResultContent;
let closeCodesResultModal;
let copyCodesButton;
let downloadCodesButton;

// 初始化兑换码管理模块
function initCodesModule() {
    // 获取DOM元素 (codesContent已在admin.js中定义)
    codesTableBody = document.getElementById('codes-table-body');
    codesLoadingMessage = document.getElementById('codesLoadingMessage');
    codesTable = document.getElementById('codesTable');
    codesSearchInput = document.getElementById('codeSearchInput');
    codeStatusFilter = document.getElementById('codeStatusFilter');
    codesPerPage = document.getElementById('codesPerPage');
    refreshCodesButton = document.getElementById('refreshCodesButton');
    codesPagination = document.getElementById('codesPagination');
    exportCodesButton = document.getElementById('exportCodesButton');
    exportSelectedCodesButton = document.getElementById('exportSelectedCodesButton');
    selectAllCodes = document.getElementById('selectAllCodes');
    deleteSelectedCodesButton = document.getElementById('deleteSelectedCodesButton');
    selectedCodesCount = document.getElementById('selectedCodesCount');

    // 创建兑换码模态框相关元素
    createCodesModal = document.getElementById('createCodesModal');
    createCodesForm = document.getElementById('createCodesForm');
    closeCreateCodesModal = document.getElementById('closeCreateCodesModal');
    cancelCreateCodesButton = document.getElementById('cancelCreateCodesButton');
    confirmCreateCodesButton = document.getElementById('confirmCreateCodesButton');
    createCodesError = document.getElementById('createCodesError');

    // 获取其他需要的元素
    const codesSkuId = document.getElementById('codesSkuId');
    const codesDuration = document.getElementById('codesDuration');
    const codesQuantity = document.getElementById('codesQuantity');
    const codesNote = document.getElementById('codesNote');

    // 将这些元素设为全局变量供其他函数使用
    window.codesSkuId = codesSkuId;
    window.codesDuration = codesDuration;
    window.codesQuantity = codesQuantity;
    window.codesNote = codesNote;

    // 创建结果模态框相关元素
    codesResultModal = document.getElementById('codesResultModal');
    codesResultContent = document.getElementById('codesResultContent');
    closeCodesResultModal = document.getElementById('closeCodesResultModal');
    copyCodesButton = document.getElementById('copyCodesButton');
    downloadCodesButton = document.getElementById('downloadCodesButton');

    // 绑定事件监听器
    if (refreshCodesButton) {
        refreshCodesButton.addEventListener('click', () => window.fetchActivationCodes());
    }
    if (codesSearchInput) {
        codesSearchInput.addEventListener('input', () => window.fetchActivationCodes(1));
    }
    if (codeStatusFilter) {
        codeStatusFilter.addEventListener('change', () => window.fetchActivationCodes(1));
    }
    if (codesPerPage) {
        codesPerPage.addEventListener('change', () => window.fetchActivationCodes(1));
    }
    if (exportCodesButton) {
        console.log('绑定导出按钮事件');
        exportCodesButton.addEventListener('click', exportCodes);
        exportCodesButton.disabled = false; // 确保按钮可用
        exportCodesButton.style.opacity = '1'; // 确保视觉上也可用
    } else {
        console.warn('未找到导出按钮元素');
    }
    if (exportSelectedCodesButton) {
        exportSelectedCodesButton.addEventListener('click', exportSelectedCodes);
    }
    if (selectAllCodes) {
        selectAllCodes.addEventListener('change', (e) => {
            const checkboxes = document.querySelectorAll('.code-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = e.target.checked;
                const codeId = parseInt(checkbox.getAttribute('data-code-id'));
                if (e.target.checked) {
                    selectedCodeIds.add(codeId);
                } else {
                    selectedCodeIds.delete(codeId);
                }
            });
            updateDeleteButton();
        });
    }
    if (deleteSelectedCodesButton) {
        deleteSelectedCodesButton.addEventListener('click', deleteSelectedCodes);
    }

    // 创建兑换码相关事件
    const createCodesButton = document.getElementById('createCodesButton');
    if (createCodesButton) {
        createCodesButton.addEventListener('click', showCreateCodesModal);
    }
    if (createCodesForm) {
        createCodesForm.addEventListener('submit', createCodes);
    }
    if (closeCreateCodesModal) {
        closeCreateCodesModal.addEventListener('click', () => createCodesModal.classList.add('hidden'));
    }
    if (cancelCreateCodesButton) {
        cancelCreateCodesButton.addEventListener('click', () => createCodesModal.classList.add('hidden'));
    }

    // 创建结果模态框
    if (closeCodesResultModal) {
        closeCodesResultModal.addEventListener('click', () => codesResultModal.classList.add('hidden'));
    }
    if (copyCodesButton) {
        copyCodesButton.addEventListener('click', copyAllCodes);
    }
    if (downloadCodesButton) {
        downloadCodesButton.addEventListener('click', downloadCodesFile);
    }
}

// 获取兑换码列表
async function fetchActivationCodes(page = 1) {
    codesLoadingMessage.classList.remove('hidden');
    codesTableBody.innerHTML = '';
    selectedCodeIds.clear();

    // 重置全选复选框
    if (selectAllCodes) {
        selectAllCodes.checked = false;
        selectAllCodes.disabled = false;
    }

    updateDeleteButton();

    try {
        const perPage = parseInt(codesPerPage.value) || 20;
        const params = new URLSearchParams({
            page: page,
            perPage: perPage,
            ...(codeStatusFilter.value && { used: codeStatusFilter.value }),
            ...(codesSearchInput.value && { search: codesSearchInput.value })
        });

        const response = await fetch(`/admin/codes?${params}`, {
            method: 'GET',
            headers: {
                'X-Admin-Password': currentAdminPassword
            }
        });

        if (!response.ok) {
            const errorResult = await response.json().catch(() => ({ message: '获取兑换码列表失败' }));
            throw new Error(errorResult.message || `HTTP error ${response.status}`);
        }

        const result = await response.json();
        console.log('获取兑换码数据:', result);

        if (result.success && result.data) {
            console.log('兑换码列表:', result.data.codes);
            populateCodesTable(result.data.codes);
            renderPagination(result.data.pagination);
            currentCodesPage = page;
        } else {
            throw new Error(result.message || '获取到的数据格式不正确');
        }
    } catch (error) {
        console.error('获取兑换码列表时出错:', error);
        showToast(`错误: ${error.message}`, 5000);
    } finally {
        codesLoadingMessage.classList.add('hidden');
    }
}

// 填充兑换码表格
function populateCodesTable(codes) {
    codesTableBody.innerHTML = '';

    codes.forEach(code => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td class="px-4 py-3" data-label="选择">
                <input type="checkbox" class="code-checkbox rounded" data-code-id="${code.id}">
            </td>
            <td class="px-4 py-3 text-sm font-mono" data-label="兑换码">
                <span class="code-display">${code.code}</span>
            </td>
            <td class="px-4 py-3 text-sm" data-label="档位">
                ${code.skuType}
            </td>
            <td class="px-4 py-3 text-sm" data-label="时长">
                ${code.durationMonths} 个月
            </td>
            <td class="px-4 py-3 text-sm" data-label="状态">
                <span class="px-2 py-1 text-xs font-semibold rounded-full ${(code.used || code.isUsed || code.usedBy) ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'}">
                    ${(code.used || code.isUsed || code.usedBy) ? '已使用' : '未使用'}
                </span>
            </td>
            <td class="px-4 py-3 text-sm" data-label="使用者">
                ${code.usedBy || '-'}
            </td>
            <td class="px-4 py-3 text-sm" data-label="备注">
                ${code.note || '-'}
            </td>
            <td class="px-4 py-3 text-sm" data-label="创建时间">
                ${code.createdAt || code.createdTime || code.createTime ? new Date(code.createdAt || code.createdTime || code.createTime).toLocaleString('zh-CN') : '未知'}
            </td>
            <td class="px-4 py-3 text-sm text-center" data-label="操作">
                <div class="flex justify-center gap-2">
                    <button onclick="window.deleteCode('${code.id}')"
                            class="text-red-600 hover:text-red-900">删除</button>
                </div>
            </td>
        `;
        codesTableBody.appendChild(row);
    });

    // 重新绑定复选框事件
    document.querySelectorAll('.code-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', (e) => {
            const codeId = parseInt(e.target.getAttribute('data-code-id'));
            if (e.target.checked) {
                selectedCodeIds.add(codeId);
            } else {
                selectedCodeIds.delete(codeId);
            }
            updateSelectAllCheckbox();
            updateDeleteButton();
        });
    });
}

// 更新删除按钮状态
function updateDeleteButton() {
    const selectedCount = selectedCodeIds.size;
    deleteSelectedCodesButton.disabled = selectedCount === 0;
    exportSelectedCodesButton.disabled = selectedCount === 0;
    selectedCodesCount.textContent = selectedCount > 0 ? `已选择 ${selectedCount} 个` : '';
}

// 更新全选复选框状态
function updateSelectAllCheckbox() {
    const checkboxes = document.querySelectorAll('.code-checkbox');
    const checkedCount = document.querySelectorAll('.code-checkbox:checked').length;

    if (checkboxes.length === 0) {
        selectAllCodes.checked = false;
        selectAllCodes.disabled = true;
    } else {
        selectAllCodes.disabled = false;
        selectAllCodes.checked = checkedCount === checkboxes.length;
    }
}

// 渲染分页控件
function renderPagination(pagination) {
    codesPagination.innerHTML = '';

    const { page, totalPages } = pagination;

    // 上一页
    const prevButton = document.createElement('button');
    prevButton.className = `px-3 py-2 rounded-md text-sm font-medium ${page <= 1 ? 'bg-gray-200 text-gray-400 cursor-not-allowed' : 'bg-blue-500 text-white hover:bg-blue-600'}`;
    prevButton.disabled = page <= 1;
    prevButton.textContent = '上一页';
    if (page > 1) {
        prevButton.onclick = () => window.fetchActivationCodes(page - 1);
    }
    codesPagination.appendChild(prevButton);

    // 页码
    for (let i = 1; i <= totalPages; i++) {
        if (i === 1 || i === totalPages || (i >= page - 2 && i <= page + 2)) {
            const pageButton = document.createElement('button');
            pageButton.className = `px-3 py-2 rounded-md text-sm font-medium ${i === page ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`;
            pageButton.textContent = i;
            if (i !== page) {
                pageButton.onclick = () => window.fetchActivationCodes(i);
            }
            codesPagination.appendChild(pageButton);
        } else if (i === page - 3 || i === page + 3) {
            const dots = document.createElement('span');
            dots.className = 'px-2';
            dots.textContent = '...';
            codesPagination.appendChild(dots);
        }
    }

    // 下一页
    const nextButton = document.createElement('button');
    nextButton.className = `px-3 py-2 rounded-md text-sm font-medium ${page >= totalPages ? 'bg-gray-200 text-gray-400 cursor-not-allowed' : 'bg-blue-500 text-white hover:bg-blue-600'}`;
    nextButton.disabled = page >= totalPages;
    nextButton.textContent = '下一页';
    if (page < totalPages) {
        nextButton.onclick = () => window.fetchActivationCodes(page + 1);
    }
    codesPagination.appendChild(nextButton);
}

// 删除兑换码
async function deleteCode(codeId) {
    console.log('删除兑换码，ID:', codeId, '类型:', typeof codeId);
    if (!confirm('确定要删除这个兑换码吗？')) {
        return;
    }

    try {
        // 尝试多种ID格式
        let idToDelete;
        if (typeof codeId === 'string') {
            idToDelete = parseInt(codeId) || codeId;
        } else {
            idToDelete = codeId;
        }

        const deleteData = { ids: [idToDelete] };
        console.log('发送删除请求:', deleteData);

        const response = await fetch('/admin/codes/delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Admin-Password': currentAdminPassword
            },
            body: JSON.stringify(deleteData)
        });

        console.log('删除响应状态:', response.status);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('删除请求失败:', errorText);
            throw new Error(`HTTP error ${response.status}: ${errorText}`);
        }

        const result = await response.json();
        console.log('删除响应结果:', result);

        if (result.success) {
            showToast('删除成功');
            window.fetchActivationCodes(currentCodesPage);
        } else {
            throw new Error(result.message || '删除失败');
        }
    } catch (error) {
        console.error('删除兑换码时出错:', error);
        showToast(`错误: ${error.message}`, 5000);
    }
}

// 删除选中的兑换码
async function deleteSelectedCodes() {
    if (selectedCodeIds.size === 0) return;

    if (!confirm(`确定要删除选中的 ${selectedCodeIds.size} 个兑换码吗？`)) {
        return;
    }

    try {
        const response = await fetch('/admin/codes/delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Admin-Password': currentAdminPassword
            },
            body: JSON.stringify({ ids: Array.from(selectedCodeIds) })
        });

        if (!response.ok) {
            throw new Error(`HTTP error ${response.status}`);
        }

        const result = await response.json();
        if (result.success) {
            showToast('删除成功');
            window.fetchActivationCodes(currentCodesPage);
        } else {
            throw new Error(result.message || '删除失败');
        }
    } catch (error) {
        console.error('删除选中的兑换码时出错:', error);
        showToast(`错误: ${error.message}`, 5000);
    }
}

// 导出兑换码
async function exportCodes() {
    console.log('开始导出兑换码');
    try {
        const response = await fetch('/admin/codes/export', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Admin-Password': currentAdminPassword
            },
            body: JSON.stringify({ format: 'csv' })
        });

        console.log('导出响应状态:', response.status);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('导出请求失败:', errorText);
            throw new Error(`HTTP error ${response.status}: ${errorText}`);
        }

        const blob = await response.blob();
        console.log('获取到blob，大小:', blob.size);

        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `codes_${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);

        console.log('导出完成');
        showToast('导出成功');
    } catch (error) {
        console.error('导出兑换码时出错:', error);
        showToast(`错误: ${error.message}`, 5000);
    }
}

// 导出选中的兑换码
async function exportSelectedCodes() {
    if (selectedCodeIds.size === 0) return;

    try {
        // 先获取选中的兑换码详情
        const params = new URLSearchParams({
            page: 1,
            perPage: 1000 // 获取足够多的数据
        });

        const response = await fetch(`/admin/codes?${params}`, {
            method: 'GET',
            headers: {
                'X-Admin-Password': currentAdminPassword
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error ${response.status}`);
        }

        const result = await response.json();
        const selectedCodes = result.data.codes.filter(code => selectedCodeIds.has(code.id));

        if (selectedCodes.length === 0) {
            showToast('未找到选中的兑换码数据', 3000);
            return;
        }

        // 生成CSV内容
        const csvContent = [
            '兑换码,档位,时长,状态,使用者,备注,创建时间',
            ...selectedCodes.map(code => [
                code.code,
                code.skuType,
                `${code.durationMonths}个月`,
                code.used ? '已使用' : '未使用',
                code.usedBy || '',
                code.note || '',
                new Date(code.createdAt).toLocaleString('zh-CN')
            ].map(field => `"${String(field).replace(/"/g, '""')}"`).join(','))
        ].join('\n');

        // 下载CSV文件
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `selected_codes_${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);

        showToast('导出成功');
    } catch (error) {
        console.error('导出选中的兑换码时出错:', error);
        showToast(`错误: ${error.message}`, 5000);
    }
}

// 打开创建兑换码模态框
function showCreateCodesModal() {
    createCodesModal.classList.remove('hidden');
    createCodesForm.reset();
    createCodesError.textContent = '';

    // 使用动态加载的档位配置
    if (window.TiersModule && window.TiersModule.populateTierSelects) {
        window.TiersModule.populateTierSelects();
    }
}

// 创建兑换码
async function createCodes(e) {
    e.preventDefault();

    const skuId = window.codesSkuId.value;
    const duration = parseInt(window.codesDuration.value);
    const quantity = parseInt(window.codesQuantity.value);
    const note = window.codesNote.value.trim();

    showLoading(confirmCreateCodesButton);
    createCodesError.textContent = '';

    try {
        const response = await fetch('/admin/codes/create', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Admin-Password': currentAdminPassword
            },
            body: JSON.stringify({
                skuId: skuId,
                durationMonths: duration,
                quantity: quantity,
                note: note || null
            })
        });

        if (!response.ok) {
            const errorResult = await response.json().catch(() => ({ message: '创建失败' }));
            throw new Error(errorResult.message || `HTTP error ${response.status}`);
        }

        const result = await response.json();
        if (result.success) {
            showCreateResult(result.data);
            createCodesModal.classList.add('hidden');
            window.fetchActivationCodes(1); // 刷新列表
        } else {
            throw new Error(result.message || '创建失败');
        }
    } catch (error) {
        console.error('创建兑换码时出错:', error);
        createCodesError.textContent = error.message;
    } finally {
        hideLoading(confirmCreateCodesButton);
    }
}

// 显示创建结果
function showCreateResult(data) {
    createdCodes = data.codes; // 保存创建的兑换码用于复制和下载

    codesResultContent.innerHTML = `
        <div class="bg-green-50 p-4 rounded-lg mb-4">
            <p class="text-green-800 font-medium">成功创建 ${data.codes.length} 个兑换码</p>
            <p class="text-sm text-green-600 mt-1">批次ID: ${data.batchId}</p>
            <p class="text-sm text-green-600">档位: ${data.skuType}</p>
            <p class="text-sm text-green-600">时长: ${data.durationMonths} 个月</p>
        </div>
        <div class="code-list">
            ${data.codes.map(code => `
                <div class="code-item">
                    <span class="code-display">${code}</span>
                    <button onclick="window.copyToClipboard('${code}')" class="text-blue-600 hover:text-blue-800">
                        复制
                    </button>
                </div>
            `).join('')}
        </div>
    `;

    codesResultModal.classList.remove('hidden');
}

// 复制所有兑换码
function copyAllCodes() {
    const codesText = createdCodes.join('\n');
    navigator.clipboard.writeText(codesText).then(() => {
        showToast('已复制到剪贴板');
    }).catch(err => {
        console.error('复制失败:', err);
        showToast('复制失败', 3000);
    });
}

// 下载兑换码文件
function downloadCodesFile() {
    const codesText = createdCodes.join('\n');
    const blob = new Blob([codesText], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `codes_${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}

// 复制到剪贴板
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showToast('兑换码已复制到剪贴板', 2000);
    }).catch(err => {
        console.error('复制失败:', err);
        showToast('复制失败，请手动选择复制', 3000);
    });
}

// 暴露给全局
window.fetchActivationCodes = fetchActivationCodes;
window.deleteCode = deleteCode;
window.exportCodes = exportCodes;
window.showCreateCodesModal = showCreateCodesModal;
window.copyToClipboard = copyToClipboard;

// 导出模块初始化函数
window.initCodesModule = initCodesModule;
