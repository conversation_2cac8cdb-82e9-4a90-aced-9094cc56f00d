// 功能黑名单管理模块
// 依赖：需要全局变量 tiersConfig, currentAdminPassword, showToast, showLoading, hideLoading, getTierDisplayName, populateTierSelects

// DOM 元素
let featureBlacklistTab, featureBlacklistContent, featureBlacklistTableBody, featureBlacklistLoadingMessage;
let featureBlacklistTable, addFeatureButton;
let featureModal, featureModalTitle, featureForm, closeFeatureModal, cancelFeatureButton;
let saveFeatureButton, featureModalError;

// 初始化功能黑名单模块
function initFeatureBlacklistModule() {
    // 获取DOM元素
    featureBlacklistTab = document.getElementById('featureBlacklistTab');
    featureBlacklistContent = document.getElementById('featureBlacklistContent');
    featureBlacklistTableBody = document.getElementById('feature-blacklist-table-body');
    featureBlacklistLoadingMessage = document.getElementById('featureBlacklistLoadingMessage');
    featureBlacklistTable = document.getElementById('featureBlacklistTable');
    addFeatureButton = document.getElementById('addFeatureButton');
    featureModal = document.getElementById('featureModal');
    featureModalTitle = document.getElementById('featureModalTitle');
    featureForm = document.getElementById('featureForm');
    closeFeatureModal = document.getElementById('closeFeatureModal');
    cancelFeatureButton = document.getElementById('cancelFeatureButton');
    saveFeatureButton = document.getElementById('saveFeatureButton');
    featureModalError = document.getElementById('featureModalError');

    // 绑定事件监听器
    if (addFeatureButton) {
        addFeatureButton.addEventListener('click', openAddFeatureModal);
    }

    if (closeFeatureModal) {
        closeFeatureModal.addEventListener('click', () => featureModal.classList.add('hidden'));
    }

    if (cancelFeatureButton) {
        cancelFeatureButton.addEventListener('click', () => featureModal.classList.add('hidden'));
    }

    if (featureForm) {
        featureForm.addEventListener('submit', saveFeature);
    }

    // 点击模态框外部关闭
    window.addEventListener('click', (e) => {
        if (e.target === featureModal) featureModal.classList.add('hidden');
    });
}

// 功能黑名单管理函数
async function fetchFeatureBlacklist() {
    featureBlacklistLoadingMessage.classList.remove('hidden');
    featureBlacklistTableBody.innerHTML = '';

    try {
        const response = await fetch('/admin/feature-blacklist', {
            method: 'GET',
            headers: {
                'X-Admin-Password': currentAdminPassword
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error ${response.status}`);
        }

        const result = await response.json();
        if (result.success && result.data) {
            populateFeaturesTable(result.data);
        }
        // 显示表格
        if (featureBlacklistTable) featureBlacklistTable.classList.remove('hidden');
    } catch (error) {
        console.error('获取功能黑名单列表时出错:', error);
        showToast(`错误: ${error.message}`, 5000);
    } finally {
        featureBlacklistLoadingMessage.classList.add('hidden');
    }
}

// 填充功能黑名单表格
function populateFeaturesTable(features) {
    featureBlacklistTableBody.innerHTML = '';

    // 确保表格可见
    if (featureBlacklistTable) featureBlacklistTable.classList.remove('hidden');

    if (!features || features.length === 0) {
        featureBlacklistTableBody.innerHTML = '<tr><td colspan="5" class="text-center py-4 text-gray-500">暂无功能定义</td></tr>';
        return;
    }

    features.forEach(feature => {
        const row = document.createElement('tr');
        const allowedTiersDisplay = feature.allowedTiers.map(skuId => getTierDisplayName(skuId)).join(', ');

        row.innerHTML = `
            <td class="px-4 py-3 text-sm" data-label="显示名称">
                <span class="font-medium">${feature.displayName}</span>
            </td>
            <td class="px-4 py-3 text-sm" data-label="实际功能">
                <div class="max-w-xs">
                    ${feature.actualFeatures.map(f =>
                        `<span class="inline-block bg-gray-100 rounded px-2 py-1 text-xs mr-1 mb-1">${f}</span>`
                    ).join('')}
                </div>
            </td>
            <td class="px-4 py-3 text-sm" data-label="请求标识符">
                <div class="max-w-xs">
                    ${feature.requestIdentifiers.map(i =>
                        `<span class="inline-block bg-blue-100 rounded px-2 py-1 text-xs mr-1 mb-1">${i}</span>`
                    ).join('')}
                </div>
            </td>
            <td class="px-4 py-3 text-sm" data-label="可用套餐">
                <div class="max-w-xs">${allowedTiersDisplay}</div>
            </td>
            <td class="px-4 py-3 text-sm text-center" data-label="操作">
                <div class="flex justify-center gap-2">
                    <button onclick="openEditFeatureModal(${JSON.stringify(feature).replace(/"/g, '&quot;')})"
                            class="text-blue-600 hover:text-blue-900">编辑</button>
                    <button onclick="deleteFeature(${feature.id})"
                            class="text-red-600 hover:text-red-900">删除</button>
                </div>
            </td>
        `;
        featureBlacklistTableBody.appendChild(row);
    });
}

// 打开添加功能黑名单模态框
function openAddFeatureModal() {
    featureModalTitle.textContent = '添加功能定义';
    featureForm.reset();
    document.getElementById('featureId').value = '';
    if (window.TiersModule && window.TiersModule.populateTierSelects) {
        window.TiersModule.populateTierSelects(); // 确保档位选择框已填充
    }
    // 绑定黑名单提交处理器，防止误用白名单处理器
    if (featureForm) {
        try { featureForm.removeEventListener('submit', saveFeature); } catch (e) {}
        try { featureForm.removeEventListener('submit', saveWhitelistFeature); } catch (e) {}
        featureForm.addEventListener('submit', saveFeature);
    }
    featureModal.classList.remove('hidden');
}

// 打开编辑功能黑名单模态框
window.openEditFeatureModal = function(feature) {
    featureModalTitle.textContent = '编辑功能定义';
    document.getElementById('featureId').value = feature.id;
    document.getElementById('featureDisplayName').value = feature.displayName;
    document.getElementById('featureActualFeatures').value = feature.actualFeatures.join(',');
    document.getElementById('featureRequestIdentifiers').value = feature.requestIdentifiers.join(',');

    // 设置允许的套餐
    const checkboxes = document.querySelectorAll('input[name="allowedTiers"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = feature.allowedTiers.includes(checkbox.value);
    });
    // 绑定黑名单提交处理器，防止误用白名单处理器
    if (featureForm) {
        try { featureForm.removeEventListener('submit', saveFeature); } catch (e) {}
        try { featureForm.removeEventListener('submit', saveWhitelistFeature); } catch (e) {}
        featureForm.addEventListener('submit', saveFeature);
    }
    featureModal.classList.remove('hidden');
};

// 保存功能黑名单
async function saveFeature(e) {
    e.preventDefault();

    const featureId = document.getElementById('featureId').value;
    const displayName = document.getElementById('featureDisplayName').value.trim();
    const actualFeatures = document.getElementById('featureActualFeatures').value
        .split(',').map(f => f.trim()).filter(f => f);
    const requestIdentifiers = document.getElementById('featureRequestIdentifiers').value
        .split(',').map(i => i.trim()).filter(i => i);

    // 获取选中的套餐
    const allowedTiers = [];
    document.querySelectorAll('input[name="allowedTiers"]:checked').forEach(checkbox => {
        allowedTiers.push(checkbox.value);
    });

    if (!displayName || actualFeatures.length === 0 || requestIdentifiers.length === 0 || allowedTiers.length === 0) {
        featureModalError.textContent = '请填写所有必填字段';
        return;
    }

    showLoading(saveFeatureButton);
    featureModalError.textContent = '';

    try {
        const response = await fetch('/admin/feature-blacklist/save', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Admin-Password': currentAdminPassword
            },
            body: JSON.stringify({
                id: featureId || undefined,
                displayName,
                actualFeatures,
                requestIdentifiers,
                allowedTiers
            })
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.message || `HTTP error ${response.status}`);
        }

        const result = await response.json();
        if (result.success) {
            showToast(result.message);
            featureModal.classList.add('hidden');
            fetchFeatureBlacklist();
        }
    } catch (error) {
        console.error('保存功能定义时出错:', error);
        featureModalError.textContent = error.message;
    } finally {
        hideLoading(saveFeatureButton);
    }
}

// 删除功能黑名单
window.deleteFeature = async function(featureId) {
    if (!confirm('确定要删除这个功能定义吗？相关的群组设置也会被清除。')) {
        return;
    }

    try {
        const response = await fetch('/admin/feature-blacklist/delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Admin-Password': currentAdminPassword
            },
            body: JSON.stringify({ id: featureId })
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.message || `HTTP error ${response.status}`);
        }

        const result = await response.json();
        if (result.success) {
            showToast(result.message);
            fetchFeatureBlacklist();
        }
    } catch (error) {
        console.error('删除功能定义时出错:', error);
        showToast(`错误: ${error.message}`, 5000);
    }
};

// 切换到功能黑名单tab的处理函数
function onFeatureBlacklistTabClick() {
    // DOM操作现在由switchTab函数处理，这里只负责数据加载
    fetchFeatureBlacklist();
}

// 初始化模块
document.addEventListener('DOMContentLoaded', initFeatureBlacklistModule);
