// 功能白名单管理模块
// 依赖：需要全局变量 tiersConfig, currentAdminPassword, showToast, showLoading, hideLoading, getTierDisplayName, populateTierSelects
// 注意：featureModal相关的DOM元素已在feature-blacklist.js中声明，这里不再重复声明

// DOM 元素（白名单特有的）
let featureWhitelistTab, featureWhitelistContent, featureWhitelistTableBody, featureWhitelistLoadingMessage;
let featureWhitelistTable, addWhitelistFeatureButton;

// 初始化功能白名单模块
function initFeatureWhitelistModule() {
    // 获取DOM元素（白名单特有的）
    featureWhitelistTab = document.getElementById('featureWhitelistTab');
    featureWhitelistContent = document.getElementById('featureWhitelistContent');
    featureWhitelistTableBody = document.getElementById('feature-whitelist-table-body');
    featureWhitelistLoadingMessage = document.getElementById('featureWhitelistLoadingMessage');
    featureWhitelistTable = document.getElementById('featureWhitelistTable');
    addWhitelistFeatureButton = document.getElementById('addWhitelistFeatureButton');

    // 绑定事件监听器
    if (addWhitelistFeatureButton) {
        addWhitelistFeatureButton.addEventListener('click', openAddWhitelistFeatureModal);
    }
}

// 功能白名单管理函数
async function fetchFeatureWhitelist() {
    if (!featureWhitelistLoadingMessage) return;

    featureWhitelistLoadingMessage.classList.remove('hidden');
    if (featureWhitelistTableBody) featureWhitelistTableBody.innerHTML = '';

    try {
        const response = await fetch('/admin/feature-whitelist', {
            method: 'GET',
            headers: {
                'X-Admin-Password': currentAdminPassword
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error ${response.status}`);
        }

        const result = await response.json();
        if (result.success && result.data) {
            populateWhitelistFeaturesTable(result.data);
        }
        // 显示表格
        if (featureWhitelistTable) featureWhitelistTable.classList.remove('hidden');
    } catch (error) {
        console.error('获取功能白名单列表时出错:', error);
        showToast(`错误: ${error.message}`, 5000);
    } finally {
        if (featureWhitelistLoadingMessage) featureWhitelistLoadingMessage.classList.add('hidden');
    }
}

// 填充功能白名单表格
function populateWhitelistFeaturesTable(features) {
    if (!featureWhitelistTableBody) return;

    featureWhitelistTableBody.innerHTML = '';

    // 确保表格可见
    if (featureWhitelistTable) featureWhitelistTable.classList.remove('hidden');

    if (!features || features.length === 0) {
        featureWhitelistTableBody.innerHTML = '<tr><td colspan="5" class="text-center py-4 text-gray-500">暂无功能定义</td></tr>';
        return;
    }

    features.forEach(feature => {
        const row = document.createElement('tr');
        const allowedTiersDisplay = feature.allowedTiers.map(skuId => getTierDisplayName(skuId)).join(', ');

        row.innerHTML = `
            <td class="px-4 py-3 text-sm" data-label="显示名称">
                <span class="font-medium">${feature.displayName}</span>
            </td>
            <td class="px-4 py-3 text-sm" data-label="实际功能">
                <div class="max-w-xs">
                    ${feature.actualFeatures.map(f =>
                        `<span class="inline-block bg-gray-100 rounded px-2 py-1 text-xs mr-1 mb-1">${f}</span>`
                    ).join('')}
                </div>
            </td>
            <td class="px-4 py-3 text-sm" data-label="请求标识符">
                <div class="max-w-xs">
                    ${feature.requestIdentifiers.map(i =>
                        `<span class="inline-block bg-blue-100 rounded px-2 py-1 text-xs mr-1 mb-1">${i}</span>`
                    ).join('')}
                </div>
            </td>
            <td class="px-4 py-3 text-sm" data-label="可用套餐">
                <div class="max-w-xs">${allowedTiersDisplay}</div>
            </td>
            <td class="px-4 py-3 text-sm text-center" data-label="操作">
                <div class="flex justify-center gap-2">
                    <button onclick="openEditWhitelistFeatureModal(${JSON.stringify(feature).replace(/"/g, '&quot;')})"
                            class="text-blue-600 hover:text-blue-900">编辑</button>
                    <button onclick="deleteWhitelistFeature(${feature.id})"
                            class="text-red-600 hover:text-red-900">删除</button>
                </div>
            </td>
        `;
        featureWhitelistTableBody.appendChild(row);
    });
}

// 打开添加功能白名单模态框
function openAddWhitelistFeatureModal() {
    const modalTitle = document.getElementById('featureModalTitle');
    const modalForm = document.getElementById('featureForm');
    const modal = document.getElementById('featureModal');

    if (!modalTitle || !modalForm || !modal) return;

    modalTitle.textContent = '添加功能白名单定义';
    modalForm.reset();
    document.getElementById('featureId').value = '';
    if (window.TiersModule && window.TiersModule.populateTierSelects) {
        window.TiersModule.populateTierSelects(); // 确保档位选择框已填充
    }
    // 绑定白名单提交处理器，防止误用黑名单处理器
    if (modalForm) {
        try { modalForm.removeEventListener('submit', saveFeature); } catch (e) {}
        try { modalForm.removeEventListener('submit', saveWhitelistFeature); } catch (e) {}
        modalForm.addEventListener('submit', saveWhitelistFeature);
    }
    modal.classList.remove('hidden');
}

// 打开编辑功能白名单模态框
window.openEditWhitelistFeatureModal = function(feature) {
    const modalTitle = document.getElementById('featureModalTitle');
    const modal = document.getElementById('featureModal');

    if (!modalTitle || !modal) return;

    modalTitle.textContent = '编辑功能白名单定义';

    const featureId = document.getElementById('featureId');
    const featureDisplayName = document.getElementById('featureDisplayName');
    const featureActualFeatures = document.getElementById('featureActualFeatures');
    const featureRequestIdentifiers = document.getElementById('featureRequestIdentifiers');

    if (featureId) featureId.value = feature.id;
    if (featureDisplayName) featureDisplayName.value = feature.displayName;
    if (featureActualFeatures) featureActualFeatures.value = feature.actualFeatures.join(',');
    if (featureRequestIdentifiers) featureRequestIdentifiers.value = feature.requestIdentifiers.join(',');

    // 设置允许的套餐
    const checkboxes = document.querySelectorAll('input[name="allowedTiers"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = feature.allowedTiers.includes(checkbox.value);
    });
    // 绑定白名单提交处理器，防止误用黑名单处理器
    const modalForm = document.getElementById('featureForm');
    if (modalForm) {
        try { modalForm.removeEventListener('submit', saveFeature); } catch (e) {}
        try { modalForm.removeEventListener('submit', saveWhitelistFeature); } catch (e) {}
        modalForm.addEventListener('submit', saveWhitelistFeature);
    }
    modal.classList.remove('hidden');
};

// 保存功能白名单
async function saveWhitelistFeature(e) {
    e.preventDefault();

    const featureId = document.getElementById('featureId').value;
    const displayName = document.getElementById('featureDisplayName').value.trim();
    const actualFeatures = document.getElementById('featureActualFeatures').value
        .split(',').map(f => f.trim()).filter(f => f);
    const requestIdentifiers = document.getElementById('featureRequestIdentifiers').value
        .split(',').map(i => i.trim()).filter(i => i);

    // 获取选中的套餐
    const allowedTiers = [];
    document.querySelectorAll('input[name="allowedTiers"]:checked').forEach(checkbox => {
        allowedTiers.push(checkbox.value);
    });

    if (!displayName || actualFeatures.length === 0 || requestIdentifiers.length === 0 || allowedTiers.length === 0) {
        const modalError = document.getElementById('featureModalError');
        if (modalError) modalError.textContent = '请填写所有必填字段';
        return;
    }

    const modalSaveButton = document.getElementById('saveFeatureButton');
    if (modalSaveButton) showLoading(modalSaveButton);
    const modalError = document.getElementById('featureModalError');
    if (modalError) modalError.textContent = '';

    try {
        const response = await fetch('/admin/feature-whitelist/save', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Admin-Password': currentAdminPassword
            },
            body: JSON.stringify({
                id: featureId || undefined,
                displayName,
                actualFeatures,
                requestIdentifiers,
                allowedTiers
            })
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.message || `HTTP error ${response.status}`);
        }

        const result = await response.json();
        if (result.success) {
            showToast(result.message);
            const modal = document.getElementById('featureModal');
            if (modal) modal.classList.add('hidden');
            fetchFeatureWhitelist();
        }
    } catch (error) {
        console.error('保存功能白名单定义时出错:', error);
        const modalError = document.getElementById('featureModalError');
        if (modalError) modalError.textContent = error.message;
    } finally {
        const modalSaveButton = document.getElementById('saveFeatureButton');
        if (modalSaveButton) hideLoading(modalSaveButton);
    }
}

// 删除功能白名单
window.deleteWhitelistFeature = async function(featureId) {
    if (!confirm('确定要删除这个功能白名单定义吗？相关的群组设置也会被清除。')) {
        return;
    }

    try {
        const response = await fetch('/admin/feature-whitelist/delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Admin-Password': currentAdminPassword
            },
            body: JSON.stringify({ id: featureId })
        });

        if (!response.ok) {
            throw new Error(`HTTP error ${response.status}`);
        }

        const result = await response.json();
        if (result.success) {
            showToast('删除成功');
            fetchFeatureWhitelist();
        }
    } catch (error) {
        console.error('删除功能白名单定义时出错:', error);
        showToast(`错误: ${error.message}`, 5000);
    }
};

// 切换到功能白名单tab的处理函数
function onFeatureWhitelistTabClick() {
    // DOM操作现在由switchTab函数处理，这里只负责数据加载
    fetchFeatureWhitelist();
}

// 初始化模块
document.addEventListener('DOMContentLoaded', initFeatureWhitelistModule);
