// 按次付费功能管理模块
// 依赖：需要全局变量 currentAdminPassword, showToast, showModal

// DOM 元素
let payPerUseTab;
let payPerUseContent;
let payPerUseFeaturesTab;
let payPerUseCodesTab;
let payPerUseBillingTab;
let payPerUseLogsTab;
let payPerUseFeaturesContent;
let payPerUseCodesContent;
let payPerUseBillingContent;
let payPerUseLogsContent;

// 按次付费功能管理元素
let createPayPerUseFeatureButton;
let payPerUseFeaturesTableBody;

// 按次付费兑换码管理元素
let generatePayPerUseCodesButton;
let payPerUseCodesTableBody;
let payPerUseCodesPrevPage;
let payPerUseCodesNextPage;
let payPerUseCodesPaginationInfo;

// 按次付费搜索元素
let payPerUseCodesSearchInput;
let searchPayPerUseCodesButton;

// 按次付费计费账户管理元素
let payPerUseBillingTableBody;
let payPerUseBillingPrevPage;
let payPerUseBillingNextPage;
let payPerUseBillingPaginationInfo;

// 按次付费计费账户搜索元素
let payPerUseBillingSearchInput;
let searchPayPerUseBillingButton;

// 按次付费使用日志元素
let payPerUseLogsTableBody;
let payPerUseLogsPrevPage;
let payPerUseLogsNextPage;
let payPerUseLogsPaginationInfo;

// 按次付费使用日志搜索元素
let payPerUseLogsGroupSearchInput;
let payPerUseLogsDateFromInput;
let payPerUseLogsDateToInput;
let searchPayPerUseLogsButton;

// 按次付费弹窗元素
let payPerUseFeatureModal;
let payPerUseFeatureForm;
let payPerUseFeatureModalTitle;
let payPerUseFeatureId;
let payPerUseFeatureCode;
let payPerUseFeatureName;
let payPerUseFeatureDescription;
let payPerUseFeatureEndpoint;
let payPerUseFeatureActive;
let cancelPayPerUseFeature;
let savePayPerUseFeature;

let payPerUseCodeModal;
let payPerUseCodeForm;
let payPerUseCodeFeaturesContainer;
let payPerUseCodeQuantity;
let payPerUseCodeBatchId;
let payPerUseCodeNote;
let payPerUseCodeExpiry;
let cancelPayPerUseCode;
let generatePayPerUseCode;

let payPerUseCodeResultModal;
let payPerUseCodeResultList;
let copyPayPerUseCodes;
let closePayPerUseCodeResult;

// 初始化按次付费功能管理模块
function initPayPerUseModule() {
    // 获取DOM元素
    payPerUseTab = document.getElementById('payPerUseTab');
    payPerUseContent = document.getElementById('payPerUseContent');
    payPerUseFeaturesTab = document.getElementById('payPerUseFeaturesTab');
    payPerUseCodesTab = document.getElementById('payPerUseCodesTab');
    payPerUseBillingTab = document.getElementById('payPerUseBillingTab');
    payPerUseLogsTab = document.getElementById('payPerUseLogsTab');

    payPerUseFeaturesContent = document.getElementById('payPerUseFeaturesContent');
    payPerUseCodesContent = document.getElementById('payPerUseCodesContent');
    payPerUseBillingContent = document.getElementById('payPerUseBillingContent');
    payPerUseLogsContent = document.getElementById('payPerUseLogsContent');

    // 按次付费功能管理元素
    createPayPerUseFeatureButton = document.getElementById('createPayPerUseFeatureButton');
    payPerUseFeaturesTableBody = document.getElementById('payPerUseFeaturesTableBody');

    // 按次付费兑换码管理元素
    generatePayPerUseCodesButton = document.getElementById('generatePayPerUseCodesButton');
    payPerUseCodesTableBody = document.getElementById('payPerUseCodesTableBody');
    payPerUseCodesPrevPage = document.getElementById('payPerUseCodesPrevPage');
    payPerUseCodesNextPage = document.getElementById('payPerUseCodesNextPage');
    payPerUseCodesPaginationInfo = document.getElementById('payPerUseCodesPaginationInfo');

    // 搜索相关元素
    payPerUseCodesSearchInput = document.getElementById('payPerUseCodesSearchInput');
    searchPayPerUseCodesButton = document.getElementById('searchPayPerUseCodesButton');

    // 按次付费计费账户管理元素
    payPerUseBillingTableBody = document.getElementById('payPerUseBillingTableBody');
    payPerUseBillingPrevPage = document.getElementById('payPerUseBillingPrevPage');
    payPerUseBillingNextPage = document.getElementById('payPerUseBillingNextPage');
    payPerUseBillingPaginationInfo = document.getElementById('payPerUseBillingPaginationInfo');

    // 计费账户搜索元素
    payPerUseBillingSearchInput = document.getElementById('payPerUseBillingSearchInput');
    searchPayPerUseBillingButton = document.getElementById('searchPayPerUseBillingButton');

    // 按次付费使用日志元素
    payPerUseLogsTableBody = document.getElementById('payPerUseLogsTableBody');
    payPerUseLogsPrevPage = document.getElementById('payPerUseLogsPrevPage');
    payPerUseLogsNextPage = document.getElementById('payPerUseLogsNextPage');
    payPerUseLogsPaginationInfo = document.getElementById('payPerUseLogsPaginationInfo');

    // 使用日志搜索元素
    payPerUseLogsGroupSearchInput = document.getElementById('payPerUseLogsGroupSearchInput');
    payPerUseLogsDateFromInput = document.getElementById('payPerUseLogsDateFromInput');
    payPerUseLogsDateToInput = document.getElementById('payPerUseLogsDateToInput');
    searchPayPerUseLogsButton = document.getElementById('searchPayPerUseLogsButton');

    // 按次付费弹窗元素
    payPerUseFeatureModal = document.getElementById('payPerUseFeatureModal');
    payPerUseFeatureForm = document.getElementById('payPerUseFeatureForm');
    payPerUseFeatureModalTitle = document.getElementById('payPerUseFeatureModalTitle');
    payPerUseFeatureId = document.getElementById('payPerUseFeatureId');
    payPerUseFeatureCode = document.getElementById('payPerUseFeatureCode');
    payPerUseFeatureName = document.getElementById('payPerUseFeatureName');
    payPerUseFeatureDescription = document.getElementById('payPerUseFeatureDescription');
    payPerUseFeatureEndpoint = document.getElementById('payPerUseFeatureEndpoint');
    payPerUseFeatureActive = document.getElementById('payPerUseFeatureActive');
    cancelPayPerUseFeature = document.getElementById('cancelPayPerUseFeature');
    savePayPerUseFeature = document.getElementById('savePayPerUseFeature');

    payPerUseCodeModal = document.getElementById('payPerUseCodeModal');
    payPerUseCodeForm = document.getElementById('payPerUseCodeForm');
    payPerUseCodeFeaturesContainer = document.getElementById('payPerUseCodeFeaturesContainer');
    payPerUseCodeQuantity = document.getElementById('payPerUseCodeQuantity');
    payPerUseCodeBatchId = document.getElementById('payPerUseCodeBatchId');
    payPerUseCodeNote = document.getElementById('payPerUseCodeNote');
    payPerUseCodeExpiry = document.getElementById('payPerUseCodeExpiry');
    cancelPayPerUseCode = document.getElementById('cancelPayPerUseCode');
    generatePayPerUseCode = document.getElementById('generatePayPerUseCode');

    payPerUseCodeResultModal = document.getElementById('payPerUseCodeResultModal');
    payPerUseCodeResultList = document.getElementById('payPerUseCodeResultList');
    copyPayPerUseCodes = document.getElementById('copyPayPerUseCodes');
    closePayPerUseCodeResult = document.getElementById('closePayPerUseCodeResult');

    // 绑定事件监听器
    bindPayPerUseEvents();

    // 验证搜索元素是否正确获取
    console.log('=== 按次付费搜索元素验证 ===');
    console.log('payPerUseCodesSearchInput:', payPerUseCodesSearchInput);
    console.log('searchPayPerUseCodesButton:', searchPayPerUseCodesButton);
    console.log('payPerUseBillingSearchInput:', payPerUseBillingSearchInput);
    console.log('searchPayPerUseBillingButton:', searchPayPerUseBillingButton);
    console.log('payPerUseLogsGroupSearchInput:', payPerUseLogsGroupSearchInput);
    console.log('payPerUseLogsDateFromInput:', payPerUseLogsDateFromInput);
    console.log('payPerUseLogsDateToInput:', payPerUseLogsDateToInput);
    console.log('searchPayPerUseLogsButton:', searchPayPerUseLogsButton);
    console.log('================================');

    console.log('按次付费功能管理模块初始化完成');
}

// ==================== 按次付费Tab切换 ====================

// 按次付费Tab切换
if (typeof window !== 'undefined') {
    // 使用DOMContentLoaded确保DOM加载完成后再绑定事件
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', bindPayPerUseTab);
    } else {
        // DOM已经加载完成，直接绑定
        bindPayPerUseTab();
    }

    function bindPayPerUseTab() {
        // 额外延迟确保所有脚本都加载完成
        setTimeout(() => {
            const payPerUseTabElement = document.getElementById('payPerUseTab');
            const payPerUseContentElement = document.getElementById('payPerUseContent');

            console.log('绑定按次付费Tab事件...');
            console.log('payPerUseTabElement:', payPerUseTabElement);
            console.log('payPerUseContentElement:', payPerUseContentElement);

            if (payPerUseTabElement) {
                payPerUseTabElement.addEventListener('click', () => {
                    console.log('按次付费标签页被点击');
                    if (typeof switchTab === 'function') {
                        console.log('调用switchTab函数');
                        switchTab('pay-per-use');
                    } else {
                        console.error('switchTab函数未找到');
                    }
                });
                console.log('按次付费Tab事件绑定成功');
            } else {
                console.error('payPerUseTab 元素未找到');
            }
        }, 200); // 增加延迟时间到200ms
    }
}

// 绑定按次付费相关事件
function bindPayPerUseEvents() {
    // 按次付费子标签切换
    if (payPerUseFeaturesTab) payPerUseFeaturesTab.addEventListener('click', () => {
        switchPayPerUseSubTab(payPerUseFeaturesTab, payPerUseFeaturesContent);
        fetchPayPerUseFeatures();
    });

    if (payPerUseCodesTab) payPerUseCodesTab.addEventListener('click', () => {
        switchPayPerUseSubTab(payPerUseCodesTab, payPerUseCodesContent);
        fetchPayPerUseCodes();
    });

    if (payPerUseBillingTab) payPerUseBillingTab.addEventListener('click', () => {
        switchPayPerUseSubTab(payPerUseBillingTab, payPerUseBillingContent);
        fetchPayPerUseBilling();
    });

    if (payPerUseLogsTab) payPerUseLogsTab.addEventListener('click', () => {
        switchPayPerUseSubTab(payPerUseLogsTab, payPerUseLogsContent);
        fetchPayPerUseLogs();
    });

    // 新增功能按钮
    if (createPayPerUseFeatureButton) {
        createPayPerUseFeatureButton.addEventListener('click', () => {
            openPayPerUseFeatureModal();
        });
    }

    // 生成兑换码按钮
    if (generatePayPerUseCodesButton) {
        generatePayPerUseCodesButton.addEventListener('click', () => {
            openPayPerUseCodeModal();
        });
    }

    // 功能管理弹窗事件
    if (cancelPayPerUseFeature) {
        cancelPayPerUseFeature.addEventListener('click', () => {
            closePayPerUseFeatureModal();
        });
    }

    if (payPerUseFeatureForm) {
        payPerUseFeatureForm.addEventListener('submit', (e) => {
            e.preventDefault();
            savePayPerUseFeatureData();
        });
    }

    // 兑换码生成弹窗事件
    if (cancelPayPerUseCode) {
        cancelPayPerUseCode.addEventListener('click', () => {
            closePayPerUseCodeModal();
        });
    }

    if (payPerUseCodeForm) {
        payPerUseCodeForm.addEventListener('submit', (e) => {
            e.preventDefault();
            generatePayPerUseCodes();
        });
    }

    // 兑换码结果弹窗事件
    if (copyPayPerUseCodes) {
        copyPayPerUseCodes.addEventListener('click', () => {
            copyGeneratedCodes();
        });
    }

    if (closePayPerUseCodeResult) {
        closePayPerUseCodeResult.addEventListener('click', () => {
            closePayPerUseCodeResultModal();
        });
    }

    // 分页事件监听器
    if (payPerUseCodesPrevPage) {
        payPerUseCodesPrevPage.addEventListener('click', () => {
            const currentPage = parseInt(payPerUseCodesPaginationInfo.textContent.match(/第 (\d+) 页/)?.[1] || '1');
            if (currentPage > 1) {
                fetchPayPerUseCodes(currentPage - 1);
            }
        });
    }

    if (payPerUseCodesNextPage) {
        payPerUseCodesNextPage.addEventListener('click', () => {
            const currentPage = parseInt(payPerUseCodesPaginationInfo.textContent.match(/第 (\d+) 页/)?.[1] || '1');
            fetchPayPerUseCodes(currentPage + 1);
        });
    }

    if (payPerUseBillingPrevPage) {
        payPerUseBillingPrevPage.addEventListener('click', () => {
            const currentPage = parseInt(payPerUseBillingPaginationInfo.textContent.split(' ')[1]);
            if (currentPage > 1) {
                fetchPayPerUseBilling(currentPage - 1);
            }
        });
    }

    if (payPerUseBillingNextPage) {
        payPerUseBillingNextPage.addEventListener('click', () => {
            const currentPage = parseInt(payPerUseBillingPaginationInfo.textContent.split(' ')[1]);
            fetchPayPerUseBilling(currentPage + 1);
        });
    }

    if (payPerUseLogsPrevPage) {
        payPerUseLogsPrevPage.addEventListener('click', () => {
            const currentPage = parseInt(payPerUseLogsPaginationInfo.textContent.split(' ')[1]);
            if (currentPage > 1) {
                fetchPayPerUseLogs(currentPage - 1);
            }
        });
    }

    if (payPerUseLogsNextPage) {
        payPerUseLogsNextPage.addEventListener('click', () => {
            const currentPage = parseInt(payPerUseLogsPaginationInfo.textContent.split(' ')[1]);
            fetchPayPerUseLogs(currentPage + 1);
        });
    }

    // 搜索按钮事件绑定
    console.log('=== 绑定搜索按钮事件 ===');
    if (searchPayPerUseCodesButton) {
        searchPayPerUseCodesButton.addEventListener('click', () => {
            console.log('兑换码搜索按钮被点击');
            searchPayPerUseCodes();
        });
        console.log('兑换码搜索按钮事件绑定成功');
    } else {
        console.error('兑换码搜索按钮未找到');
    }

    if (searchPayPerUseBillingButton) {
        searchPayPerUseBillingButton.addEventListener('click', () => {
            console.log('计费账户搜索按钮被点击');
            searchPayPerUseBilling();
        });
        console.log('计费账户搜索按钮事件绑定成功');
    } else {
        console.error('计费账户搜索按钮未找到');
    }

    if (searchPayPerUseLogsButton) {
        searchPayPerUseLogsButton.addEventListener('click', () => {
            console.log('使用日志搜索按钮被点击');
            searchPayPerUseLogs();
        });
        console.log('使用日志搜索按钮事件绑定成功');
    } else {
        console.error('使用日志搜索按钮未找到');
    }
    console.log('===========================');
}

// 按次付费子标签切换函数
function switchPayPerUseSubTab(activeTab, activeContent) {
    // 移除所有子标签的active类
    document.querySelectorAll('.pay-per-use-subtab').forEach(tab => {
        tab.classList.remove('active');
    });

    // 隐藏所有子内容
    document.querySelectorAll('.pay-per-use-subcontent').forEach(content => {
        content.classList.add('hidden');
    });

    // 激活当前标签和内容
    activeTab.classList.add('active');
    activeContent.classList.remove('hidden');
}

// ==================== 按次付费功能实现 ====================

// 获取按次付费功能列表
async function fetchPayPerUseFeatures() {
    try {
        const response = await fetch('/admin/pay-per-use/features', {
            headers: {
                'X-Admin-Password': currentAdminPassword
            }
        });
        const result = await response.json();

        if (result.success) {
            renderPayPerUseFeaturesTable(result.data);
        } else {
            showToast('获取功能列表失败: ' + result.message);
        }
    } catch (error) {
        console.error('获取按次付费功能列表时出错:', error);
        showToast('获取功能列表失败');
    }
}

// 渲染按次付费功能表格
function renderPayPerUseFeaturesTable(features) {
    if (!payPerUseFeaturesTableBody) return;

    payPerUseFeaturesTableBody.innerHTML = '';

    if (features.length === 0) {
        payPerUseFeaturesTableBody.innerHTML = '<tr><td colspan="8" class="text-center py-4">暂无数据</td></tr>';
        return;
    }

    features.forEach(feature => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${feature.id}</td>
            <td>${feature.feature_code}</td>
            <td>${feature.display_name}</td>
            <td>${feature.description || ''}</td>
            <td>${feature.api_endpoint || ''}</td>
            <td><span class="px-2 py-1 rounded text-sm ${feature.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">${feature.is_active ? '启用' : '禁用'}</span></td>
            <td>${new Date(feature.created_time).toLocaleString()}</td>
            <td>
                <button onclick="editPayPerUseFeature(${feature.id})" class="text-blue-600 hover:text-blue-800 mr-2">编辑</button>
                <button onclick="deletePayPerUseFeature(${feature.id})" class="text-red-600 hover:text-red-800">删除</button>
            </td>
        `;
        payPerUseFeaturesTableBody.appendChild(row);
    });
}

// 获取按次付费兑换码列表
async function fetchPayPerUseCodes(page = 1) {
    try {
        const url = `/admin/pay-per-use/codes?page=${page}&per_page=20`;
        console.log('fetchPayPerUseCodes URL:', url);

        const response = await fetch(url, {
            headers: {
                'X-Admin-Password': currentAdminPassword
            }
        });
        const result = await response.json();

        if (result.success) {
            renderPayPerUseCodesTable(result.data);
            updatePayPerUseCodesPagination(result.pagination);
        } else {
            showToast('获取兑换码列表失败: ' + result.message);
        }
    } catch (error) {
        console.error('获取按次付费兑换码列表时出错:', error);
        showToast('获取兑换码列表失败');
    }
}

// 渲染按次付费兑换码表格
function renderPayPerUseCodesTable(codes) {
    if (!payPerUseCodesTableBody) return;

    // 保存原始数据用于前端搜索
    if (codes && codes.length > 0) {
        originalPayPerUseCodesData = [...codes];
    }

    payPerUseCodesTableBody.innerHTML = '';

    if (codes.length === 0) {
        payPerUseCodesTableBody.innerHTML = '<tr><td colspan="9" class="text-center py-4">暂无数据</td></tr>';
        return;
    }

    codes.forEach(code => {
        const row = document.createElement('tr');
        const statusText = code.is_used ? '已使用' : '未使用';
        const statusClass = code.is_used ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800';

        row.innerHTML = `
            <td>${code.id}</td>
            <td><code class="bg-gray-100 px-2 py-1 rounded">${code.code}</code></td>
            <td>${code.feature_info || ''}</td>
            <td>${code.note || ''}</td>
            <td>${code.batch_id || ''}</td>
            <td><span class="px-2 py-1 rounded text-sm ${statusClass}">${statusText}</span></td>
            <td>${code.expires_at ? new Date(code.expires_at).toLocaleString() : '永久'}</td>
            <td>${new Date(code.created_time).toLocaleString()}</td>
            <td>
                <button onclick="deletePayPerUseCode(${code.id})" class="text-red-600 hover:text-red-800">删除</button>
            </td>
        `;
        payPerUseCodesTableBody.appendChild(row);
    });
}

// 更新按次付费兑换码分页信息
function updatePayPerUseCodesPagination(pagination) {
    if (payPerUseCodesPaginationInfo) {
        payPerUseCodesPaginationInfo.textContent = `第 ${pagination.page} 页，共 ${pagination.total} 条记录`;
    }
    if (payPerUseCodesPrevPage) {
        payPerUseCodesPrevPage.disabled = pagination.page <= 1;
    }
    if (payPerUseCodesNextPage) {
        payPerUseCodesNextPage.disabled = pagination.page >= pagination.pages;
    }
}

// 获取按次付费计费账户列表
async function fetchPayPerUseBilling(page = 1) {
    try {
        const url = `/admin/pay-per-use/billing?page=${page}&per_page=20`;
        console.log('fetchPayPerUseBilling URL:', url);

        const response = await fetch(url, {
            headers: {
                'X-Admin-Password': currentAdminPassword
            }
        });
        const result = await response.json();

        if (result.success) {
            renderPayPerUseBillingTable(result.data);
            updatePayPerUseBillingPagination(result.pagination);
        } else {
            showToast('获取计费账户列表失败: ' + result.message);
        }
    } catch (error) {
        console.error('获取按次付费计费账户列表时出错:', error);
        showToast('获取计费账户列表失败');
    }
}

// 渲染按次付费计费账户表格
function renderPayPerUseBillingTable(accounts) {
    if (!payPerUseBillingTableBody) return;

    // 保存原始数据用于前端搜索
    if (accounts && accounts.length > 0) {
        originalPayPerUseBillingData = [...accounts];
    }

    payPerUseBillingTableBody.innerHTML = '';

    if (accounts.length === 0) {
        payPerUseBillingTableBody.innerHTML = '<tr><td colspan="7" class="text-center py-4">暂无数据</td></tr>';
        return;
    }

    accounts.forEach(account => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${account.id}</td>
            <td>${account.group_number}</td>
            <td>${account.feature_name || ''}</td>
            <td><span class="font-semibold text-blue-600">${account.remaining_count}</span></td>
            <td>${account.total_purchased}</td>
            <td>${new Date(account.updated_time).toLocaleString()}</td>
            <td>
                <button onclick="adjustPayPerUseBilling(${account.id}, '${account.group_number}', ${account.feature_id}, ${account.tier_id})" class="text-green-600 hover:text-green-800 mr-2">调整余额</button>
            </td>
        `;
        payPerUseBillingTableBody.appendChild(row);
    });
}

// 更新按次付费计费账户分页信息
function updatePayPerUseBillingPagination(pagination) {
    if (payPerUseBillingPaginationInfo) {
        payPerUseBillingPaginationInfo.textContent = `第 ${pagination.page} 页，共 ${pagination.total} 条记录`;
    }
    if (payPerUseBillingPrevPage) {
        payPerUseBillingPrevPage.disabled = pagination.page <= 1;
    }
    if (payPerUseBillingNextPage) {
        payPerUseBillingNextPage.disabled = pagination.page >= pagination.pages;
    }
}

// 获取按次付费使用日志
async function fetchPayPerUseLogs(page = 1) {
    try {
        const url = `/admin/pay-per-use/logs?page=${page}&per_page=20`;
        console.log('fetchPayPerUseLogs URL:', url);

        const response = await fetch(url, {
            headers: {
                'X-Admin-Password': currentAdminPassword
            }
        });
        const result = await response.json();

        if (result.success) {
            renderPayPerUseLogsTable(result.data);
            updatePayPerUseLogsPagination(result.pagination);
        } else {
            showToast('获取使用日志失败: ' + result.message);
        }
    } catch (error) {
        console.error('获取按次付费使用日志时出错:', error);
        showToast('获取使用日志失败');
    }
}

// 渲染按次付费使用日志表格
function renderPayPerUseLogsTable(logs) {
    if (!payPerUseLogsTableBody) return;

    // 保存原始数据用于前端搜索
    if (logs && logs.length > 0) {
        originalPayPerUseLogsData = [...logs];
    }

    payPerUseLogsTableBody.innerHTML = '';

    if (logs.length === 0) {
        payPerUseLogsTableBody.innerHTML = '<tr><td colspan="6" class="text-center py-4">暂无数据</td></tr>';
        return;
    }

    logs.forEach(log => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${log.id}</td>
            <td>${log.group_number}</td>
            <td>${log.feature_name || ''}</td>
            <td>${log.usage_count}</td>
            <td>${log.request_note || ''}</td>
            <td>${new Date(log.request_time).toLocaleString()}</td>
        `;
        payPerUseLogsTableBody.appendChild(row);
    });
}

// 更新按次付费使用日志分页信息
function updatePayPerUseLogsPagination(pagination) {
    if (payPerUseLogsPaginationInfo) {
        payPerUseLogsPaginationInfo.textContent = `第 ${pagination.page} 页，共 ${pagination.total} 条记录`;
    }
    if (payPerUseLogsPrevPage) {
        payPerUseLogsPrevPage.disabled = pagination.page <= 1;
    }
    if (payPerUseLogsNextPage) {
        payPerUseLogsNextPage.disabled = pagination.page >= pagination.pages;
    }
}

// 打开新增功能弹窗
function openPayPerUseFeatureModal(featureId = null) {
    if (featureId) {
        // 编辑模式
        if (payPerUseFeatureModalTitle) payPerUseFeatureModalTitle.textContent = '编辑功能';
        loadPayPerUseFeatureData(featureId);
    } else {
        // 新增模式
        if (payPerUseFeatureModalTitle) payPerUseFeatureModalTitle.textContent = '新增功能';
        resetPayPerUseFeatureForm();
    }

    if (payPerUseFeatureModal) {
        payPerUseFeatureModal.classList.remove('hidden');
    }
}

// 关闭新增功能弹窗
function closePayPerUseFeatureModal() {
    if (payPerUseFeatureModal) {
        payPerUseFeatureModal.classList.add('hidden');
    }
    resetPayPerUseFeatureForm();
}

// 重置功能表单
function resetPayPerUseFeatureForm() {
    if (payPerUseFeatureId) payPerUseFeatureId.value = '';
    if (payPerUseFeatureCode) payPerUseFeatureCode.value = '';
    if (payPerUseFeatureName) payPerUseFeatureName.value = '';
    if (payPerUseFeatureDescription) payPerUseFeatureDescription.value = '';
    if (payPerUseFeatureEndpoint) payPerUseFeatureEndpoint.value = '';
    if (payPerUseFeatureActive) payPerUseFeatureActive.checked = true;

    // 添加功能代码输入监听，自动更新API端点显示
    if (payPerUseFeatureCode) {
        payPerUseFeatureCode.addEventListener('input', function() {
            const featureCode = this.value.trim();
            if (featureCode && payPerUseFeatureEndpoint) {
                payPerUseFeatureEndpoint.value = `/api/pay-per-use/${featureCode}`;
            } else if (payPerUseFeatureEndpoint) {
                payPerUseFeatureEndpoint.value = '';
            }
        });
    }
}

// 加载功能数据（编辑时使用）
async function loadPayPerUseFeatureData(featureId) {
    try {
        const response = await fetch('/admin/pay-per-use/features', {
            headers: {
                'X-Admin-Password': currentAdminPassword
            }
        });
        const result = await response.json();

        if (result.success) {
            const feature = result.data.find(f => f.id == featureId);
            if (feature) {
                if (payPerUseFeatureId) payPerUseFeatureId.value = feature.id;
                if (payPerUseFeatureCode) payPerUseFeatureCode.value = feature.feature_code;
                if (payPerUseFeatureName) payPerUseFeatureName.value = feature.display_name;
                if (payPerUseFeatureDescription) payPerUseFeatureDescription.value = feature.description || '';
                if (payPerUseFeatureEndpoint) payPerUseFeatureEndpoint.value = feature.api_endpoint || '';
                if (payPerUseFeatureActive) payPerUseFeatureActive.checked = feature.is_active == 1;

                // 确保API端点显示正确
                const featureCode = feature.feature_code;
                if (featureCode && payPerUseFeatureEndpoint) {
                    payPerUseFeatureEndpoint.value = `/api/pay-per-use/${featureCode}`;
                }

                console.log('加载功能数据成功:', feature);
            } else {
                console.error('未找到功能:', featureId);
                showToast('未找到功能数据');
            }
        } else {
            console.error('获取功能列表失败:', result.message);
            showToast('获取功能列表失败: ' + result.message);
        }
    } catch (error) {
        console.error('加载功能数据失败:', error);
        showToast('加载数据失败: ' + error.message);
    }
}

// 保存功能数据
async function savePayPerUseFeatureData() {
    if (!payPerUseFeatureCode || !payPerUseFeatureName) return;

    const featureCode = payPerUseFeatureCode.value.trim();
    const displayName = payPerUseFeatureName.value.trim();

    if (!featureCode || !displayName) {
        showToast('功能代码和显示名称不能为空');
        return;
    }

    // 自动生成API端点
    const apiEndpoint = `/api/pay-per-use/${featureCode}`;

    const featureData = {
        id: payPerUseFeatureId ? payPerUseFeatureId.value || null : null,
        feature_code: featureCode,
        display_name: displayName,
        description: payPerUseFeatureDescription ? payPerUseFeatureDescription.value.trim() : '',
        api_endpoint: apiEndpoint,
        is_active: payPerUseFeatureActive ? (payPerUseFeatureActive.checked ? 1 : 0) : 1
    };

    try {
        const response = await fetch('/admin/pay-per-use/features/save', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Admin-Password': currentAdminPassword
            },
            body: JSON.stringify(featureData)
        });

        const result = await response.json();

        if (result.success) {
            showToast(result.message || '保存成功');
            closePayPerUseFeatureModal();
            fetchPayPerUseFeatures(); // 刷新列表
        } else {
            showToast(result.message || '保存失败');
        }
    } catch (error) {
        console.error('保存功能数据失败:', error);
        showToast('保存失败');
    }

}

// 编辑功能
function editPayPerUseFeature(featureId) {
    openPayPerUseFeatureModal(featureId);
}

// 删除功能
async function deletePayPerUseFeature(featureId) {
    if (!confirm('确定要删除这个功能吗？此操作不可恢复。')) {
        return;
    }

    try {
        const response = await fetch(`/admin/pay-per-use/features/delete/${featureId}`, {
            method: 'DELETE',
            headers: {
                'X-Admin-Password': currentAdminPassword
            }
        });

        const result = await response.json();

        if (result.success) {
            showToast(result.message || '删除成功');
            fetchPayPerUseFeatures(); // 刷新列表
        } else {
            showToast(result.message || '删除失败');
        }
    } catch (error) {
        console.error('删除功能失败:', error);
        showToast('删除失败');
    }
}

// 打开生成兑换码弹窗
function openPayPerUseCodeModal() {
    if (payPerUseCodeModal) {
        payPerUseCodeModal.classList.remove('hidden');
        loadPayPerUseCodeOptions();
    }
}

// 关闭生成兑换码弹窗
function closePayPerUseCodeModal() {
    if (payPerUseCodeModal) {
        payPerUseCodeModal.classList.add('hidden');
    }
    resetPayPerUseCodeForm();
}

// 重置兑换码表单
function resetPayPerUseCodeForm() {
    // 清空功能配置容器
    if (payPerUseCodeFeaturesContainer) {
        payPerUseCodeFeaturesContainer.innerHTML = `
            <label class="block text-sm font-medium text-gray-700 mb-1">功能配置</label>
            <p class="text-xs text-gray-500 mb-2">为每个功能设置不同的使用次数</p>
            <!-- 动态生成的功能选择区域 -->
        `;
    }
    if (payPerUseCodeQuantity) payPerUseCodeQuantity.value = '1';
    if (payPerUseCodeBatchId) payPerUseCodeBatchId.value = '';
    if (payPerUseCodeNote) payPerUseCodeNote.value = '';
    if (payPerUseCodeExpiry) payPerUseCodeExpiry.value = '';
}

// 加载兑换码选项（功能）
async function loadPayPerUseCodeOptions() {
    try {
        // 加载功能选项
        const featuresResponse = await fetch('/admin/pay-per-use/features', {
            headers: { 'X-Admin-Password': currentAdminPassword }
        });
        const featuresResult = await featuresResponse.json();

        if (featuresResult.success && payPerUseCodeFeaturesContainer) {
            // 清空现有内容（保留标题）
            const titleElements = payPerUseCodeFeaturesContainer.querySelectorAll('label, p');
            const existingContent = Array.from(titleElements);

            payPerUseCodeFeaturesContainer.innerHTML = '';
            existingContent.forEach(el => payPerUseCodeFeaturesContainer.appendChild(el));

            // 动态生成功能选择区域
            const featuresContainer = document.createElement('div');
            featuresContainer.className = 'space-y-3 max-h-60 overflow-y-auto border rounded p-3 bg-gray-50';

            featuresResult.data.forEach(feature => {
                if (feature.is_active == 1) {
                    const featureDiv = document.createElement('div');
                    featureDiv.className = 'flex items-center space-x-3 p-2 bg-white rounded border';
                    featureDiv.innerHTML = `
                        <input type="checkbox" id="feature_${feature.id}" value="${feature.id}" class="pay-per-use-feature-checkbox h-4 w-4 text-blue-600">
                        <label for="feature_${feature.id}" class="flex-1 text-sm font-medium text-gray-700">${feature.display_name}</label>
                        <input type="number" id="count_${feature.id}" min="1" value="1" class="w-20 px-2 py-1 border rounded text-sm" placeholder="次数">
                        <span class="text-xs text-gray-500">次</span>
                    `;
                    featuresContainer.appendChild(featureDiv);
                }
            });

            payPerUseCodeFeaturesContainer.appendChild(featuresContainer);
        }
    } catch (error) {
        console.error('加载选项失败:', error);
        showToast('加载选项失败');
    }
}

// 生成兑换码
async function generatePayPerUseCodes() {
    // 收集选中的功能及其使用次数
    const selectedFeatures = {};
    const checkboxes = document.querySelectorAll('.pay-per-use-feature-checkbox:checked');

    checkboxes.forEach(checkbox => {
        const featureId = checkbox.value;
        const countInput = document.getElementById(`count_${featureId}`);
        const count = parseInt(countInput.value) || 1;
        selectedFeatures[featureId] = count;
    });

    if (Object.keys(selectedFeatures).length === 0) {
        showToast('请至少选择一个功能');
        return;
    }

    const codeData = {
        feature_config: JSON.stringify(selectedFeatures),
        count: parseInt(payPerUseCodeQuantity ? payPerUseCodeQuantity.value : '1') || 1,
        batch_id: payPerUseCodeBatchId ? payPerUseCodeBatchId.value.trim() : '',
        note: payPerUseCodeNote ? payPerUseCodeNote.value.trim() : '',
        expires_at: payPerUseCodeExpiry ? payPerUseCodeExpiry.value : null
    };

    if (codeData.count > 1000) {
        showToast('单次生成数量不能超过1000个');
        return;
    }

    try {
        console.log('发送数据:', codeData);

        const response = await fetch('/admin/pay-per-use/codes/generate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Admin-Password': currentAdminPassword
            },
            body: JSON.stringify(codeData)
        });

        console.log('响应状态:', response.status);

        const result = await response.json();
        console.log('响应数据:', result);

        if (result.success) {
            showToast(result.message || '生成成功');
            closePayPerUseCodeModal();
            showGeneratedCodes(result.data);
            fetchPayPerUseCodes(); // 刷新列表
        } else {
            showToast(result.message || '生成失败');
            console.error('API错误:', result.message);
        }
    } catch (error) {
        console.error('生成兑换码失败:', error);
        showToast('生成失败: ' + error.message);
    }
}

// 显示生成的兑换码
function showGeneratedCodes(codes) {
    if (payPerUseCodeResultList) {
        payPerUseCodeResultList.innerHTML = '';
        codes.forEach((code, index) => {
            const codeItem = document.createElement('div');
            codeItem.className = 'mb-2 p-2 bg-white rounded border';
            codeItem.innerHTML = `
                <div class="flex justify-between items-center">
                    <span class="font-mono text-blue-600">${code}</span>
                    <span class="text-sm text-gray-500">#${index + 1}</span>
                </div>
            `;
            payPerUseCodeResultList.appendChild(codeItem);
        });
    }

    if (payPerUseCodeResultModal) {
        payPerUseCodeResultModal.classList.remove('hidden');
    }
}

// 复制生成的兑换码
function copyGeneratedCodes() {
    if (payPerUseCodeResultList) {
        const codes = Array.from(payPerUseCodeResultList.querySelectorAll('.font-mono'))
            .map(el => el.textContent)
            .join('\n');

        navigator.clipboard.writeText(codes).then(() => {
            showToast('兑换码已复制到剪贴板');
        }).catch(() => {
            showToast('复制失败');
        });
    }
}

// 关闭兑换码结果弹窗
function closePayPerUseCodeResultModal() {
    if (payPerUseCodeResultModal) {
        payPerUseCodeResultModal.classList.add('hidden');
    }
}

// 删除兑换码
async function deletePayPerUseCode(codeId) {
    if (!confirm('确定要删除这个兑换码吗？此操作不可恢复。')) {
        return;
    }

    try {
        const response = await fetch(`/admin/pay-per-use/codes/delete/${codeId}`, {
            method: 'DELETE',
            headers: {
                'X-Admin-Password': currentAdminPassword
            }
        });

        const result = await response.json();

        if (result.success) {
            showToast(result.message || '删除成功');
            fetchPayPerUseCodes(); // 刷新列表
        } else {
            showToast(result.message || '删除失败');
        }
    } catch (error) {
        console.error('删除兑换码失败:', error);
        showToast('删除失败');
    }
}

// 调整余额
function adjustPayPerUseBilling(accountId, groupNumber, featureId, tierId) {
    const adjustment = prompt('请输入调整数量（正数增加，负数减少）:');
    if (adjustment === null || adjustment === '') return;

    const amount = parseInt(adjustment);
    if (isNaN(amount) || amount === 0) {
        showToast('请输入有效的数字');
        return;
    }

    const reason = prompt('请输入调整原因（可选）:') || '';

    adjustPayPerUseBillingAmount(accountId, amount, reason);
}

// 调整余额金额
async function adjustPayPerUseBillingAmount(accountId, adjustment, reason) {
    try {
        const response = await fetch('/admin/pay-per-use/billing/adjust', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Admin-Password': currentAdminPassword
            },
            body: JSON.stringify({
                group_number: '', // 由后端根据accountId查找
                feature_id: '', // 由后端根据accountId查找
                tier_id: '', // 由后端根据accountId查找
                adjustment: adjustment,
                reason: reason,
                account_id: accountId
            })
        });

        const result = await response.json();

        if (result.success) {
            showToast(result.message || '调整成功');
            fetchPayPerUseBilling(); // 刷新列表
        } else {
            showToast(result.message || '调整失败');
        }
    } catch (error) {
        console.error('调整余额失败:', error);
        showToast('调整失败');
    }
}

// ==================== 前端搜索功能 ====================

// 全局变量存储原始数据
let originalPayPerUseCodesData = [];
let originalPayPerUseBillingData = [];
let originalPayPerUseLogsData = [];

// 搜索兑换码（前端搜索）
function searchPayPerUseCodes() {
    console.log('前端搜索兑换码被调用');
    const searchTerm = payPerUseCodesSearchInput ? payPerUseCodesSearchInput.value.trim().toLowerCase() : '';

    if (!searchTerm) {
        // 如果搜索词为空，显示所有数据并恢复分页
        renderPayPerUseCodesTable(originalPayPerUseCodesData);
        restorePayPerUseCodesPagination();
        return;
    }

    // 在前端过滤数据
    const filteredData = originalPayPerUseCodesData.filter(code => {
        return code.code.toLowerCase().includes(searchTerm) ||
               (code.feature_info && code.feature_info.toLowerCase().includes(searchTerm)) ||
               (code.note && code.note.toLowerCase().includes(searchTerm)) ||
               (code.batch_id && code.batch_id.toLowerCase().includes(searchTerm));
    });

    console.log(`搜索结果: ${filteredData.length} 条记录`);
    renderPayPerUseCodesTable(filteredData);

    // 隐藏分页控件，因为搜索结果不需要分页
    if (payPerUseCodesPrevPage) payPerUseCodesPrevPage.style.display = 'none';
    if (payPerUseCodesNextPage) payPerUseCodesNextPage.style.display = 'none';
    if (payPerUseCodesPaginationInfo) payPerUseCodesPaginationInfo.textContent = `搜索结果: ${filteredData.length} 条记录`;
}

// 恢复兑换码分页控件
function restorePayPerUseCodesPagination() {
    if (payPerUseCodesPrevPage) payPerUseCodesPrevPage.style.display = '';
    if (payPerUseCodesNextPage) payPerUseCodesNextPage.style.display = '';
    // 分页信息会在fetchPayPerUseCodes时更新
}

// 搜索计费账户（前端搜索）
function searchPayPerUseBilling() {
    console.log('前端搜索计费账户被调用');
    const searchTerm = payPerUseBillingSearchInput ? payPerUseBillingSearchInput.value.trim().toLowerCase() : '';

    if (!searchTerm) {
        // 如果搜索词为空，显示所有数据并恢复分页
        renderPayPerUseBillingTable(originalPayPerUseBillingData);
        restorePayPerUseBillingPagination();
        return;
    }

    // 在前端过滤数据
    const filteredData = originalPayPerUseBillingData.filter(account => {
        return account.group_number.toString().includes(searchTerm);
    });

    console.log(`搜索结果: ${filteredData.length} 条记录`);
    renderPayPerUseBillingTable(filteredData);

    // 隐藏分页控件，因为搜索结果不需要分页
    if (payPerUseBillingPrevPage) payPerUseBillingPrevPage.style.display = 'none';
    if (payPerUseBillingNextPage) payPerUseBillingNextPage.style.display = 'none';
    if (payPerUseBillingPaginationInfo) payPerUseBillingPaginationInfo.textContent = `搜索结果: ${filteredData.length} 条记录`;
}

// 恢复计费账户分页控件
function restorePayPerUseBillingPagination() {
    if (payPerUseBillingPrevPage) payPerUseBillingPrevPage.style.display = '';
    if (payPerUseBillingNextPage) payPerUseBillingNextPage.style.display = '';
    // 分页信息会在fetchPayPerUseBilling时更新
}

// 搜索使用日志（前端搜索）
function searchPayPerUseLogs() {
    console.log('前端搜索使用日志被调用');
    const groupSearch = payPerUseLogsGroupSearchInput ? payPerUseLogsGroupSearchInput.value.trim().toLowerCase() : '';
    const dateFrom = payPerUseLogsDateFromInput ? payPerUseLogsDateFromInput.value : '';
    const dateTo = payPerUseLogsDateToInput ? payPerUseLogsDateToInput.value : '';

    let filteredData = [...originalPayPerUseLogsData];

    // 按群号搜索
    if (groupSearch) {
        filteredData = filteredData.filter(log => {
            return log.group_number.toString().includes(groupSearch);
        });
    }

    // 按日期范围过滤
    if (dateFrom || dateTo) {
        filteredData = filteredData.filter(log => {
            const logDate = new Date(log.request_time);
            const fromDate = dateFrom ? new Date(dateFrom) : null;
            const toDate = dateTo ? new Date(dateTo) : null;

            if (fromDate && logDate < fromDate) return false;
            if (toDate && logDate > toDate) return false;
            return true;
        });
    }

    // 如果没有搜索条件，显示所有数据并恢复分页
    if (!groupSearch && !dateFrom && !dateTo) {
        renderPayPerUseLogsTable(originalPayPerUseLogsData);
        restorePayPerUseLogsPagination();
        return;
    }

    console.log(`搜索结果: ${filteredData.length} 条记录`);
    renderPayPerUseLogsTable(filteredData);

    // 隐藏分页控件，因为搜索结果不需要分页
    if (payPerUseLogsPrevPage) payPerUseLogsPrevPage.style.display = 'none';
    if (payPerUseLogsNextPage) payPerUseLogsNextPage.style.display = 'none';
    if (payPerUseLogsPaginationInfo) payPerUseLogsPaginationInfo.textContent = `搜索结果: ${filteredData.length} 条记录`;
}

// 恢复使用日志分页控件
function restorePayPerUseLogsPagination() {
    if (payPerUseLogsPrevPage) payPerUseLogsPrevPage.style.display = '';
    if (payPerUseLogsNextPage) payPerUseLogsNextPage.style.display = '';
    // 分页信息会在fetchPayPerUseLogs时更新
}

// ==================== 暴露全局函数 ====================

window.editPayPerUseFeature = editPayPerUseFeature;
window.deletePayPerUseFeature = deletePayPerUseFeature;
window.deletePayPerUseCode = deletePayPerUseCode;
window.adjustPayPerUseBilling = adjustPayPerUseBilling;
window.adjustPayPerUseBillingAmount = adjustPayPerUseBillingAmount;

// 初始化模块（在DOM加载完成后调用）
if (typeof window !== 'undefined') {
    document.addEventListener('DOMContentLoaded', function() {
        // 延迟初始化，确保其他脚本加载完成
        setTimeout(() => {
            if (typeof initPayPerUseModule === 'function') {
                initPayPerUseModule();
            }
        }, 200);
    });
}
