// robots.js - 机器人管理模块

// 全局变量（从主文件获取 currentAdminPassword）
let currentLeaveGroupData = null;

// 机器人管理元素
const robotsTableBody = document.getElementById('robots-table-body');
const robotsLoadingMessage = document.getElementById('robotsLoadingMessage');
const addRobotButton = document.getElementById('addRobotButton');

// 机器人模态框元素
const robotModal = document.getElementById('robotModal');
const robotModalTitle = document.getElementById('robotModalTitle');
const robotForm = document.getElementById('robotForm');
const robotModalError = document.getElementById('robotModalError');
const closeRobotModal = document.getElementById('closeRobotModal');
const cancelRobotButton = document.getElementById('cancelRobotButton');
const saveRobotButton = document.getElementById('saveRobotButton');

// 退群相关元素
const leaveGroupModal = document.getElementById('leaveGroupModal');
const leaveGroupRobot = document.getElementById('leaveGroupRobot');
const confirmLeaveGroup = document.getElementById('confirmLeaveGroup');
const cancelLeaveGroup = document.getElementById('cancelLeaveGroup');

// 群聊相关元素（机器人模块需要用到）- botGroupStats 在主文件中已声明

// 绑定相关（用于刷新绑定列表）- bindingsTab 在主文件中已声明

// 获取机器人列表
async function fetchRobots() {
    robotsLoadingMessage.classList.remove('hidden');
    robotsTableBody.innerHTML = '';

    try {
        const response = await fetch('/admin/robots', {
            method: 'GET',
            headers: {
                'X-Admin-Password': currentAdminPassword
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error ${response.status}`);
        }

        const result = await response.json();
        if (result.success && result.data) {
            populateRobotsTable(result.data);
        }
    } catch (error) {
        console.error('获取机器人列表时出错:', error);
        showToast(`错误: ${error.message}`, 5000);
    } finally {
        robotsLoadingMessage.classList.add('hidden');
    }
}

// 填充机器人表格
function populateRobotsTable(robots) {
    robotsTableBody.innerHTML = '';

    robots.forEach(robot => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td class="px-4 py-3 text-sm" data-label="机器人账号">
                ${robot.botAccount}
            </td>
            <td class="px-4 py-3 text-sm font-medium" data-label="机器人名称">
                ${robot.botName}
            </td>
            <td class="px-4 py-3 text-sm" data-label="API地址">
                <span class="text-xs">${robot.apiUrl}</span>
            </td>
            <td class="px-4 py-3 text-sm" data-label="Token">
                ${robot.apiToken ? '已设置' : '未设置'}
            </td>
            <td class="px-4 py-3" data-label="状态">
                ${robot.isActive ?
                    '<span class="px-2 py-1 text-xs font-medium text-green-800 bg-green-100 rounded-full">活跃</span>' :
                    '<span class="px-2 py-1 text-xs font-medium text-red-800 bg-red-100 rounded-full">停用</span>'}
            </td>
            <td class="px-4 py-3 text-sm text-center" data-label="操作">
                <div class="flex justify-center gap-2">
                    <button onclick="viewRobotGroups(${robot.id})" class="text-green-600 hover:text-green-900">
                        查看群聊
                    </button>
                    <button onclick="openEditRobotModal(${robot.id}, '${robot.botAccount}', '${robot.botName}', '${robot.apiUrl}', '${robot.apiToken || ''}')"
                            class="text-blue-600 hover:text-blue-900">
                        编辑
                    </button>
                    <button onclick="deleteRobot(${robot.id})" class="text-red-600 hover:text-red-900">
                        删除
                    </button>
                </div>
            </td>
        `;
        robotsTableBody.appendChild(row);
    });
}

// 查看机器人群聊
window.viewRobotGroups = async function(robotId) {
    try {
        const response = await fetch(`/admin/robots/${robotId}/groups`, {
            method: 'GET',
            headers: {
                'X-Admin-Password': currentAdminPassword
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error ${response.status}`);
        }

        const result = await response.json();
        if (result.success) {
            // TODO: 显示群聊列表模态框
            showToast(`该机器人加入了 ${result.data.groups.length} 个群聊`);
        }
    } catch (error) {
        console.error('获取机器人群聊时出错:', error);
        showToast(`错误: ${error.message}`, 5000);
    }
};

// 打开添加机器人模态框
function openAddRobotModal() {
    robotModalTitle.textContent = '添加机器人';
    robotForm.reset();
    document.getElementById('robotId').value = '';
    document.getElementById('robotAccount').disabled = false;
    robotModal.classList.remove('hidden');
}

// 打开编辑机器人模态框
window.openEditRobotModal = function(id, account, name, apiUrl, apiToken) {
    robotModalTitle.textContent = '编辑机器人';
    document.getElementById('robotId').value = id;
    document.getElementById('robotAccount').value = account;
    document.getElementById('robotAccount').disabled = true;
    document.getElementById('robotName').value = name;
    document.getElementById('robotApiUrl').value = apiUrl;
    document.getElementById('robotApiToken').value = apiToken;
    robotModal.classList.remove('hidden');
};

// 保存机器人
async function saveRobot(e) {
    e.preventDefault();

    const robotId = document.getElementById('robotId').value;
    const botAccount = document.getElementById('robotAccount').value.trim();
    const botName = document.getElementById('robotName').value.trim();
    const apiUrl = document.getElementById('robotApiUrl').value.trim();
    const apiToken = document.getElementById('robotApiToken').value.trim();

    showLoading(saveRobotButton);
    robotModalError.textContent = '';

    try {
        const response = await fetch('/admin/robots/save', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Admin-Password': currentAdminPassword
            },
            body: JSON.stringify({
                id: robotId || undefined,
                botAccount,
                botName,
                apiUrl,
                apiToken
            })
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.message || `HTTP error ${response.status}`);
        }

        const result = await response.json();
        if (result.success) {
            showToast(result.message);
            robotModal.classList.add('hidden');
            fetchRobots();
        }
    } catch (error) {
        console.error('保存机器人时出错:', error);
        robotModalError.textContent = error.message;
    } finally {
        hideLoading(saveRobotButton);
    }
}

// 删除机器人
window.deleteRobot = async function(robotId) {
    if (!confirm('确定要删除这个机器人吗？相关的群聊记录也会被删除。')) {
        return;
    }

    try {
        const response = await fetch('/admin/robots/delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Admin-Password': currentAdminPassword
            },
            body: JSON.stringify({ id: robotId })
        });

        if (!response.ok) {
            throw new Error(`HTTP error ${response.status}`);
        }

        const result = await response.json();
        if (result.success) {
            showToast('删除成功');
            fetchRobots();
        }
    } catch (error) {
        console.error('删除机器人时出错:', error);
        showToast(`错误: ${error.message}`, 5000);
    }
};

// 刷新所有机器人群聊
async function refreshAllRobotGroups() {
    const button = refreshAllGroupsButton;
    const originalText = button.innerHTML;

    button.innerHTML = '<span class="admin-loader inline-block mr-2"></span>刷新中...';
    button.disabled = true;

    try {
        const response = await fetch('/admin/robots/refresh-all-groups', {
            method: 'POST',
            headers: {
                'X-Admin-Password': currentAdminPassword
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error ${response.status}`);
        }

        const result = await response.json();
        if (result.success) {
            showToast(`成功获取 ${result.data.totalGroups} 个群聊`, 3000);

            // 显示失败的机器人信息
            if (result.data.failedRobots && result.data.failedRobots.length > 0) {
                const failedMsg = result.data.failedRobots.map(r =>
                    `${r.botAccount}: ${r.error}`
                ).join('\n');
                console.error('部分机器人刷新失败:', failedMsg);
            }

            // 重新加载群聊列表
            await fetchAllGroups();
            // 同时刷新绑定列表，因为群聊变化可能影响绑定状态
            if (bindingsTab.classList.contains('active')) {
                fetchBindings();
            }
        }
    } catch (error) {
        console.error('刷新群聊时出错:', error);
        showToast(`错误: ${error.message}`, 5000);
    } finally {
        button.innerHTML = originalText;
        button.disabled = false;
    }
}

// 显示退群模态框
window.showLeaveGroupModal = function(groupId, groupName, robotIds) {
    currentLeaveGroupData = { groupId, groupName, robotIds };

    // 填充机器人选择列表
    leaveGroupRobot.innerHTML = '';

    // 获取机器人信息
    fetch('/admin/robots', {
        method: 'GET',
        headers: {
            'X-Admin-Password': currentAdminPassword
        }
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            const robots = result.data.filter(r => robotIds.includes(String(r.id)));
            robots.forEach(robot => {
                const option = document.createElement('option');
                option.value = robot.id;
                option.textContent = `${robot.botName} (${robot.botAccount})`;
                leaveGroupRobot.appendChild(option);
            });
        }
    });

    document.getElementById('leaveGroupInfo').textContent =
        `确定要让机器人退出群 "${groupName}" (${groupId}) 吗？`;

    leaveGroupModal.classList.remove('hidden');
};

// 执行退群操作
async function executeLeaveGroup() {
    if (!currentLeaveGroupData) return;

    const robotId = leaveGroupRobot.value;
    if (!robotId) {
        showToast('请选择要退群的机器人', 3000);
        return;
    }

    confirmLeaveGroup.disabled = true;
    confirmLeaveGroup.innerHTML = '<span class="admin-loader inline-block mr-2"></span>退群中...';

    try {
        const response = await fetch('/admin/robots/leave-group', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Admin-Password': currentAdminPassword
            },
            body: JSON.stringify({
                groupId: currentLeaveGroupData.groupId,
                robotId: robotId
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error ${response.status}`);
        }

        const result = await response.json();
        if (result.success) {
            showToast('退群成功');
            leaveGroupModal.classList.add('hidden');
            // 刷新相关列表
            await fetchAllGroups();
            if (bindingsTab.classList.contains('active')) {
                fetchBindings();
            }
        }
    } catch (error) {
        console.error('退群时出错:', error);
        showToast(`错误: ${error.message}`, 5000);
    } finally {
        confirmLeaveGroup.disabled = false;
        confirmLeaveGroup.innerHTML = '确认退群';
    }
}

// 初始化机器人模块事件监听器
function initRobotsModule() {
    // 机器人管理事件监听器
    if (addRobotButton) {
        addRobotButton.addEventListener('click', openAddRobotModal);
    }
    if (closeRobotModal) {
        closeRobotModal.addEventListener('click', () => robotModal.classList.add('hidden'));
    }
    if (cancelRobotButton) {
        cancelRobotButton.addEventListener('click', () => robotModal.classList.add('hidden'));
    }
    if (robotForm) {
        robotForm.addEventListener('submit', saveRobot);
    }

    // 群聊管理事件监听器
    if (refreshAllGroupsButton) {
        refreshAllGroupsButton.addEventListener('click', refreshAllRobotGroups);
    }

    // 退群相关事件监听器
    if (confirmLeaveGroup) {
        confirmLeaveGroup.addEventListener('click', executeLeaveGroup);
    }
    if (cancelLeaveGroup) {
        cancelLeaveGroup.addEventListener('click', () => leaveGroupModal.classList.add('hidden'));
    }

    // 模态框外部点击关闭
    window.addEventListener('click', (e) => {
        if (e.target === robotModal) robotModal.classList.add('hidden');
        if (e.target === leaveGroupModal) leaveGroupModal.classList.add('hidden');
    });
}

// 设置全局变量（从主文件调用）- currentAdminPassword 现在是全局变量，不需要设置
function setRobotsGlobalVars(adminPassword) {
    // currentAdminPassword 现在从全局作用域获取，无需设置
}

// 导出函数供外部使用
window.RobotsModule = {
    init: initRobotsModule,
    setGlobalVars: setRobotsGlobalVars,
    fetchRobots: fetchRobots
};
