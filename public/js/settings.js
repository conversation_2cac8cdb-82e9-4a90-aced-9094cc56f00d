// 系统设置模块
console.log('系统设置模块开始加载...');
const SettingsModule = (function() {
    // 私有变量
    let currentAdminPassword = null;
    let settingsCache = {};

    // DOM 元素
    let settingsTab = null;
    let settingsContent = null;
    let settingAdminEmail = null;
    let settingSmtpHost = null;
    let settingSmtpPort = null;
    let settingSmtpTLS = null;
    let settingSmtpUser = null;
    let settingSmtpPass = null;
    let settingEmailTemplateEnd = null;
    let settingEmailTemplateCaptcha = null;
    let settingEmailTemplateWorkOrder = null;
    let settingEmailTemplateMessages = null;
    let refreshEmailTemplates = null;
    let saveSettingsButton = null;
    let testEmailButton = null;
    let settingsSaveMsg = null;
    let settingSupportContact = null;
    let settingSiteLink = null;

    // 设置全局变量
    function setGlobalVars(password) {
        currentAdminPassword = password;
    }

    // 获取当前管理员密码
    function getCurrentAdminPassword() {
        return currentAdminPassword || window.currentAdminPassword || '';
    }

    // 显示提示消息（依赖全局showToast函数）
    function showToast(message, type = 'info') {
        if (window.showToast) {
            window.showToast(message, type);
        }
    }

    // 加载系统设置
    async function loadSettings() {
        try {
            const resp = await fetch('/admin/settings', {
                headers: { 'X-Admin-Password': getCurrentAdminPassword() }
            });
            const result = await resp.json();
            if (!resp.ok || !result.success) throw new Error(result.message || '读取失败');
            settingsCache = result.data || {};
            if (settingAdminEmail) settingAdminEmail.value = settingsCache.admin_email || '';
            if (settingSmtpHost) settingSmtpHost.value = settingsCache.smtp_host || '';
            if (settingSmtpPort) settingSmtpPort.value = settingsCache.smtp_port || '587';
            if (settingSmtpTLS) settingSmtpTLS.value = (String(settingsCache.smtp_tls || '1') === '1') ? '1' : '0';
            if (settingSmtpUser) settingSmtpUser.value = settingsCache.smtp_user || '';
            const settingSmtpFrom = document.getElementById('settingSmtpFrom');
            const settingSmtpFromName = document.getElementById('settingSmtpFromName');
            if (settingSmtpFrom) settingSmtpFrom.value = settingsCache.smtp_from || '';
            if (settingSmtpFromName) settingSmtpFromName.value = settingsCache.smtp_from_name || '';
            const settingSiteTitle = document.getElementById('settingSiteTitle');
            if (settingSiteTitle) settingSiteTitle.value = settingsCache.site_title || '';
            if (settingSupportContact) settingSupportContact.value = settingsCache.support_contact || '';
            if (settingSiteLink) settingSiteLink.value = settingsCache.site_link || '';
            const settingBackgroundImageApi = document.getElementById('settingBackgroundImageApi');
            if (settingBackgroundImageApi) settingBackgroundImageApi.value = settingsCache.background_image_api || '';
            if (settingSmtpPass) settingSmtpPass.value = '';
            const settingAllowedEmailSuffixes = document.getElementById('settingAllowedEmailSuffixes');

            // 机器人群聊自动刷新设置
            const toggleRobotGroupsAutoRefreshEnabled = document.getElementById('toggleRobotGroupsAutoRefreshEnabled');
            const inputRobotGroupsRefreshInterval = document.getElementById('inputRobotGroupsRefreshInterval');
            if (settingAllowedEmailSuffixes) settingAllowedEmailSuffixes.value = settingsCache.allowed_email_suffixes || '';
            const toggleTicketsEnabled = document.getElementById('toggleTicketsEnabled');
            const toggleFeatureBlacklistEnabled = document.getElementById('toggleFeatureBlacklistEnabled');
            const toggleFeatureWhitelistEnabled = document.getElementById('toggleFeatureWhitelistEnabled');
            const togglePayPerUseEnabled = document.getElementById('togglePayPerUseEnabled');
            const toggleRegisterEmailVerificationEnabled = document.getElementById('toggleRegisterEmailVerificationEnabled');

            // 填充机器人群聊自动刷新设置值
            if (toggleRobotGroupsAutoRefreshEnabled) toggleRobotGroupsAutoRefreshEnabled.checked = (String(settingsCache.robot_groups_auto_refresh_enabled || '1') === '1');
            if (inputRobotGroupsRefreshInterval) inputRobotGroupsRefreshInterval.value = settingsCache.robot_groups_refresh_interval_minutes || '60';
            const toggleTicketEmailNotifyEnabled = document.getElementById('toggleTicketEmailNotifyEnabled');
            const toggleAdminTicketEmailNotifyEnabled = document.getElementById('toggleAdminTicketEmailNotifyEnabled');
            const toggleAdminReplyEveryEmailEnabled = document.getElementById('toggleAdminReplyEveryEmailEnabled');
            const togglePreExpiryRenewalEmailEnabled = document.getElementById('togglePreExpiryRenewalEmailEnabled');
            const inputPreExpiryRenewalDays = document.getElementById('inputPreExpiryRenewalDays');
            if (toggleTicketsEnabled) toggleTicketsEnabled.checked = ['1','true','True','yes','on'].includes(String(settingsCache.tickets_enabled || '1'));
            if (toggleFeatureBlacklistEnabled) toggleFeatureBlacklistEnabled.checked = ['1','true','True','yes','on'].includes(String(settingsCache.feature_blacklist_enabled || '1'));
            if (toggleFeatureWhitelistEnabled) toggleFeatureWhitelistEnabled.checked = ['1','true','True','yes','on'].includes(String(settingsCache.feature_whitelist_enabled || '1'));
            if (togglePayPerUseEnabled) togglePayPerUseEnabled.checked = ['1','true','True','yes','on'].includes(String(settingsCache.pay_per_use_enabled || '1'));
            if (toggleRegisterEmailVerificationEnabled) toggleRegisterEmailVerificationEnabled.checked = ['1','true','True','yes','on'].includes(String(settingsCache.register_email_verification_enabled || '0'));
            if (toggleTicketEmailNotifyEnabled) toggleTicketEmailNotifyEnabled.checked = ['1','true','True','yes','on'].includes(String(settingsCache.ticket_email_notify_enabled || '0'));
            if (toggleAdminTicketEmailNotifyEnabled) toggleAdminTicketEmailNotifyEnabled.checked = ['1','true','True','yes','on'].includes(String(settingsCache.admin_ticket_email_notify_enabled || '0'));
            if (toggleAdminReplyEveryEmailEnabled) toggleAdminReplyEveryEmailEnabled.checked = ['1','true','True','yes','on'].includes(String(settingsCache.admin_reply_every_email_enabled || '0'));
            if (togglePreExpiryRenewalEmailEnabled) togglePreExpiryRenewalEmailEnabled.checked = ['1','true','True','yes','on'].includes(String(settingsCache.pre_expiry_renewal_email_enabled || '0'));
            if (togglePreExpiryRenewalRepeatEnabled) togglePreExpiryRenewalRepeatEnabled.checked = ['1','true','True','yes','on'].includes(String(settingsCache.pre_expiry_renewal_repeat_enabled || '0'));
            if (inputPreExpiryRenewalDays) inputPreExpiryRenewalDays.value = String(settingsCache.pre_expiry_renewal_days || '3');
            // 回填模板选择（需要先加载模板清单）
            try {
                await loadEmailTemplateOptions();
                if (settingEmailTemplateEnd) settingEmailTemplateEnd.value = settingsCache.email_template_end || '';
                if (settingEmailTemplateCaptcha) settingEmailTemplateCaptcha.value = settingsCache.email_template_captcha || '';
                if (settingEmailTemplateWorkOrder) settingEmailTemplateWorkOrder.value = settingsCache.email_template_work_order || '';
                if (settingEmailTemplateMessages) settingEmailTemplateMessages.value = settingsCache.email_template_messages || '';
            } catch (e) { console.warn('加载模板列表失败', e); }
            // 应用标题
            setAdminDocumentTitle(settingsCache.site_title || '');
        } catch (e) {
            console.error('读取系统设置失败:', e);
            showToast('读取系统设置失败', 'error');
        }
    }

    // 保存系统设置
    async function saveSettings() {
        try {
            const body = {
                admin_email: settingAdminEmail?.value || '',
                smtp_host: settingSmtpHost?.value || '',
                smtp_port: (settingSmtpPort?.value || '587'),
                smtp_user: settingSmtpUser?.value || '',
                smtp_tls: (document.getElementById('settingSmtpTLS')?.value === '1' ? '1' : '0'),
                smtp_from: document.getElementById('settingSmtpFrom')?.value || '',
                smtp_from_name: document.getElementById('settingSmtpFromName')?.value || '',
                site_title: document.getElementById('settingSiteTitle')?.value || '',
                support_contact: settingSupportContact?.value || '',
                site_link: settingSiteLink?.value || '',
                background_image_api: document.getElementById('settingBackgroundImageApi')?.value || '',
                allowed_email_suffixes: (document.getElementById('settingAllowedEmailSuffixes')?.value || ''),
                tickets_enabled: (document.getElementById('toggleTicketsEnabled')?.checked ? '1' : '0'),
                feature_blacklist_enabled: (document.getElementById('toggleFeatureBlacklistEnabled')?.checked ? '1' : '0'),
                feature_whitelist_enabled: (document.getElementById('toggleFeatureWhitelistEnabled')?.checked ? '1' : '0'),
                pay_per_use_enabled: (document.getElementById('togglePayPerUseEnabled')?.checked ? '1' : '0'),
                robot_groups_auto_refresh_enabled: (document.getElementById('toggleRobotGroupsAutoRefreshEnabled')?.checked ? '1' : '0'),
                robot_groups_refresh_interval_minutes: document.getElementById('inputRobotGroupsRefreshInterval')?.value || '60',
                register_email_verification_enabled: (document.getElementById('toggleRegisterEmailVerificationEnabled')?.checked ? '1' : '0'),
                ticket_email_notify_enabled: (document.getElementById('toggleTicketEmailNotifyEnabled')?.checked ? '1' : '0'),
                admin_ticket_email_notify_enabled: (document.getElementById('toggleAdminTicketEmailNotifyEnabled')?.checked ? '1' : '0'),
                admin_reply_every_email_enabled: (document.getElementById('toggleAdminReplyEveryEmailEnabled')?.checked ? '1' : '0'),
                pre_expiry_renewal_email_enabled: (document.getElementById('togglePreExpiryRenewalEmailEnabled')?.checked ? '1' : '0'),
                pre_expiry_renewal_repeat_enabled: (document.getElementById('togglePreExpiryRenewalRepeatEnabled')?.checked ? '1' : '0'),
                pre_expiry_renewal_days: (document.getElementById('inputPreExpiryRenewalDays')?.value || '3'),
                // 新增：模板选择
                email_template_end: settingEmailTemplateEnd?.value || '',
                email_template_captcha: settingEmailTemplateCaptcha?.value || '',
                email_template_work_order: settingEmailTemplateWorkOrder?.value || '',
                email_template_messages: settingEmailTemplateMessages?.value || ''
            };
            if (settingSmtpPass && settingSmtpPass.value) {
                body.smtp_pass = settingSmtpPass.value;
            }
            const resp = await fetch('/admin/settings', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json', 'X-Admin-Password': getCurrentAdminPassword() },
                body: JSON.stringify(body)
            });
            const result = await resp.json().catch(() => ({ success: false }));
            if (!resp.ok || !result.success) throw new Error((result && result.message) || '保存失败');
            showToast('设置已保存', 'success');
            if (settingsSaveMsg) settingsSaveMsg.textContent = '保存成功';
            // 使用服务端返回的数据覆盖本地缓存并回填，确保显示保存后的实际值
            if (result && result.data) {
                settingsCache = { ...settingsCache, ...result.data };
            }
            const settingSiteTitle = document.getElementById('settingSiteTitle');
            if (settingSiteTitle) settingSiteTitle.value = settingsCache.site_title || '';
            const settingBackgroundImageApi2 = document.getElementById('settingBackgroundImageApi');
            if (settingBackgroundImageApi2) settingBackgroundImageApi2.value = settingsCache.background_image_api || '';
            const settingAllowedEmailSuffixes2 = document.getElementById('settingAllowedEmailSuffixes');
            if (settingAllowedEmailSuffixes2) settingAllowedEmailSuffixes2.value = settingsCache.allowed_email_suffixes || '';
            // 回填模板
            if (settingEmailTemplateEnd) settingEmailTemplateEnd.value = settingsCache.email_template_end || '';
            if (settingEmailTemplateCaptcha) settingEmailTemplateCaptcha.value = settingsCache.email_template_captcha || '';
            if (settingEmailTemplateWorkOrder) settingEmailTemplateWorkOrder.value = settingsCache.email_template_work_order || '';
            if (settingEmailTemplateMessages) settingEmailTemplateMessages.value = settingsCache.email_template_messages || '';
            // 应用标题
            setAdminDocumentTitle(settingsCache.site_title || '');
        } catch (e) {
            console.error('保存系统设置失败:', e);
            if (settingsSaveMsg) settingsSaveMsg.textContent = '保存失败：' + e.message;
            showToast('保存失败：' + e.message, 'error');
        }
    }

    // 加载模板选项
    async function loadEmailTemplateOptions() {
        try {
            const resp = await fetch('/admin/settings/email-templates', {
                headers: { 'X-Admin-Password': getCurrentAdminPassword() }
            });
            const result = await resp.json().catch(() => ({ success: false }));
            if (!resp.ok || !result.success) throw new Error(result.message || '读取模板失败');
            const data = result.data || {};
            const fill = (selectEl, arr) => {
                if (!selectEl) return;
                const current = selectEl.value || '';
                selectEl.innerHTML = '';
                const optNone = document.createElement('option');
                optNone.value = '';
                optNone.textContent = '默认(留空)';
                selectEl.appendChild(optNone);
                (arr || []).forEach(name => {
                    const opt = document.createElement('option');
                    opt.value = name;
                    opt.textContent = name;
                    selectEl.appendChild(opt);
                });
                // 维持选择
                selectEl.value = current;
            };
            fill(settingEmailTemplateEnd, data.end);
            fill(settingEmailTemplateCaptcha, data.captcha);
            fill(settingEmailTemplateWorkOrder, data.work_order);
            fill(settingEmailTemplateMessages, data.messages);
        } catch (e) {
            console.error('加载模板列表失败:', e);
            showToast('加载模板列表失败: ' + e.message, 'error');
        }
    }

    // 发送测试邮件
    async function sendTestEmail() {
        try {
            const to = settingAdminEmail?.value || '';
            const resp = await fetch('/admin/settings/test-email', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json', 'X-Admin-Password': getCurrentAdminPassword() },
                body: JSON.stringify({ to })
            });
            const result = await resp.json().catch(() => ({ success: false }));
            if (!resp.ok || !result.success) throw new Error((result && result.message) || '发送失败');
            showToast('测试邮件已发送', 'success');
        } catch (e) {
            console.error('测试邮件失败:', e);
            showToast('测试邮件失败：' + e.message, 'error');
        }
    }

    // 设置文档标题
    function setAdminDocumentTitle(siteTitle) {
        try {
            const title = (siteTitle && String(siteTitle).trim()) ? `${siteTitle} - 后台管理面板` : '后台管理面板';
            document.title = title;
        } catch {}
    }

    // Tab切换处理
    function onTabSwitch() {
        // 当切换到系统设置标签页时，加载设置数据
        loadSettings();
    }

    // 初始化模块
    function init() {
        // 获取DOM元素
        settingsTab = document.getElementById('settingsTab');
        settingsContent = document.getElementById('settingsContent');
        settingAdminEmail = document.getElementById('settingAdminEmail');
        settingSmtpHost = document.getElementById('settingSmtpHost');
        settingSmtpPort = document.getElementById('settingSmtpPort');
        settingSmtpTLS = document.getElementById('settingSmtpTLS');
        settingSmtpUser = document.getElementById('settingSmtpUser');
        settingSmtpPass = document.getElementById('settingSmtpPass');
        settingEmailTemplateEnd = document.getElementById('settingEmailTemplateEnd');
        settingEmailTemplateCaptcha = document.getElementById('settingEmailTemplateCaptcha');
        settingEmailTemplateWorkOrder = document.getElementById('settingEmailTemplateWorkOrder');
        settingEmailTemplateMessages = document.getElementById('settingEmailTemplateMessages');
        refreshEmailTemplates = document.getElementById('refreshEmailTemplates');
        saveSettingsButton = document.getElementById('saveSettingsButton');
        testEmailButton = document.getElementById('testEmailButton');
        settingsSaveMsg = document.getElementById('settingsSaveMsg');
        settingSupportContact = document.getElementById('settingSupportContact');
        settingSiteLink = document.getElementById('settingSiteLink');

        // 绑定事件监听器
        if (settingsTab) {
            settingsTab.addEventListener('click', () => {
                // 调用主switchTab函数切换到settings标签页
                if (window.switchTab) {
                    window.switchTab('settings');
                }
            });
        }

        if (saveSettingsButton) {
            saveSettingsButton.addEventListener('click', saveSettings);
        }

        if (testEmailButton) {
            testEmailButton.addEventListener('click', sendTestEmail);
        }

        if (refreshEmailTemplates) {
            refreshEmailTemplates.addEventListener('click', async () => {
                await loadEmailTemplateOptions();
                // 刷新后按缓存回填
                if (settingEmailTemplateEnd) settingEmailTemplateEnd.value = settingsCache.email_template_end || '';
                if (settingEmailTemplateCaptcha) settingEmailTemplateCaptcha.value = settingsCache.email_template_captcha || '';
                if (settingEmailTemplateWorkOrder) settingEmailTemplateWorkOrder.value = settingsCache.email_template_work_order || '';
                if (settingEmailTemplateMessages) settingEmailTemplateMessages.value = settingsCache.email_template_messages || '';
                showToast('模板列表已刷新', 'success');
            });
        }

        console.log('系统设置模块初始化完成');
    }

    // 公共API
    return {
        init,
        setGlobalVars,
        loadSettings,
        saveSettings,
        loadEmailTemplateOptions,
        sendTestEmail,
        onTabSwitch,
        setAdminDocumentTitle
    };
})();

// 全局暴露函数，确保内联 onclick 可用
window.SettingsModule = SettingsModule;
console.log('系统设置模块已暴露到全局:', !!window.SettingsModule);
