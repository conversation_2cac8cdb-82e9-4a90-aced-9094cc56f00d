// 系统日志模块
// 依赖：需要全局变量 currentAdminPassword, showToast, switchTab, escapeHtml

const SystemLogsModule = (function() {
    // 私有变量
    let systemLogsTab = null;
    let systemLogsContent = null;
    let systemLogsContainer = null;
    let systemLogsDays = null;
    let refreshSystemLogsButton = null;

    // 获取当前管理员密码
    function getCurrentAdminPassword() {
        return window.currentAdminPassword || '';
    }

    // 显示提示消息（依赖全局showToast函数）
    function showToast(message, type = 'info') {
        if (window.showToast) {
            window.showToast(message, type);
        }
    }

    // 获取系统日志
    async function fetchSystemLogs() {
        try {
            console.log('SystemLogsModule.fetchSystemLogs called');
            const password = getCurrentAdminPassword();
            console.log('Current admin password:', password ? '***' : 'empty');
            if (!password) {
                console.log('No admin password found, skipping fetch');
                return;
            }

            const days = Number(systemLogsDays?.value || 7);
            console.log('Fetching logs for', days, 'days');
            const url = `${window.location.origin}/admin/system-logs?days=${encodeURIComponent(days)}`;
            console.log('API URL:', url);

            const resp = await fetch(url, {
                headers: { 'X-Admin-Password': password }
            });

            console.log('API response status:', resp.status);
            const result = await resp.json().catch(() => ({ success: false }));
            console.log('API response:', result);

            if (!resp.ok || !result.success) throw new Error(result.message || '读取失败');
            const logs = Array.isArray(result.data) ? result.data : [];
            if (!systemLogsContainer) return;

            if (!logs || logs.length === 0) {
                systemLogsContainer.innerHTML = `
                    <div class="flex items-center justify-center py-12">
                        <div class="text-center">
                            <div class="text-gray-400 mb-2">
                                <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                            <p class="text-gray-500 text-sm">暂无系统日志数据</p>
                            <p class="text-gray-400 text-xs mt-1">选择不同的时间范围或稍后再试</p>
                        </div>
                    </div>
                `;
            } else {
                systemLogsContainer.innerHTML = logs.map(item => `
                    <div class="bg-gray-50 border rounded-md">
                        <div class="px-4 py-2 border-b font-medium text-gray-700">${item.date}</div>
                        <pre class="p-4 whitespace-pre-wrap text-sm leading-6">${escapeHtml(item.content || '')}</pre>
                    </div>
                `).join('');
            }
        } catch (e) {
            console.error('读取系统日志失败:', e);
            showToast('读取系统日志失败: ' + (e.message || '未知错误'), 'error');
        }
    }

    // Tab切换处理
    function onTabSwitch(tabName) {
        console.log('SystemLogsModule.onTabSwitch called with:', tabName);
        if (tabName === 'system-logs') {
            console.log('Fetching system logs...');
            fetchSystemLogs();
        }
    }

    // 设置全局变量（供其他模块调用）
    function setGlobalVars(password) {
        // 系统日志模块不需要特殊的全局变量设置
        // 密码通过全局的window.currentAdminPassword获取
    }

    // 初始化模块
    function init() {
        console.log('SystemLogsModule.init called');

        // 获取DOM元素
        systemLogsTab = document.getElementById('systemLogsTab');
        systemLogsContent = document.getElementById('systemLogsContent');
        systemLogsContainer = document.getElementById('systemLogsContainer');
        systemLogsDays = document.getElementById('systemLogsDays');
        refreshSystemLogsButton = document.getElementById('refreshSystemLogsButton');

        console.log('DOM elements found:', {
            systemLogsTab: !!systemLogsTab,
            systemLogsContent: !!systemLogsContent,
            systemLogsContainer: !!systemLogsContainer,
            systemLogsDays: !!systemLogsDays,
            refreshSystemLogsButton: !!refreshSystemLogsButton
        });

        // 绑定事件监听器
        if (systemLogsTab) {
            systemLogsTab.addEventListener('click', () => {
                // 调用全局的switchTab函数
                if (window.switchTab) {
                    window.switchTab('system-logs');
                }
            });
        }

        if (refreshSystemLogsButton) {
            refreshSystemLogsButton.addEventListener('click', fetchSystemLogs);
        }

        if (systemLogsDays) {
            systemLogsDays.addEventListener('change', fetchSystemLogs);
        }

        console.log('系统日志模块初始化完成');
    }

    // 公共API
    return {
        init,
        fetchSystemLogs,
        onTabSwitch,
        setGlobalVars
    };
})();

// 全局暴露模块
window.SystemLogsModule = SystemLogsModule;
console.log('系统日志模块已暴露到全局:', !!window.SystemLogsModule);
