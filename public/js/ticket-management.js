// 工单管理模块
const TicketManagement = (function() {
    // 私有变量
    let currentTicketId = null;
    let currentTicketPage = 1;
    let currentTicketPerPage = 20;
    let currentAdminPassword = null; // 管理员密码

    // 设置全局变量
    function setGlobalVars(password) {
        currentAdminPassword = password;
    }

    // 获取当前管理员密码
    function getCurrentAdminPassword() {
        return currentAdminPassword || window.currentAdminPassword || '';
    }

    // 显示提示消息（依赖全局showToast函数）
    function showToast(message, type = 'info') {
        if (window.showToast) {
            window.showToast(message, type);
        }
    }





    // 加载工单列表
    async function loadTickets(page = 1) {
        currentTicketPage = page;

        try {
            const statusFilter = document.getElementById('ticketStatusFilter').value;
            const typeFilter = document.getElementById('ticketTypeFilter').value;
            const priorityFilter = document.getElementById('ticketPriorityFilter').value;
            const searchQuery = document.getElementById('ticketSearchInput').value;

            const params = new URLSearchParams({
                page: page,
                perPage: currentTicketPerPage
            });

            if (statusFilter) params.append('status', statusFilter);
            if (typeFilter) params.append('type', typeFilter);
            if (priorityFilter) params.append('priority', priorityFilter);
            if (searchQuery) params.append('search', searchQuery);

            const response = await fetch(`/admin/tickets?${params}`, {
                headers: {
                    'X-Admin-Password': getCurrentAdminPassword()
                }
            });

            if (response.status === 401) {
                showToast('管理员密码验证失败，请先登录', 'error');
                return;
            }

            const result = await response.json();

            if (result.success) {
                displayTicketsList(result.data.tickets);
                displayTicketsPagination(result.data.pagination);
                document.getElementById('ticketsTable').classList.remove('hidden');
            } else {
                showToast(result.message, 'error');
            }
        } catch (error) {
            console.error('加载工单列表失败:', error);
            showToast('加载工单列表失败', 'error');
        }
    }

    // 显示工单列表
    function displayTicketsList(tickets) {
        const tbody = document.getElementById('ticketsTableBody');

        if (!tickets || tickets.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="px-4 py-6 text-center text-gray-500">暂无工单</td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = tickets.map(ticket => `
            <tr>
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">${ticket.ticketNumber}</td>
                <td class="px-4 py-4 text-sm text-gray-900 max-w-xs truncate">${ticket.title}</td>
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">${ticket.typeName}</td>
                <td class="px-4 py-4 whitespace-nowrap">
                    <span class="px-2 py-1 text-xs rounded-full ${getTicketStatusBadgeClass(ticket.status)}">
                        ${getTicketStatusText(ticket.status)}
                    </span>
                </td>
                <td class="px-4 py-4 whitespace-nowrap">
                    <span class="px-2 py-1 text-xs rounded-full ${getTicketPriorityBadgeClass(ticket.priority)}">
                        ${getTicketPriorityText(ticket.priority)}
                    </span>
                </td>
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${ticket.username}<br>
                    <span class="text-gray-500">${ticket.userQQ}</span>
                </td>
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${formatDateTime(ticket.createdTime)}
                </td>
                <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                    <button onclick="TicketManagement.viewTicketDetail(${ticket.id})" class="text-indigo-600 hover:text-indigo-900">查看</button>
                </td>
            </tr>
        `).join('');
    }

    // 显示工单分页
    function displayTicketsPagination(pagination) {
        const container = document.getElementById('ticketsPagination');

        if (pagination.totalPages <= 1) {
            container.innerHTML = '';
            return;
        }

        let paginationHTML = `
            <div class="text-sm text-gray-700">
                显示第 ${(pagination.page - 1) * pagination.perPage + 1} 到
                ${Math.min(pagination.page * pagination.perPage, pagination.total)} 条，
                共 ${pagination.total} 条记录
            </div>
            <div class="flex space-x-2">
        `;

        // 上一页
        if (pagination.page > 1) {
            paginationHTML += `<button onclick="TicketManagement.loadTickets(${pagination.page - 1})" class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">上一页</button>`;
        }

        // 页码
        for (let i = 1; i <= pagination.totalPages; i++) {
            if (i === pagination.page) {
                paginationHTML += `<span class="px-3 py-2 text-sm bg-blue-600 text-white border border-blue-600 rounded-md">${i}</span>`;
            } else {
                paginationHTML += `<button onclick="TicketManagement.loadTickets(${i})" class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">${i}</button>`;
            }
        }

        // 下一页
        if (pagination.page < pagination.totalPages) {
            paginationHTML += `<button onclick="TicketManagement.loadTickets(${pagination.page + 1})" class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">下一页</button>`;
        }

        paginationHTML += '</div>';
        container.innerHTML = paginationHTML;
    }

    // 获取工单状态徽章样式
    function getTicketStatusBadgeClass(status) {
        const statusClasses = {
            'open': 'bg-blue-100 text-blue-800',
            'in_progress': 'bg-yellow-100 text-yellow-800',
            'waiting': 'bg-orange-100 text-orange-800',
            'closed': 'bg-gray-100 text-gray-800',
            'reopened': 'bg-purple-100 text-purple-800'
        };
        return statusClasses[status] || 'bg-gray-100 text-gray-800';
    }

    // 获取工单状态文本
    function getTicketStatusText(status) {
        const statusTexts = {
            'open': '待处理',
            'in_progress': '处理中',
            'waiting': '等待回复',
            'closed': '已关闭',
            'reopened': '重新打开'
        };
        return statusTexts[status] || status;
    }

    // 获取工单优先级徽章样式
    function getTicketPriorityBadgeClass(priority) {
        const priorityClasses = {
            'low': 'bg-green-100 text-green-800',
            'normal': 'bg-blue-100 text-blue-800',
            'high': 'bg-red-100 text-red-800'
        };
        return priorityClasses[priority] || 'bg-blue-100 text-blue-800';
    }

    // 获取工单优先级文本
    function getTicketPriorityText(priority) {
        const priorityTexts = {
            'low': '低',
            'normal': '普通',
            'high': '高'
        };
        return priorityTexts[priority] || '普通';
    }

    // 查看工单详情
    async function viewTicketDetail(ticketId) {
        currentTicketId = ticketId;

        try {
            const response = await fetch(`/admin/tickets/${ticketId}`, {
                headers: {
                    'X-Admin-Password': getCurrentAdminPassword()
                }
            });

            if (response.status === 401) {
                showToast('管理员密码验证失败，请先登录', 'error');
                return;
            }

            const result = await response.json();

            if (result.success) {
                displayTicketDetail(result.data);
                document.getElementById('ticketDetailModal').classList.remove('hidden');
            } else {
                showToast(result.message, 'error');
            }
        } catch (error) {
            console.error('获取工单详情失败:', error);
            showToast('获取工单详情失败', 'error');
        }
    }

    // 显示工单详情
    function displayTicketDetail(ticket) {
        const content = document.getElementById('ticketDetailContent');

        content.innerHTML = `
            <div class="bg-gray-50 p-4 rounded-lg">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <span class="font-medium text-gray-700">工单号:</span> ${ticket.ticketNumber}
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">类型:</span> ${ticket.typeName}
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">状态:</span>
                        <span class="px-2 py-1 text-xs rounded-full ${getTicketStatusBadgeClass(ticket.status)}">
                            ${getTicketStatusText(ticket.status)}
                        </span>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">优先级:</span>
                        <span class="px-2 py-1 text-xs rounded-full ${getTicketPriorityBadgeClass(ticket.priority)}">
                            ${getTicketPriorityText(ticket.priority)}
                        </span>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">用户:</span> ${ticket.username} (${ticket.userQQ})
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">创建时间:</span> ${formatDateTime(ticket.createdTime)}
                    </div>
                </div>

                ${ticket.groupNumber ? `
                    <div class="mb-4 p-3 bg-blue-50 rounded-lg">
                        <span class="font-medium text-blue-900">相关群号:</span> ${ticket.groupNumber}
                    </div>
                ` : ''}

                <div class="mb-4">
                    <h4 class="font-medium text-gray-900 mb-2">问题描述:</h4>
                    <div class="prose prose-sm max-w-none bg-white p-3 rounded border ticket-rich-text">
                        ${renderMarkdown(ticket.content)}
                    </div>
                </div>
                ${ticket.attachments && ticket.attachments.length ? `
                <div class="mb-4">
                    <h4 class="font-medium text-gray-900 mb-2">附件:</h4>
                    <div id="ticketAttachmentsGrid" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3"></div>
                </div>` : ''}
            </div>
        `;

        // 显示回复列表
        displayTicketReplies(ticket.replies);

        // 设置当前状态
        document.getElementById('ticketStatusSelect').value = ticket.status;

        // 将正文中内部 /files/ 图片替换为可预览的对象URL，并启用点击预览
        const rich = content.querySelector('.ticket-rich-text');
        if (rich) {
            rewriteInternalImagesForAdmin(rich);
            enableImagePreview(rich);
        }

        // 渲染附件
        if (ticket.attachments && ticket.attachments.length) {
            const grid = document.getElementById('ticketAttachmentsGrid');
            if (grid) {
                grid.innerHTML = ticket.attachments.map(att => {
                    const isImage = /\.(png|jpe?g|gif|webp|bmp|svg)$/i.test(att.fileName || att.filePath || '');
                    const escapedName = (att.fileName || '').replace(/"/g, '&quot;');
                    if (isImage) {
                        return `
                            <div class="border rounded p-3 bg-white flex flex-col">
                                <button class="w-full inline-flex items-center justify-center px-3 py-2 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
                                    onclick="TicketManagement.openAdminFilePreview('${att.filePath}')">预览图片</button>
                                <div class="mt-2 text-xs text-gray-600 truncate" title="${escapedName}">📷 ${escapedName}</div>
                            </div>
                        `;
                    }
                    return `
                        <div class="border rounded p-3 bg-white flex flex-col">
                            <button class="w-full inline-flex items-center justify-center px-3 py-2 text-sm bg-gray-700 text-white rounded hover:bg-gray-800"
                                onclick="TicketManagement.downloadAdminFile('${att.filePath}', '${escapedName}')">下载附件</button>
                            <div class="mt-2 text-xs text-gray-600 truncate" title="${escapedName}">📎 ${escapedName}</div>
                        </div>
                    `;
                }).join('');
            }
        }
    }

    // 显示工单回复
    function displayTicketReplies(replies) {
        const container = document.getElementById('ticketRepliesList');

        if (replies.length === 0) {
            container.innerHTML = '<p class="text-gray-500 text-center py-4">暂无回复</p>';
            return;
        }

        container.innerHTML = replies.map(reply => `
            <div class="bg-white p-3 rounded-lg border">
                <div class="flex justify-between items-start mb-2">
                    <span class="font-medium ${reply.isAdmin ? 'text-blue-600' : 'text-green-600'}">
                        ${reply.username} ${reply.isAdmin ? '(管理员)' : ''}
                    </span>
                    <span class="text-sm text-gray-500">${formatDateTime(reply.createdTime)}</span>
                </div>
                <div class="prose prose-sm max-w-none ticket-reply-rich-text">
                    ${renderMarkdown(reply.content)}
                </div>
            </div>
        `).join('');

        // 替换回复中的内部 /files/ 图片为对象URL，并启用点击预览
        container.querySelectorAll('.ticket-reply-rich-text').forEach(rich => {
            rewriteInternalImagesForAdmin(rich);
            enableImagePreview(rich);
        });
    }

    // 提交管理员回复
    async function submitAdminReply(content) {
        if (!currentTicketId) return;

        try {
            const response = await fetch(`/admin/tickets/${currentTicketId}/reply`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Admin-Password': getCurrentAdminPassword()
                },
                body: JSON.stringify({ content })
            });

            if (response.status === 401) {
                showToast('管理员密码验证失败，请先登录', 'error');
                return;
            }

            const result = await response.json();

            if (result.success) {
                showToast('回复提交成功', 'success');
                document.getElementById('adminReplyContent').value = '';
                // 重新加载工单详情
                viewTicketDetail(currentTicketId);
            } else {
                showToast(result.message, 'error');
            }
        } catch (error) {
            console.error('提交回复失败:', error);
            showToast('提交回复失败', 'error');
        }
    }

    // 更新工单状态
    async function updateTicketStatus(newStatus) {
        if (!currentTicketId) return;

        try {
            const response = await fetch(`/admin/tickets/${currentTicketId}/status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Admin-Password': getCurrentAdminPassword()
                },
                body: JSON.stringify({
                    status: newStatus,
                    changeReason: '管理员手动更新状态'
                })
            });

            if (response.status === 401) {
                showToast('管理员密码验证失败，请先登录', 'error');
                return;
            }

            const result = await response.json();

            if (result.success) {
                showToast('状态更新成功', 'success');
                // 重新加载工单详情
                viewTicketDetail(currentTicketId);
                // 重新加载工单列表
                loadTickets(currentTicketPage);
            } else {
                showToast(result.message, 'error');
            }
        } catch (error) {
            console.error('更新工单状态失败:', error);
            showToast('更新工单状态失败', 'error');
        }
    }

    // 关闭工单
    async function closeTicket() {
        if (!currentTicketId) return;

        if (!confirm('确定要关闭这个工单吗？')) {
            return;
        }

        try {
            const response = await fetch(`/admin/tickets/${currentTicketId}/status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Admin-Password': getCurrentAdminPassword()
                },
                body: JSON.stringify({
                    status: 'closed',
                    changeReason: '管理员关闭工单'
                })
            });

            if (response.status === 401) {
                showToast('管理员密码验证失败，请先登录', 'error');
                return;
            }

            const result = await response.json();

            if (result.success) {
                showToast('工单已关闭', 'success');
                // 重新加载工单详情
                viewTicketDetail(currentTicketId);
                // 重新加载工单列表
                loadTickets(currentTicketPage);
            } else {
                showToast(result.message, 'error');
            }
        } catch (error) {
            console.error('关闭工单失败:', error);
            showToast('关闭工单失败', 'error');
        }
    }

    // 简易Markdown渲染（支持图片与链接，并保留 /files/ 以便携带的 token 生效）
    function renderMarkdown(text) {
        if (!text) return '';

        const escapeHtml = (s) => String(s)
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;');

        const sanitizeUrl = (url) => {
            try {
                const u = String(url).trim();
                if (/^https?:\/\//i.test(u)) return u; // http/https
                if (/^\//.test(u)) return u;           // 站内路径（如 /files/...）
                return '';
            } catch { return ''; }
        };

        let html = escapeHtml(text);

        // 图片语法
        html = html.replace(/!\[(.*?)\]\((.*?)\)/g, (m, alt, url) => {
            const safeUrl = sanitizeUrl(url);
            const safeAlt = escapeHtml(alt);
            if (!safeUrl) return m;
            return `<img src="${safeUrl}" alt="${safeAlt}" style="max-width:160px;height:auto;border-radius:6px;" />`;
        });

        // 链接语法
        html = html.replace(/\[(.*?)\]\((.*?)\)/g, (m, text, url) => {
            const safeUrl = sanitizeUrl(url);
            const safeText = escapeHtml(text);
            if (!safeUrl) return safeText;
            return `<a href="${safeUrl}" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline break-all">${safeText}</a>`;
        });

        // 强调/代码/换行
        html = html
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`([^`]+)`/g, '<code class="bg-gray-100 px-1 py-0.5 rounded text-sm">$1</code>')
            .replace(/\n/g, '<br>');

        return html;
    }

    // 生成管理端文件下载的临时URL（通过携带管理密码的请求头获取Blob并转为ObjectURL）
    async function getAdminFileObjectUrl(filePath) {
        try {
            const resp = await fetch(`/admin/files/${filePath}`, {
                headers: { 'X-Admin-Password': getCurrentAdminPassword() }
            });
            if (!resp.ok) throw new Error('文件获取失败');
            const blob = await resp.blob();
            return URL.createObjectURL(blob);
        } catch (e) {
            console.error('获取文件失败:', e);
            showToast('获取文件失败', 'error');
            return '';
        }
    }

    // 图片预览
    function enableImagePreview(rootEl) {
        if (!rootEl) return;
        rootEl.querySelectorAll('img').forEach(img => {
            img.style.cursor = 'zoom-in';
            img.addEventListener('click', () => openImagePreview(img.src));
        });
    }

    async function openAdminFilePreview(filePath) {
        const url = await getAdminFileObjectUrl(filePath);
        if (url) openImagePreview(url);
    }

    function openImagePreview(src) {
        const modal = document.getElementById('imagePreviewModal');
        const img = document.getElementById('imagePreviewImg');
        if (!modal || !img) return;
        img.src = src;
        modal.classList.remove('hidden');
    }

    function closeImagePreview() {
        const modal = document.getElementById('imagePreviewModal');
        const img = document.getElementById('imagePreviewImg');
        if (!modal || !img) return;
        img.src = '';
        modal.classList.add('hidden');
    }

    // 将富文本中的 /files/ 内部图片改为可在管理端直接预览的对象URL
    async function rewriteInternalImagesForAdmin(rootEl) {
        const imgs = Array.from(rootEl.querySelectorAll('img'));
        await Promise.all(imgs.map(async (img) => {
            try {
                const m = img.src.match(/\/files\/([^?\s#]+)/);
                if (!m) return;
                const objectUrl = await getAdminFileObjectUrl(m[1]);
                if (objectUrl) img.src = objectUrl;
            } catch (e) {
                console.warn('图片转换失败:', e);
            }
        }));
    }

    // 下载管理端文件
    async function downloadAdminFile(filePath, filename) {
        try {
            const resp = await fetch(`/admin/files/${filePath}`, {
                headers: { 'X-Admin-Password': getCurrentAdminPassword() }
            });
            if (!resp.ok) throw new Error('下载失败');
            const blob = await resp.blob();
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename || 'attachment';
            document.body.appendChild(a);
            a.click();
            a.remove();
            URL.revokeObjectURL(url);
        } catch (e) {
            console.error('下载失败:', e);
            showToast('下载失败', 'error');
        }
    }

    // 格式化日期时间
    function formatDateTime(dateTimeStr) {
        if (!dateTimeStr) return '';

        const date = new Date(dateTimeStr);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    // 处理Tab切换
    function onTabSwitch(tabName) {
        if (tabName === 'tickets') {
            // 切换到工单管理Tab时，加载工单列表
            loadTickets(1);
            // 注意：工单类型筛选器现在由工单类型模块管理
        }
    }

    // 初始化模块
    function init() {

        // 绑定工单管理相关事件
        const refreshTicketsButton = document.getElementById('refreshTicketsButton');
        if (refreshTicketsButton) {
            refreshTicketsButton.addEventListener('click', () => loadTickets(1));
        }

        const closeTicketDetailModal = document.getElementById('closeTicketDetailModal');
        if (closeTicketDetailModal) {
            closeTicketDetailModal.addEventListener('click', () => {
                document.getElementById('ticketDetailModal').classList.add('hidden');
            });
        }

        const adminReplyForm = document.getElementById('adminReplyForm');
        if (adminReplyForm) {
            adminReplyForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const content = document.getElementById('adminReplyContent').value.trim();
                if (!content) {
                    showToast('请输入回复内容', 'error');
                    return;
                }

                submitAdminReply(content);
            });
        }

        const updateTicketStatusButton = document.getElementById('updateTicketStatusButton');
        if (updateTicketStatusButton) {
            updateTicketStatusButton.addEventListener('click', () => {
                const newStatus = document.getElementById('ticketStatusSelect').value;
                updateTicketStatus(newStatus);
            });
        }

        const closeTicketButton = document.getElementById('closeTicketButton');
        if (closeTicketButton) {
            closeTicketButton.addEventListener('click', closeTicket);
        }

        // 图片预览模态框交互
        const imageModal = document.getElementById('imagePreviewModal');
        const closeImageBtn = document.getElementById('closeImagePreview');
        if (imageModal) {
            imageModal.addEventListener('click', (e) => {
                if (e.target === imageModal) {
                    closeImagePreview();
                }
            });
        }
        if (closeImageBtn) {
            closeImageBtn.addEventListener('click', closeImagePreview);
        }

        // 绑定筛选器事件
        const statusFilter = document.getElementById('ticketStatusFilter');
        const typeFilter = document.getElementById('ticketTypeFilter');
        const priorityFilter = document.getElementById('ticketPriorityFilter');
        const searchInput = document.getElementById('ticketSearchInput');

        if (statusFilter) {
            statusFilter.addEventListener('change', () => loadTickets(1));
        }
        if (typeFilter) {
            typeFilter.addEventListener('change', () => loadTickets(1));
        }
        if (priorityFilter) {
            priorityFilter.addEventListener('change', () => loadTickets(1));
        }
        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', () => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => loadTickets(1), 500);
            });
        }

        // 添加点击工单详情模态框外部关闭功能
        const ticketDetailModal = document.getElementById('ticketDetailModal');
        if (ticketDetailModal) {
            ticketDetailModal.addEventListener('click', function(e) {
                if (e.target === ticketDetailModal) {
                    ticketDetailModal.classList.add('hidden');
                }
            });
        }

        // 绑定ESC键关闭图片预览
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') closeImagePreview();
        });

        console.log('工单管理模块初始化完成');
    }

    // 公共API
    return {
        init,
        setGlobalVars,
        onTabSwitch,
        loadTickets,
        viewTicketDetail,
        submitAdminReply,
        updateTicketStatus,
        closeTicket,
        openAdminFilePreview,
        downloadAdminFile,
        openImagePreview,
        closeImagePreview
    };
})();

// 全局暴露函数，确保内联 onclick 可用
window.TicketManagement = TicketManagement;
