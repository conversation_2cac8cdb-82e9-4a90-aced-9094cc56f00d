// 工单类型管理模块
const TicketTypesModule = (function() {
    // 私有变量
    let currentAdminPassword = null;

    // 设置全局变量
    function setGlobalVars(password) {
        currentAdminPassword = password;
    }

    // 获取当前管理员密码
    function getCurrentAdminPassword() {
        return currentAdminPassword || window.currentAdminPassword || '';
    }

    // 显示提示消息（依赖全局showToast函数）
    function showToast(message, type = 'info') {
        if (window.showToast) {
            window.showToast(message, type);
        }
    }

    // 显示加载状态
    function showLoadingState() {
        const loadingMessage = document.getElementById('ticketTypesLoadingMessage');
        const tableContainer = document.querySelector('#ticketTypesContent .overflow-x-auto');
        if (loadingMessage) loadingMessage.classList.remove('hidden');
        if (tableContainer) tableContainer.classList.add('hidden');
    }

    // 隐藏加载状态
    function hideLoadingState() {
        const loadingMessage = document.getElementById('ticketTypesLoadingMessage');
        const tableContainer = document.querySelector('#ticketTypesContent .overflow-x-auto');
        if (loadingMessage) loadingMessage.classList.add('hidden');
        if (tableContainer) tableContainer.classList.remove('hidden');
    }

    // 加载工单类型列表
    async function loadTicketTypes() {
        showLoadingState();

        try {
            const response = await fetch('/admin/ticket-types', {
                headers: {
                    'X-Admin-Password': getCurrentAdminPassword()
                }
            });

            if (response.status === 401) {
                showToast('管理员密码验证失败，请先登录', 'error');
                hideLoadingState();
                return;
            }

            const result = await response.json();

            if (result.success) {
                displayTicketTypesList(result.data);
                updateTicketTypeFilters(result.data);
            } else {
                showToast(result.message, 'error');
            }
        } catch (error) {
            console.error('加载工单类型失败:', error);
            showToast('加载工单类型失败', 'error');
        } finally {
            hideLoadingState();
        }
    }

    // 显示工单类型列表
    function displayTicketTypesList(types) {
        const tbody = document.getElementById('ticketTypesTableBody');

        tbody.innerHTML = types.map(type => `
            <tr>
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900" data-label="ID">${type.id}</td>
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900" data-label="名称">${type.name}</td>
                <td class="px-4 py-4 text-sm text-gray-900 max-w-xs truncate" data-label="描述">${type.description || '-'}</td>
                <td class="px-4 py-4 whitespace-nowrap" data-label="状态">
                    <span class="px-2 py-1 text-xs rounded-full ${type.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                        ${type.isActive ? '启用' : '禁用'}
                    </span>
                </td>
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900" data-label="排序">${type.sortOrder}</td>
                <td class="px-4 py-4 whitespace-nowrap text-sm font-medium" data-label="操作">
                    <button onclick="TicketTypesModule.editTicketType(${type.id})" class="text-indigo-600 hover:text-indigo-900 mr-3">编辑</button>
                    <button onclick="TicketTypesModule.deleteTicketType(${type.id})" class="text-red-600 hover:text-red-900">删除</button>
                </td>
            </tr>
        `).join('');
    }

    // 更新工单类型筛选器
    function updateTicketTypeFilters(types) {
        const filter = document.getElementById('ticketTypeFilter');
        if (!filter) return;

        // 清空现有选项
        filter.innerHTML = '<option value="">全部类型</option>';

        // 添加类型选项
        types.forEach(type => {
            const option = document.createElement('option');
            option.value = type.id;
            option.textContent = type.name;
            filter.appendChild(option);
        });
    }

    // 添加工单类型
    function addTicketType() {
        document.getElementById('ticketTypeModalTitle').textContent = '添加工单类型';
        document.getElementById('ticketTypeId').value = '';
        document.getElementById('ticketTypeName').value = '';
        document.getElementById('ticketTypeDescription').value = '';
        document.getElementById('ticketTypeSortOrder').value = '0';
        document.getElementById('ticketTypeActive').checked = true;

        document.getElementById('ticketTypeModal').classList.remove('hidden');
    }

    // 编辑工单类型
    async function editTicketType(typeId) {
        try {
            const response = await fetch('/admin/ticket-types', {
                headers: {
                    'X-Admin-Password': getCurrentAdminPassword()
                }
            });

            if (response.status === 401) {
                showToast('管理员密码验证失败，请先登录', 'error');
                return;
            }

            const result = await response.json();

            if (result.success) {
                const type = result.data.find(t => t.id === typeId);
                if (type) {
                    document.getElementById('ticketTypeModalTitle').textContent = '编辑工单类型';
                    document.getElementById('ticketTypeId').value = type.id;
                    document.getElementById('ticketTypeName').value = type.name;
                    document.getElementById('ticketTypeDescription').value = type.description || '';
                    document.getElementById('ticketTypeSortOrder').value = type.sortOrder;
                    document.getElementById('ticketTypeActive').checked = type.isActive;

                    document.getElementById('ticketTypeModal').classList.remove('hidden');
                }
            }
        } catch (error) {
            console.error('获取工单类型详情失败:', error);
            showToast('获取工单类型详情失败', 'error');
        }
    }

    // 删除工单类型
    async function deleteTicketType(typeId) {
        if (!confirm('确定要删除这个工单类型吗？')) {
            return;
        }

        try {
            const response = await fetch('/admin/ticket-types/delete', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Admin-Password': getCurrentAdminPassword()
                },
                body: JSON.stringify({ id: typeId })
            });

            if (response.status === 401) {
                showToast('管理员密码验证失败，请先登录', 'error');
                return;
            }

            const result = await response.json();

            if (result.success) {
                showToast('工单类型删除成功', 'success');
                loadTicketTypes();
            } else {
                showToast(result.message, 'error');
            }
        } catch (error) {
            console.error('删除工单类型失败:', error);
            showToast('删除工单类型失败', 'error');
        }
    }

    // 保存工单类型
    async function saveTicketType(formData) {
        // 兜底：将 id 转为数字或移除
        if (formData && typeof formData.id !== 'undefined' && formData.id !== null && formData.id !== '') {
            const parsedId = parseInt(formData.id, 10);
            if (!Number.isNaN(parsedId)) {
                formData.id = parsedId;
            } else {
                delete formData.id;
            }
        } else if (formData) {
            delete formData.id;
        }
        // 兜底：排序转数字
        if (formData && typeof formData.sortOrder !== 'undefined') {
            const parsedSort = parseInt(formData.sortOrder, 10);
            formData.sortOrder = Number.isNaN(parsedSort) ? 0 : parsedSort;
        }

        try {
            // 检查密码是否存在
            if (!getCurrentAdminPassword()) {
                showToast('请先登录管理员账户', 'error');
                return;
            }

            // 前端基础校验
            const nameToValidate = (formData.name || '').trim();
            if (!nameToValidate) {
                showToast('类型名称不能为空', 'error');
                return;
            }

            const response = await fetch('/admin/ticket-types/save', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Admin-Password': getCurrentAdminPassword()
                },
                body: JSON.stringify(formData)
            });

            // 检查HTTP状态码
            if (response.status === 401) {
                showToast('管理员密码验证失败，请重新登录', 'error');
                // 不要跳转到登录界面，而是提示用户
                return;
            }

            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status}`);
            }

            const result = await response.json();

            if (result.success) {
                showToast('工单类型保存成功', 'success');
                document.getElementById('ticketTypeModal').classList.add('hidden');
                // 重置表单，防止旧值残留
                const formEl = document.getElementById('ticketTypeForm');
                if (formEl) formEl.reset();
                loadTicketTypes();
            } else {
                showToast(result.message || '保存失败', 'error');
            }
        } catch (error) {
            console.error('保存工单类型失败:', error);
            showToast('保存工单类型失败: ' + error.message, 'error');
        }
    }

    // 初始化模块
    function init() {
        // 绑定工单类型相关事件
        const addTicketTypeButton = document.getElementById('addTicketTypeButton');
        if (addTicketTypeButton) {
            addTicketTypeButton.addEventListener('click', addTicketType);
        }

        const closeTicketTypeModal = document.getElementById('closeTicketTypeModal');
        if (closeTicketTypeModal) {
            closeTicketTypeModal.addEventListener('click', () => {
                document.getElementById('ticketTypeModal').classList.add('hidden');
            });
        }

        const cancelTicketTypeButton = document.getElementById('cancelTicketTypeButton');
        if (cancelTicketTypeButton) {
            cancelTicketTypeButton.addEventListener('click', () => {
                document.getElementById('ticketTypeModal').classList.add('hidden');
            });
        }

        const ticketTypeForm = document.getElementById('ticketTypeForm');
        if (ticketTypeForm) {
            ticketTypeForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const formData = {
                    id: document.getElementById('ticketTypeId').value,
                    name: document.getElementById('ticketTypeName').value,
                    description: document.getElementById('ticketTypeDescription').value,
                    isActive: document.getElementById('ticketTypeActive').checked,
                    sortOrder: parseInt(document.getElementById('ticketTypeSortOrder').value) || 0
                };

                saveTicketType(formData);
            });
        }

        // 兜底：直接绑定保存按钮点击，避免部分浏览器阻止表单提交事件
        const saveTicketTypeButton = document.getElementById('saveTicketTypeButton');
        if (saveTicketTypeButton) {
            saveTicketTypeButton.addEventListener('click', function(e) {
                e.preventDefault();
                const formData = {
                    id: document.getElementById('ticketTypeId').value,
                    name: document.getElementById('ticketTypeName').value,
                    description: document.getElementById('ticketTypeDescription').value,
                    isActive: document.getElementById('ticketTypeActive').checked,
                    sortOrder: document.getElementById('ticketTypeSortOrder').value
                };
                saveTicketType(formData);
            });
        }

        // 添加点击模态框外部关闭功能
        const ticketTypeModal = document.getElementById('ticketTypeModal');
        if (ticketTypeModal) {
            ticketTypeModal.addEventListener('click', function(e) {
                // 如果点击的是模态框背景（不是内容区域），则关闭模态框
                if (e.target === ticketTypeModal) {
                    ticketTypeModal.classList.add('hidden');
                }
            });
        }

        console.log('工单类型模块初始化完成');
    }

    // 公共API
    return {
        init,
        setGlobalVars,
        loadTicketTypes,
        addTicketType,
        editTicketType,
        deleteTicketType,
        saveTicketType
    };
})();

// 全局暴露函数，确保内联 onclick 可用
window.TicketTypesModule = TicketTypesModule;
