// 档位管理模块
console.log('档位管理模块开始加载...');
const TiersModule = (function() {
    // 私有变量
    let currentAdminPassword = null;
    let tiersConfig = [];

    // DOM 元素
    let tiersTab = null;
    let tiersContent = null;
    let addTierButton = null;
    let closeTierModalBtn = null;
    let cancelTierButton = null;
    let tierForm = null;

    // 设置全局变量
    function setGlobalVars(password) {
        currentAdminPassword = password;
    }

    // 获取当前管理员密码
    function getCurrentAdminPassword() {
        return currentAdminPassword || window.currentAdminPassword || '';
    }

    // 显示提示消息（依赖全局showToast函数）
    function showToast(message, type = 'info') {
        if (window.showToast) {
            window.showToast(message, type);
        }
    }

    // 加载档位配置
    async function loadTiersConfig() {
        try {
            const response = await fetch('/admin/tiers', {
                headers: {
                    'X-Admin-Password': getCurrentAdminPassword()
                }
            });

            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    tiersConfig = result.data;
                    populateTierSelects();
                    renderTiersTable();
                } else {
                    // API失败时显示空状态
                    tiersConfig = [];
                    renderTiersTable();
                }
            } else {
                // 请求失败时显示空状态
                tiersConfig = [];
                renderTiersTable();
            }
        } catch (error) {
            console.error('加载档位配置失败:', error);
            tiersConfig = [];
            renderTiersTable();
        }
    }

    // 填充档位选择下拉框
    function populateTierSelects() {
        // 填充绑定模态框的档位选择
        const modalSkuId = document.getElementById('modalSkuId');
        if (modalSkuId) {
            modalSkuId.innerHTML = tiersConfig.map(tier =>
                `<option value="${tier.sku_id}">${tier.display_name} (¥${tier.price || 0})</option>`
            ).join('');
        }

        // 填充创建兑换码模态框的档位选择
        const codesSkuId = document.getElementById('codesSkuId');
        if (codesSkuId) {
            codesSkuId.innerHTML = tiersConfig.map(tier =>
                `<option value="${tier.sku_id}">${tier.display_name} (¥${tier.price || 0})</option>`
            ).join('');
        }

        // 填充功能黑名单的档位多选框
        const featureTiersCheckboxes = document.getElementById('featureTiersCheckboxes');
        if (featureTiersCheckboxes) {
            featureTiersCheckboxes.innerHTML = tiersConfig.map(tier =>
                `<label class="flex items-center space-x-2">
                    <input type="checkbox" name="allowedTiers" value="${tier.sku_id}" class="rounded">
                    <span>${tier.display_name}</span>
                </label>`
            ).join('');
        }
    }

    // 渲染档位表格
    function renderTiersTable() {
        try {
            const tbody = document.getElementById('tiersTableBody');
        if (!tbody) {
            console.error('tiersTableBody元素未找到');
            return;
        }

        tbody.innerHTML = '';

        if (!tiersConfig || tiersConfig.length === 0) {
            tbody.innerHTML = '<tr><td colspan="5" class="text-center py-8 text-gray-500">暂无档位数据</td></tr>';
            return;
        }

            (tiersConfig || []).forEach(tier => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td class="px-4 py-3 text-sm">${escapeHtml(tier.sku_id)}</td>
                    <td class="px-4 py-3 text-sm">${escapeHtml(tier.display_name || tier.name || '')}</td>
                    <td class="px-4 py-3 text-sm">${Number(tier.price || 0)}</td>
                    <td class="px-4 py-3 text-sm">${escapeHtml(tier.description || '')}</td>
                    <td class="px-4 py-3 text-sm">
                        <button class="text-blue-600 hover:text-blue-800 mr-3" data-action="edit" data-sku="${escapeHtml(tier.sku_id)}">编辑</button>
                        <button class="text-red-600 hover:text-red-800" data-action="delete" data-sku="${escapeHtml(tier.sku_id)}">删除</button>
                    </td>
                `;
                tbody.appendChild(tr);
            });

            // 绑定操作事件
            tbody.querySelectorAll('button[data-action="edit"]').forEach(btn => {
                btn.addEventListener('click', () => openTierModal(btn.dataset.sku));
            });
            tbody.querySelectorAll('button[data-action="delete"]').forEach(btn => {
                btn.addEventListener('click', () => deleteTier(btn.dataset.sku));
            });


        } catch (e) {
            console.error('渲染档位表失败:', e);
        }
    }

    // 打开档位编辑模态框（skuId 为空表示新增）
    function openTierModal(skuId) {
        const modal = document.getElementById('tierModal');
        const title = document.getElementById('tierModalTitle');
        const skuInput = document.getElementById('tierSkuId');
        const nameInput = document.getElementById('tierDisplayName');
        const priceInput = document.getElementById('tierPrice');
        const descInput = document.getElementById('tierDescription');
        const featuresInput = document.getElementById('tierFeatures');

        if (!modal) return;

        let tier = null;
        if (skuId) {
            tier = (tiersConfig || []).find(t => t.sku_id === skuId) || null;
        }

        if (tier) {
            title.textContent = '编辑档位';
            skuInput.value = tier.sku_id;
            nameInput.value = tier.display_name || tier.name || '';
            priceInput.value = Number(tier.price || 0);
            descInput.value = tier.description || '';
            featuresInput.value = (tier.features || []).join(',');
            skuInput.disabled = false; // 允许修改 sku，后端按新 sku 覆盖
        } else {
            title.textContent = '添加档位';
            skuInput.value = '';
            nameInput.value = '';
            priceInput.value = 0;
            descInput.value = '';
            featuresInput.value = '';
            skuInput.disabled = false;
        }

        modal.classList.remove('hidden');
    }

    // 关闭档位模态框
    function closeTierModal() {
        const modal = document.getElementById('tierModal');
        if (modal) modal.classList.add('hidden');
    }

    // 提交保存档位
    async function submitTierForm(e) {
        e.preventDefault();

        try {
            const sku = document.getElementById('tierSkuId').value.trim();
            const name = document.getElementById('tierDisplayName').value.trim();
            const price = Number(document.getElementById('tierPrice').value || 0);
            const description = document.getElementById('tierDescription').value.trim();
            const featuresRaw = document.getElementById('tierFeatures').value.trim();

            if (!sku || !name) {
                showToast('SKU 与名称必填', 'error');
                return;
            }

            const body = {
                sku_id: sku,
                display_name: name,
                name: name,
                price: isNaN(price) ? 0 : price,
                description: description,
                features: featuresRaw ? featuresRaw.split(',').map(s => s.trim()).filter(Boolean) : []
            };

            const resp = await fetch('/admin/tiers/upsert', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Admin-Password': getCurrentAdminPassword()
                },
                body: JSON.stringify(body)
            });

            const result = await resp.json().catch(() => ({ success: false }));

            if (!resp.ok || !result.success) {
                throw new Error((result && result.message) || '保存失败');
            }

            showToast('保存成功', 'success');

            // 重新加载并刷新依赖下拉
            await loadTiersConfig();
            closeTierModal();
        } catch (e) {
            console.error('保存档位失败:', e);
            showToast('保存失败：' + e.message, 'error');
        }
    }

    // 删除档位
    async function deleteTier(skuId) {
        if (!confirm('确定删除该档位吗？若已有兑换码或绑定使用，将无法删除。')) return;

        try {
            const resp = await fetch('/admin/tiers/delete', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Admin-Password': getCurrentAdminPassword()
                },
                body: JSON.stringify({ sku_id: skuId })
            });

            const result = await resp.json().catch(() => ({ success: false }));

            if (!resp.ok || !result.success) {
                // 若返回 IN_USE，提示更明确信息
                const msg = (result && result.message) ? result.message : '删除失败';
                showToast(msg, 'error');
                return;
            }

            showToast('删除成功', 'success');
            await loadTiersConfig();
        } catch (e) {
            console.error('删除档位失败:', e);
            showToast('删除失败：' + e.message, 'error');
        }
    }

    // 初始化模块
    function init() {
        // 获取DOM元素
        tiersTab = document.getElementById('tiersTab');
        tiersContent = document.getElementById('tiersContent');
        addTierButton = document.getElementById('addTierButton');
        closeTierModalBtn = document.getElementById('closeTierModal');
        cancelTierButton = document.getElementById('cancelTierButton');
        tierForm = document.getElementById('tierForm');

        // Tab点击事件已在HTML中绑定，这里不需要重复绑定

        // 绑定事件监听器
        if (addTierButton) {
            addTierButton.addEventListener('click', () => openTierModal());
        }

        if (closeTierModalBtn) {
            closeTierModalBtn.addEventListener('click', closeTierModal);
        }

        if (cancelTierButton) {
            cancelTierButton.addEventListener('click', closeTierModal);
        }

        if (tierForm) {
            tierForm.addEventListener('submit', submitTierForm);
        }

        // 点击模态框外部关闭
        const tierModal = document.getElementById('tierModal');
        if (tierModal) {
            tierModal.addEventListener('click', function(e) {
                if (e.target === tierModal) {
                    closeTierModal();
                }
            });
        }

        console.log('档位管理模块初始化完成');
    }

    // 获取档位配置（供其他模块使用）
    function getTiersConfig() {
        return tiersConfig;
    }

    // 公共API
    return {
        init,
        setGlobalVars,
        loadTiersConfig,
        populateTierSelects,
        renderTiersTable,
        openTierModal,
        closeTierModal,
        submitTierForm,
        deleteTier,
        getTiersConfig
    };
})();

// 全局暴露函数，确保内联 onclick 可用
window.TiersModule = TiersModule;
console.log('档位管理模块已暴露到全局:', !!window.TiersModule);
