// 用户管理模块
// DOM 元素 - 延迟初始化
let usersTab, usersContent, usersTableBody, usersLoadingMessage, userSearchInput, refreshUsersButton, createUserButton;
let userEditModal, userEditForm, userEditError, closeUserEditModal, cancelUserEditButton, saveUserButton;
let banUserModal, banUserForm, banUserError, closeBanUserModal, cancelBanButton, confirmBanButton;
let sendEmailModal, sendEmailForm, sendEmailUserId, sendEmailTo, sendEmailSubject, sendEmailContent, sendEmailError, closeSendEmailModal, cancelSendEmailButton, confirmSendEmailButton;
let editEmailModal, editEmailForm, closeEditEmailModal, cancelEditEmailButton, saveEditEmailButton;
let createUserModal, createUserForm, closeCreateUserModal, cancelCreateUserButton, saveCreateUserButton;

// 初始化DOM元素
function initDOMElements() {
    // 基础元素
    usersTab = document.getElementById('usersTab');
    usersContent = document.getElementById('usersContent');
    usersTableBody = document.getElementById('users-table-body');
    usersLoadingMessage = document.getElementById('usersLoadingMessage');
    userSearchInput = document.getElementById('userSearchInput');
    refreshUsersButton = document.getElementById('refreshUsersButton');
    createUserButton = document.getElementById('createUserButton');

    // 用户编辑相关元素
    userEditModal = document.getElementById('userEditModal');
    userEditForm = document.getElementById('userEditForm');
    userEditError = document.getElementById('userEditError');
    closeUserEditModal = document.getElementById('closeUserEditModal');
    cancelUserEditButton = document.getElementById('cancelUserEditButton');
    saveUserButton = document.getElementById('saveUserButton');

    // 封禁用户相关元素
    banUserModal = document.getElementById('banUserModal');
    banUserForm = document.getElementById('banUserForm');
    banUserError = document.getElementById('banUserError');
    closeBanUserModal = document.getElementById('closeBanUserModal');
    cancelBanButton = document.getElementById('cancelBanButton');
    confirmBanButton = document.getElementById('confirmBanButton');

    // 发送邮件相关元素
    sendEmailModal = document.getElementById('sendEmailModal');
    sendEmailForm = document.getElementById('sendEmailForm');
    sendEmailUserId = document.getElementById('sendEmailUserId');
    sendEmailTo = document.getElementById('sendEmailTo');
    sendEmailSubject = document.getElementById('sendEmailSubject');
    sendEmailContent = document.getElementById('sendEmailContent');
    sendEmailError = document.getElementById('sendEmailError');
    closeSendEmailModal = document.getElementById('closeSendEmailModal');
    cancelSendEmailButton = document.getElementById('cancelSendEmailButton');
    confirmSendEmailButton = document.getElementById('confirmSendEmailButton');

    // 编辑邮箱相关元素
    editEmailModal = document.getElementById('editEmailModal');
    editEmailForm = document.getElementById('editEmailForm');
    closeEditEmailModal = document.getElementById('closeEditEmailModal');
    cancelEditEmailButton = document.getElementById('cancelEditEmailButton');
    saveEditEmailButton = document.getElementById('saveEditEmailButton');

    // 创建用户相关元素
    createUserModal = document.getElementById('createUserModal');
    createUserForm = document.getElementById('createUserForm');
    closeCreateUserModal = document.getElementById('closeCreateUserModal');
    cancelCreateUserButton = document.getElementById('cancelCreateUserButton');
    saveCreateUserButton = document.getElementById('saveCreateUserButton');
}

// 初始化用户管理模块
function initUsersModule() {
    console.log('初始化用户管理模块...');

    // 先初始化DOM元素
    initDOMElements();

    // 绑定事件监听器
    bindUsersEvents();

    // 如果当前是用户管理tab，加载数据
    if (usersTab && usersTab.classList.contains('active')) {
        fetchUsers();
    }
}

// 绑定用户管理相关的事件
function bindUsersEvents() {
    // 确保DOM元素已初始化
    if (!userSearchInput || !refreshUsersButton) {
        initDOMElements();
    }

    // 如果还没有获取到元素，尝试获取
    if (!userSearchInput) {
        userSearchInput = document.getElementById('userSearchInput');
    }
    if (!refreshUsersButton) {
        refreshUsersButton = document.getElementById('refreshUsersButton');
    }

    // 搜索和刷新
    if (userSearchInput) {
        userSearchInput.addEventListener('input', fetchUsers);
    }
    if (refreshUsersButton) {
        refreshUsersButton.addEventListener('click', fetchUsers);
    }

    // 创建用户
    if (!createUserButton) {
        createUserButton = document.getElementById('createUserButton');
    }
    if (!createUserForm) {
        createUserForm = document.getElementById('createUserForm');
    }
    if (!closeCreateUserModal) {
        closeCreateUserModal = document.getElementById('closeCreateUserModal');
    }
    if (!cancelCreateUserButton) {
        cancelCreateUserButton = document.getElementById('cancelCreateUserButton');
    }
    if (!createUserModal) {
        createUserModal = document.getElementById('createUserModal');
    }

    if (createUserButton) {
        createUserButton.addEventListener('click', openCreateUserModal);
    }
    if (createUserForm) {
        createUserForm.addEventListener('submit', submitCreateUserForm);
    }
    if (closeCreateUserModal && createUserModal) {
        closeCreateUserModal.addEventListener('click', () => createUserModal.classList.add('hidden'));
    }
    if (cancelCreateUserButton && createUserModal) {
        cancelCreateUserButton.addEventListener('click', () => createUserModal.classList.add('hidden'));
    }

    // 确保其他DOM元素已初始化
    if (!userEditModal || !userEditForm || !closeUserEditModal || !cancelUserEditButton ||
        !banUserModal || !banUserForm || !closeBanUserModal || !cancelBanButton ||
        !sendEmailModal || !sendEmailForm || !closeSendEmailModal || !cancelSendEmailButton ||
        !editEmailModal || !editEmailForm || !closeEditEmailModal || !cancelEditEmailButton) {
        initDOMElements();
    }

    // 编辑用户
    if (userEditForm) {
        userEditForm.addEventListener('submit', saveUserEdit);
    }
    if (closeUserEditModal && userEditModal) {
        closeUserEditModal.addEventListener('click', () => userEditModal.classList.add('hidden'));
    }
    if (cancelUserEditButton && userEditModal) {
        cancelUserEditButton.addEventListener('click', () => userEditModal.classList.add('hidden'));
    }

    // 封禁用户
    if (banUserForm) {
        banUserForm.addEventListener('submit', banUser);
    }
    if (closeBanUserModal && banUserModal) {
        closeBanUserModal.addEventListener('click', () => banUserModal.classList.add('hidden'));
    }
    if (cancelBanButton && banUserModal) {
        cancelBanButton.addEventListener('click', () => banUserModal.classList.add('hidden'));
    }

    // 发送邮件
    if (sendEmailForm) {
        sendEmailForm.addEventListener('submit', submitSendEmail);
    }
    if (closeSendEmailModal && sendEmailModal) {
        closeSendEmailModal.addEventListener('click', () => sendEmailModal.classList.add('hidden'));
    }
    if (cancelSendEmailButton && sendEmailModal) {
        cancelSendEmailButton.addEventListener('click', () => sendEmailModal.classList.add('hidden'));
    }

    // 编辑邮箱
    if (editEmailForm) {
        editEmailForm.addEventListener('submit', submitEditEmailForm);
    }
    if (closeEditEmailModal && editEmailModal) {
        closeEditEmailModal.addEventListener('click', () => editEmailModal.classList.add('hidden'));
    }
    if (cancelEditEmailButton && editEmailModal) {
        cancelEditEmailButton.addEventListener('click', () => editEmailModal.classList.add('hidden'));
    }

    // 点击模态框背景关闭
    [userEditModal, banUserModal, sendEmailModal, editEmailModal, createUserModal].forEach(modal => {
        if (modal) {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) modal.classList.add('hidden');
            });
        }
    });
}

// 获取用户列表
async function fetchUsers() {
    // 确保DOM元素已初始化
    if (!usersLoadingMessage || !usersTableBody) {
        initDOMElements();
    }

    // 如果还没有获取到元素，尝试获取
    if (!usersLoadingMessage) {
        usersLoadingMessage = document.getElementById('usersLoadingMessage');
    }
    if (!usersTableBody) {
        usersTableBody = document.getElementById('users-table-body');
    }

    // 检查元素是否存在
    if (!usersLoadingMessage || !usersTableBody) {
        console.error('无法找到用户管理相关的DOM元素');
        return;
    }

    usersLoadingMessage.classList.remove('hidden');
    usersTableBody.innerHTML = '';

    try {
        const response = await fetch('/admin/users', {
            method: 'GET',
            headers: {
                'X-Admin-Password': currentAdminPassword
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error ${response.status}`);
        }

        const result = await response.json();
        if (result.success && result.data) {
            populateUsersTable(result.data);
        }
    } catch (error) {
        console.error('获取用户列表时出错:', error);
        showToast(`错误: ${error.message}`, 5000);
    } finally {
        usersLoadingMessage.classList.add('hidden');
    }
}

// 填充用户表格
function populateUsersTable(users) {
    // 确保DOM元素已初始化
    if (!usersTableBody) {
        usersTableBody = document.getElementById('users-table-body');
    }
    if (!userSearchInput) {
        userSearchInput = document.getElementById('userSearchInput');
    }

    // 检查元素是否存在
    if (!usersTableBody || !userSearchInput) {
        console.error('无法找到用户管理表格或搜索输入框');
        return;
    }

    usersTableBody.innerHTML = '';

    // 根据搜索关键词过滤
    const searchTerm = userSearchInput.value.toLowerCase();
    const filteredUsers = users.filter(user =>
        user.username.toLowerCase().includes(searchTerm) ||
        user.email.toLowerCase().includes(searchTerm) ||
        (user.qq && user.qq.includes(searchTerm))
    );

    filteredUsers.forEach(user => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td class="px-4 py-3 text-sm" data-label="ID">${user.id}</td>
            <td class="px-4 py-3 text-sm font-medium" data-label="用户名">${user.username}</td>
            <td class="px-4 py-3 text-sm" data-label="邮箱">${user.email}</td>
            <td class="px-4 py-3 text-sm" data-label="QQ">${user.qq || '未绑定'}</td>
            <td class="px-4 py-3 text-sm" data-label="注册时间">
                ${new Date(user.registerTime).toLocaleString('zh-CN')}
            </td>
            <td class="px-4 py-3 text-sm" data-label="绑定数">
                ${user.bindingCount} 个群 / ${user.codeCount} 个码
            </td>
            <td class="px-4 py-3" data-label="状态">
                ${user.isBanned ?
                    `<span class="px-2 py-1 text-xs font-medium text-red-800 bg-red-100 rounded-full">已封禁</span>` :
                    `<span class="px-2 py-1 text-xs font-medium text-green-800 bg-green-100 rounded-full">正常</span>`}
            </td>
            <td class="px-4 py-3 text-sm text-center" data-label="操作">
                <div class="flex justify-center gap-2 flex-wrap">
                    <button onclick="openEditUserModal(${user.id}, '${user.username}', '${user.email}', '${user.qq || ''}')"
                            class="text-blue-600 hover:text-blue-900">编辑</button>
                    <button onclick="openEditEmailModal(${user.id}, '${user.email}')"
                            class="text-purple-600 hover:text-purple-900">编辑邮箱</button>
                    ${user.isBanned ?
                        `<button onclick="unbanUser(${user.id})" class="text-green-600 hover:text-green-900">解封</button>` :
                        `<button onclick=\"openBanUserModal(${user.id}, '${user.username}')\" class=\"text-orange-600 hover:text-orange-900\">封禁</button>`
                    }
                    <button onclick="deleteUser(${user.id}, '${user.username}')" class="text-red-600 hover:text-red-900">删除</button>
                    <button onclick="openSendEmailModal(${user.id}, '${user.email}')" class="text-gray-700 hover:text-black">发邮件</button>
                </div>
            </td>
        `;
        usersTableBody.appendChild(row);
    });
}

// 打开编辑用户模态框
window.openEditUserModal = function(id, username, email, qq) {
    // 确保DOM元素已初始化
    if (!userEditModal) {
        userEditModal = document.getElementById('userEditModal');
    }
    if (!userEditError) {
        userEditError = document.getElementById('userEditError');
    }

    if (!userEditModal || !userEditError) {
        console.error('无法找到编辑用户模态框或错误元素');
        return;
    }

    document.getElementById('editUserId').value = id;
    document.getElementById('editUsername').value = username;
    document.getElementById('editEmail').value = email;
    document.getElementById('editQQ').value = qq;
    document.getElementById('editQQ').setAttribute('data-original', qq);
    document.getElementById('editPassword').value = '';
    userEditError.textContent = '';
    userEditModal.classList.remove('hidden');
};

// 保存用户编辑
async function saveUserEdit(e) {
    e.preventDefault();

    const userId = document.getElementById('editUserId').value;
    const newQQ = document.getElementById('editQQ').value.trim();
    const newPassword = document.getElementById('editPassword').value.trim();

    showLoading(saveUserButton);
    userEditError.textContent = '';

    try {
        // 更新QQ
        if (newQQ !== document.getElementById('editQQ').getAttribute('data-original')) {
            const response = await fetch('/admin/users/update-qq', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Admin-Password': currentAdminPassword
                },
                body: JSON.stringify({ userId, newQQ })
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.message || '更新QQ失败');
            }
        }

        // 更新密码
        if (newPassword) {
            const response = await fetch('/admin/users/update-password', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Admin-Password': currentAdminPassword
                },
                body: JSON.stringify({ userId, newPassword })
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.message || '更新密码失败');
            }
        }

        showToast('用户信息更新成功');
        userEditModal.classList.add('hidden');
        fetchUsers();
    } catch (error) {
        console.error('保存用户编辑时出错:', error);
        userEditError.textContent = error.message;
    } finally {
        hideLoading(saveUserButton);
    }
}

// 打开封禁用户模态框
window.openBanUserModal = function(id, username) {
    // 确保DOM元素已初始化
    if (!banUserModal) {
        banUserModal = document.getElementById('banUserModal');
    }
    if (!banUserError) {
        banUserError = document.getElementById('banUserError');
    }

    if (!banUserModal || !banUserError) {
        console.error('无法找到封禁用户模态框或错误元素');
        return;
    }

    document.getElementById('banUserId').value = id;
    document.getElementById('banUsername').textContent = username;
    document.getElementById('banReason').value = '';
    banUserError.textContent = '';
    banUserModal.classList.remove('hidden');
};

// 封禁用户
async function banUser(e) {
    e.preventDefault();

    const userId = document.getElementById('banUserId').value;
    const banReason = document.getElementById('banReason').value.trim();

    if (!banReason) {
        banUserError.textContent = '请输入封禁原因';
        return;
    }

    showLoading(confirmBanButton);
    banUserError.textContent = '';

    try {
        const response = await fetch('/admin/users/ban', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Admin-Password': currentAdminPassword
            },
            body: JSON.stringify({ userId, isBanned: true, banReason })
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.message || '封禁失败');
        }

        showToast('用户已被封禁');
        banUserModal.classList.add('hidden');
        fetchUsers();
        // 用户状态变化可能影响绑定，刷新绑定列表
        if (bindingsTab && bindingsTab.classList.contains('active')) {
            fetchBindings();
        }
    } catch (error) {
        console.error('封禁用户时出错:', error);
        banUserError.textContent = error.message;
    } finally {
        hideLoading(confirmBanButton);
    }
}

// 解封用户
window.unbanUser = async function(userId) {
    if (!confirm('确定要解封该用户吗？')) {
        return;
    }

    try {
        const response = await fetch('/admin/users/ban', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Admin-Password': currentAdminPassword
            },
            body: JSON.stringify({ userId, isBanned: false })
        });

        if (!response.ok) {
            throw new Error(`HTTP error ${response.status}`);
        }

        showToast('用户已解封');
        fetchUsers();
        // 用户状态变化可能影响绑定，刷新绑定列表
        if (bindingsTab && bindingsTab.classList.contains('active')) {
            fetchBindings();
        }
    } catch (error) {
        console.error('解封用户时出错:', error);
        showToast(`错误: ${error.message}`, 5000);
    }
};

// 删除用户
window.deleteUser = async function(userId, username) {
    if (!confirm(`确定要删除用户 ${username} 吗？此操作不可恢复！`)) {
        return;
    }

    try {
        const response = await fetch('/admin/users/delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Admin-Password': currentAdminPassword
            },
            body: JSON.stringify({ userId })
        });

        if (!response.ok) {
            throw new Error(`HTTP error ${response.status}`);
        }

        showToast('用户已删除');
        fetchUsers();
        // 用户删除可能影响绑定，刷新绑定列表
        if (bindingsTab && bindingsTab.classList.contains('active')) {
            fetchBindings();
        }
    } catch (error) {
        console.error('删除用户时出错:', error);
        showToast(`错误: ${error.message}`, 5000);
    }
};

// 打开创建用户模态框
window.openCreateUserModal = function() {
    // 确保DOM元素已初始化
    if (!createUserModal) {
        createUserModal = document.getElementById('createUserModal');
    }

    if (!createUserModal) {
        console.error('无法找到创建用户模态框');
        return;
    }

    document.getElementById('createUsername').value = '';
    document.getElementById('createEmail').value = '';
    document.getElementById('createPassword').value = '';
    document.getElementById('createQQ').value = '';
    document.getElementById('createUserModalError').textContent = '';
    createUserModal.classList.remove('hidden');
};

// 提交创建用户表单
async function submitCreateUserForm(e) {
    e.preventDefault();

    const username = document.getElementById('createUsername').value.trim();
    const email = document.getElementById('createEmail').value.trim();
    const password = document.getElementById('createPassword').value;
    const qq = document.getElementById('createQQ').value.trim();
    const errorElement = document.getElementById('createUserModalError');

    if (!username || !email || !password || !qq) {
        errorElement.textContent = '所有字段都不能为空';
        return;
    }

    if (password.length < 6) {
        errorElement.textContent = '密码长度至少6位';
        return;
    }

    showLoading(saveCreateUserButton);
    errorElement.textContent = '';

    try {
        const response = await fetch('/admin/users/create', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Admin-Password': currentAdminPassword
            },
            body: JSON.stringify({
                username: username,
                email: email,
                password: password,
                qq: qq
            })
        });

        const result = await response.json();

        if (result.success) {
            showToast('用户创建成功');
            createUserModal.classList.add('hidden');
            fetchUsers(); // 刷新用户列表
        } else {
            errorElement.textContent = result.message || '创建失败';
        }
    } catch (error) {
        console.error('创建用户时出错:', error);
        errorElement.textContent = '创建失败，请稍后重试';
    } finally {
        hideLoading(saveCreateUserButton);
    }
}

// 打开发送邮件模态框（从用户行）
window.openSendEmailModal = function(userId, email) {
    if (!email) {
        showToast('该用户未绑定邮箱', 'error');
        return;
    }

    // 确保DOM元素已初始化
    if (!sendEmailModal) {
        sendEmailModal = document.getElementById('sendEmailModal');
    }
    if (!sendEmailUserId) {
        sendEmailUserId = document.getElementById('sendEmailUserId');
    }
    if (!sendEmailTo) {
        sendEmailTo = document.getElementById('sendEmailTo');
    }
    if (!sendEmailSubject) {
        sendEmailSubject = document.getElementById('sendEmailSubject');
    }
    if (!sendEmailContent) {
        sendEmailContent = document.getElementById('sendEmailContent');
    }
    if (!sendEmailError) {
        sendEmailError = document.getElementById('sendEmailError');
    }

    if (!sendEmailModal || !sendEmailUserId || !sendEmailTo || !sendEmailSubject || !sendEmailContent || !sendEmailError) {
        console.error('无法找到发送邮件模态框相关元素');
        return;
    }

    sendEmailUserId.value = userId;
    sendEmailTo.value = email;
    sendEmailSubject.value = '';
    sendEmailContent.value = '';
    sendEmailError.textContent = '';
    sendEmailModal.classList.remove('hidden');
};

// 提交发送邮件表单
async function submitSendEmail(e) {
    e.preventDefault();

    const userId = sendEmailUserId.value;
    const subject = sendEmailSubject.value.trim();
    const content = sendEmailContent.value.trim();

    if (!userId) {
        sendEmailError.textContent = '请从用户表格中选择要发送的用户';
        return;
    }
    if (!subject) {
        sendEmailError.textContent = '请输入主题';
        return;
    }
    if (!content) {
        sendEmailError.textContent = '请输入内容';
        return;
    }

    showLoading(confirmSendEmailButton);
    sendEmailError.textContent = '';

    try {
        const response = await fetch('/admin/users/send-email', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Admin-Password': currentAdminPassword
            },
            body: JSON.stringify({ userId, subject, content })
        });

        const result = await response.json();

        if (result.success) {
            showToast('邮件发送成功');
            sendEmailModal.classList.add('hidden');
        } else {
            sendEmailError.textContent = result.message || '发送失败';
        }
    } catch (error) {
        console.error('发送邮件时出错:', error);
        sendEmailError.textContent = '发送失败，请稍后重试';
    } finally {
        hideLoading(confirmSendEmailButton);
    }
}

// 打开编辑邮箱模态框
window.openEditEmailModal = function(userId, currentEmail) {
    // 确保DOM元素已初始化
    if (!editEmailModal) {
        editEmailModal = document.getElementById('editEmailModal');
    }

    if (!editEmailModal) {
        console.error('无法找到编辑邮箱模态框');
        return;
    }

    document.getElementById('editEmailUserId').value = userId;
    document.getElementById('editEmailOldEmail').value = currentEmail;
    document.getElementById('editEmailCurrent').value = currentEmail;
    document.getElementById('editEmailNew').value = currentEmail;
    document.getElementById('editEmailModalError').textContent = '';
    editEmailModal.classList.remove('hidden');
};

// 提交编辑邮箱表单
async function submitEditEmailForm(e) {
    e.preventDefault();

    const saveButton = document.getElementById('saveEditEmailButton');
    const userId = document.getElementById('editEmailUserId').value;
    const newEmail = document.getElementById('editEmailNew').value.trim();
    const errorElement = document.getElementById('editEmailModalError');

    if (!newEmail) {
        errorElement.textContent = '新邮箱不能为空';
        return;
    }

    showLoading(saveButton);

    try {
        const response = await fetch('/admin/users/update-email', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Admin-Password': currentAdminPassword
            },
            body: JSON.stringify({
                userId: userId,
                newEmail: newEmail
            })
        });

        const result = await response.json();

        if (result.success) {
            showToast('邮箱更新成功');
            editEmailModal.classList.add('hidden');
            fetchUsers();
        } else {
            errorElement.textContent = result.message || '更新失败';
        }
    } catch (error) {
        console.error('更新邮箱时出错:', error);
        errorElement.textContent = '更新失败，请稍后重试';
    } finally {
        hideLoading(saveButton);
    }
}

// 初始化用户管理模块
document.addEventListener('DOMContentLoaded', function() {
    initUsersModule();
});

// 当用户管理tab被点击时调用
function onUsersTabClick() {
    // 确保DOM元素已初始化
    if (!usersContent) {
        initDOMElements();
    }

    // 获取用户内容元素（如果还没有获取到）
    if (!usersContent) {
        usersContent = document.getElementById('usersContent');
    }

    // 获取用户tab元素
    const usersTabEl = document.getElementById('usersTab');
    if (!usersTabEl) {
        console.error('无法找到用户管理tab元素');
        return;
    }

    // 切换tab状态 - 更精确地找到nav元素中的所有tab
    const navElement = usersTabEl.closest('nav');
    if (!navElement) {
        console.error('无法找到tab导航容器');
        return;
    }

    // 获取nav中的所有tab按钮
    const allTabsInNav = navElement.querySelectorAll('.tab-button');
    const allContents = navElement.closest('section').querySelectorAll('.tab-content');

    // 移除所有tab的active状态
    allTabsInNav.forEach(tab => {
        tab.classList.remove('active');
    });

    // 隐藏所有内容区域
    allContents.forEach(content => {
        content.classList.add('hidden');
    });

    // 添加active状态到用户管理tab
    usersTabEl.classList.add('active');

    // 显示用户管理内容
    if (usersContent) {
        usersContent.classList.remove('hidden');
    }

    // 加载用户数据
    fetchUsers();
}
