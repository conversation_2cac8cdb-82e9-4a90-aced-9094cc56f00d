<!-- 用户管理Tab内容 -->
<div id="usersContent" class="tab-content hidden flex-1 flex flex-col min-h-0">
    <div class="flex flex-col sm:flex-row justify-between items-center mb-4 gap-4 flex-shrink-0">
        <h2 class="text-2xl sm:text-3xl font-semibold text-slate-700">用户管理</h2>
        <div class="flex gap-2">
            <button id="createUserButton"
                class="flex items-center justify-center bg-green-500 hover:bg-green-600 text-white font-semibold py-2.5 px-5 rounded-lg shadow-md hover:shadow-lg transition-all duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
                </svg>
                创建用户
            </button>
            <input type="text" id="userSearchInput" placeholder="搜索用户名/邮箱/QQ"
                class="px-3 py-2 border border-gray-300 rounded-md w-64">
            <button id="refreshUsersButton"
                class="flex items-center justify-center bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2.5 px-5 rounded-lg shadow-md hover:shadow-lg transition-all duration-300">
                刷新
            </button>
            <button id="openSendEmailModalButton"
                class="flex items-center justify-center bg-gray-700 hover:bg-gray-800 text-white font-semibold py-2.5 px-5 rounded-lg shadow-md hover:shadow-lg transition-all duration-300">
                发送邮件
            </button>
        </div>
    </div>

    <div id="usersLoadingMessage" class="text-center py-10 text-gray-500 hidden flex-shrink-0">
        <div class="admin-loader inline-block mr-2"></div> 加载中，请稍候...
    </div>

    <div class="overflow-x-auto overflow-y-auto shadow-md rounded-lg flex-1 min-h-0">
        <table id="usersTable" class="admin-table-container responsive-table">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                        ID
                    </th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                        用户名
                    </th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                        邮箱
                    </th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                        QQ
                    </th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                        注册时间
                    </th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                        绑定数
                    </th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                        状态
                    </th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                        操作
                    </th>
                </tr>
            </thead>
            <tbody id="users-table-body" class="bg-white divide-y divide-gray-200">
            </tbody>
        </table>
    </div>
</div>

<!-- 创建用户模态框 -->
<div id="createUserModal"
    class="fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center p-4 z-50 hidden"
    aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div
        class="modal-content-admin bg-white rounded-xl shadow-2xl overflow-hidden max-w-lg w-full p-6 sm:p-8 transform transition-all">
        <div class="flex justify-between items-center mb-6">
            <h3 id="createUserModalTitle" class="text-2xl font-semibold text-slate-800">创建新用户</h3>
            <button type="button" id="closeCreateUserModal"
                class="text-gray-400 hover:text-gray-600 transition-colors">
                <span class="sr-only">关闭</span>
                <svg class="h-7 w-7" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>

        <form id="createUserForm" class="space-y-5">
            <div>
                <label for="createUsername" class="block text-sm font-medium text-gray-700 mb-1">用户名 *</label>
                <input type="text" id="createUsername" required placeholder="请输入用户名"
                    class="mt-1 block w-full px-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors">
            </div>

            <div>
                <label for="createEmail" class="block text-sm font-medium text-gray-700 mb-1">邮箱 *</label>
                <input type="email" id="createEmail" required placeholder="请输入邮箱地址"
                    class="mt-1 block w-full px-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors">
            </div>

            <div>
                <label for="createPassword" class="block text-sm font-medium text-gray-700 mb-1">密码 *</label>
                <input type="password" id="createPassword" required placeholder="请输入密码（至少6位）"
                    class="mt-1 block w-full px-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors">
            </div>

            <div>
                <label for="createQQ" class="block text-sm font-medium text-gray-700 mb-1">QQ号 *</label>
                <input type="text" id="createQQ" required placeholder="请输入QQ号"
                    class="mt-1 block w-full px-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors">
            </div>

            <div id="createUserModalError" class="text-sm text-red-600 min-h-[1.25rem]" aria-live="polite"></div>

            <div class="flex flex-col sm:flex-row-reverse sm:space-x-reverse sm:space-x-3 pt-3 gap-3 sm:gap-0">
                <button type="submit" id="saveCreateUserButton"
                    class="w-full sm:w-auto flex justify-center items-center py-2.5 px-6 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-transform transform hover:scale-105">
                    <span class="button-text">创建用户</span>
                    <span class="admin-loader hidden ml-2"></span>
                </button>
                <button type="button" id="cancelCreateUserButton"
                    class="w-full sm:w-auto justify-center py-2.5 px-6 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors">
                    取消
                </button>
            </div>
        </form>
    </div>
</div>

<!-- 编辑用户模态框 -->
<div id="userEditModal"
    class="fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center p-4 z-50 hidden"
    aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="modal-content-admin bg-white rounded-xl shadow-2xl overflow-hidden max-w-lg w-full p-6 sm:p-8 transform transition-all">
        <div class="flex justify-between items-center mb-6">
            <h3 id="userEditModalTitle" class="text-2xl font-semibold text-slate-800">编辑用户</h3>
            <button type="button" id="closeUserEditModal"
                class="text-gray-400 hover:text-gray-600 transition-colors">
                <span class="sr-only">关闭</span>
                <svg class="h-7 w-7" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>

        <form id="userEditForm" class="space-y-5">
            <input type="hidden" id="editUserId">

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                <input type="text" id="editUsername" disabled
                    class="mt-1 block w-full px-3 py-2.5 border border-gray-300 rounded-md shadow-sm bg-gray-100 sm:text-sm">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">邮箱</label>
                <input type="email" id="editEmail" disabled
                    class="mt-1 block w-full px-3 py-2.5 border border-gray-300 rounded-md shadow-sm bg-gray-100 sm:text-sm">
            </div>

            <div>
                <label for="editQQ" class="block text-sm font-medium text-gray-700 mb-1">QQ号</label>
                <input type="text" id="editQQ" placeholder="输入新的QQ号"
                    class="mt-1 block w-full px-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors">
            </div>

            <div>
                <label for="editPassword" class="block text-sm font-medium text-gray-700 mb-1">新密码（留空不修改）</label>
                <input type="password" id="editPassword" placeholder="输入新密码"
                    class="mt-1 block w-full px-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors">
            </div>

            <div id="userEditError" class="text-sm text-red-600 min-h-[1.25rem]" aria-live="polite"></div>

            <div class="flex flex-col sm:flex-row-reverse sm:space-x-reverse sm:space-x-3 pt-3 gap-3 sm:gap-0">
                <button type="submit" id="saveUserButton"
                    class="w-full sm:w-auto flex justify-center items-center py-2.5 px-6 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-transform transform hover:scale-105">
                    <span class="button-text">保存</span>
                    <span class="admin-loader hidden ml-2"></span>
                </button>
                <button type="button" id="cancelUserEditButton"
                    class="w-full sm:w-auto justify-center py-2.5 px-6 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors">
                    取消
                </button>
            </div>
        </form>
    </div>
</div>

<!-- 封禁用户模态框 -->
<div id="banUserModal"
    class="fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center p-4 z-50 hidden"
    aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="modal-content-admin bg-white rounded-xl shadow-2xl overflow-hidden max-w-lg w-full p-6 sm:p-8 transform transition-all">
        <div class="flex justify-between items-center mb-6">
            <h3 id="banUserModalTitle" class="text-2xl font-semibold text-slate-800">封禁用户</h3>
            <button type="button" id="closeBanUserModal"
                class="text-gray-400 hover:text-gray-600 transition-colors">
                <span class="sr-only">关闭</span>
                <svg class="h-7 w-7" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>

        <form id="banUserForm" class="space-y-5">
            <input type="hidden" id="banUserId">

            <div>
                <p class="text-sm text-gray-600 mb-4">确定要封禁用户 <span id="banUsername" class="font-semibold"></span> 吗？</p>
                <label for="banReason" class="block text-sm font-medium text-gray-700 mb-1">封禁原因</label>
                <textarea id="banReason" rows="3" required
                    placeholder="请输入封禁原因"
                    class="mt-1 block w-full px-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors"></textarea>
            </div>

            <div id="banUserError" class="text-sm text-red-600 min-h-[1.25rem]" aria-live="polite"></div>

            <div class="flex flex-col sm:flex-row-reverse sm:space-x-reverse sm:space-x-3 pt-3 gap-3 sm:gap-0">
                <button type="submit" id="confirmBanButton"
                    class="w-full sm:w-auto flex justify-center items-center py-2.5 px-6 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-transform transform hover:scale-105">
                    <span class="button-text">确认封禁</span>
                    <span class="admin-loader hidden ml-2"></span>
                </button>
                <button type="button" id="cancelBanButton"
                    class="w-full sm:w-auto justify-center py-2.5 px-6 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors">
                    取消
                </button>
            </div>
        </form>
    </div>
</div>

<!-- 发送邮件模态框 -->
<div id="sendEmailModal" class="fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center p-4 z-50 hidden" role="dialog" aria-modal="true">
    <div class="modal-content-admin bg-white rounded-xl shadow-2xl overflow-hidden max-w-lg w-full p-6 sm:p-8 transform transition-all">
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-2xl font-semibold text-slate-800">发送邮件</h3>
            <button type="button" id="closeSendEmailModal" class="text-gray-400 hover:text-gray-600 transition-colors" aria-label="关闭">
                <svg class="h-7 w-7" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg>
            </button>
        </div>
        <form id="sendEmailForm" class="space-y-4">
            <input type="hidden" id="sendEmailUserId">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">收件人</label>
                <input type="text" id="sendEmailTo" disabled class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100" />
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">主题</label>
                <input type="text" id="sendEmailSubject" placeholder="请输入主题" class="w-full px-3 py-2 border border-gray-300 rounded-md" />
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">内容</label>
                <textarea id="sendEmailContent" rows="6" placeholder="请输入正文内容（支持换行）" class="w-full px-3 py-2 border border-gray-300 rounded-md resize-none"></textarea>
            </div>
            <div id="sendEmailError" class="text-sm text-red-600 min-h-[1.25rem]"></div>
            <div class="flex justify-end gap-3 pt-2">
                <button type="button" id="cancelSendEmailButton" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">取消</button>
                <button type="submit" id="confirmSendEmailButton" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">发送</button>
            </div>
        </form>
    </div>
</div>

<!-- 编辑邮箱模态框 -->
<div id="editEmailModal"
    class="fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center p-4 z-50 hidden"
    aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div
        class="modal-content-admin bg-white rounded-xl shadow-2xl overflow-hidden max-w-lg w-full p-6 sm:p-8 transform transition-all">
        <div class="flex justify-between items-center mb-6">
            <h3 id="editEmailModalTitle" class="text-2xl font-semibold text-slate-800">编辑用户邮箱</h3>
            <button type="button" id="closeEditEmailModal"
                class="text-gray-400 hover:text-gray-600 transition-colors">
                <span class="sr-only">关闭</span>
                <svg class="h-7 w-7" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>

        <form id="editEmailForm" class="space-y-5">
            <input type="hidden" id="editEmailUserId">
            <input type="hidden" id="editEmailOldEmail">

            <div>
                <label for="editEmailCurrent" class="block text-sm font-medium text-gray-700 mb-1">当前邮箱</label>
                <input type="email" id="editEmailCurrent" disabled class="mt-1 block w-full px-3 py-2.5 border border-gray-300 rounded-md bg-gray-100">
            </div>

            <div>
                <label for="editEmailNew" class="block text-sm font-medium text-gray-700 mb-1">新邮箱 *</label>
                <input type="email" id="editEmailNew" required placeholder="请输入新邮箱地址"
                    class="mt-1 block w-full px-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors">
            </div>

            <div id="editEmailModalError" class="text-sm text-red-600 min-h-[1.25rem]" aria-live="polite"></div>

            <div class="flex flex-col sm:flex-row-reverse sm:space-x-reverse sm:space-x-3 pt-3 gap-3 sm:gap-0">
                <button type="submit" id="saveEditEmailButton"
                    class="w-full sm:w-auto flex justify-center items-center py-2.5 px-6 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-transform transform hover:scale-105">
                    <span class="button-text">保存</span>
                    <span class="admin-loader hidden ml-2"></span>
                </button>
                <button type="button" id="cancelEditEmailButton"
                    class="w-full sm:w-auto justify-center py-2.5 px-6 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors">
                    取消
                </button>
            </div>
        </form>
    </div>
</div>
