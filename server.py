import os
from flask import Flask, send_from_directory, jsonify
from flask_cors import CORS
from dotenv import load_dotenv

# Import configuration and database
from config import PORT, DB_PATH, logger, Database

# Import route registrars
from frontend import register_frontend_routes
from backend import register_backend_routes
from bot_api import register_bot_api_routes

# Load environment variables
load_dotenv()

# Create Flask app
app = Flask(__name__, static_folder='public')
CORS(app)

# Initialize database
db = Database(DB_PATH)
# 确保数据库连接立即建立
if not db.connect():
    logger.error('数据库连接失败，服务器无法启动')
    exit(1)
db.create_tables()

# --- Email queue and background sender ---
from threading import Thread
from queue import Queue, Empty
from typing import Optional
import time as _time

def _send_email_smtp_util(db_inst: Database, to_email: str, subject: str, html_body: str):
    import smtplib
    from email.mime.text import MIMEText
    from email.header import Header
    from email.utils import formataddr, make_msgid, formatdate
    host = db_inst.get_setting('smtp_host', '')
    port = int(db_inst.get_setting('smtp_port', '587') or '587')
    user = db_inst.get_setting('smtp_user', '')
    pwd = db_inst.get_setting('smtp_pass', '')
    use_tls = str(db_inst.get_setting('smtp_tls', '1')) in ('1','true','True','yes','on')
    from_addr = db_inst.get_setting('smtp_from', '') or user
    from_name = db_inst.get_setting('smtp_from_name', '') or db_inst.get_setting('site_title', '')
    if not host or not user or not pwd or not from_addr:
        raise RuntimeError('SMTP配置不完整')
    msg = MIMEText(html_body or '', 'html', 'utf-8')
    msg['Subject'] = str(Header(subject or '', 'utf-8'))
    msg['From'] = formataddr((str(Header(from_name, 'utf-8')), from_addr)) if from_name else from_addr
    msg['To'] = to_email
    msg['Date'] = formatdate(localtime=True)
    msg['Message-ID'] = make_msgid()
    attempts = ['starttls', 'ssl'] if use_tls else (['ssl'] if port == 465 else ['smtp', 'starttls', 'ssl'])
    last_error = None
    for method in attempts:
        try:
            if method == 'starttls':
                server = smtplib.SMTP(host, port, timeout=10)
                server.ehlo(); server.starttls(); server.ehlo()
            elif method == 'ssl':
                server = smtplib.SMTP_SSL(host, port, timeout=10); server.ehlo()
            else:
                server = smtplib.SMTP(host, port, timeout=10); server.ehlo()
            server.login(user, pwd)
            server.sendmail(from_addr, [to_email], msg.as_string())
            server.quit()
            return
        except Exception as e2:
            last_error = e2
    raise last_error or RuntimeError('发送失败')

class _EmailTask:
    def __init__(self, to_email: str, subject: str, html: str, meta: Optional[dict] = None):
        self.to_email = to_email
        self.subject = subject
        self.html = html
        self.meta = meta or {}

def _email_worker_loop(app_obj, db_inst: Database, q: Queue):
    while True:
        try:
            task: _EmailTask = q.get(timeout=1)
        except Empty:
            continue
        try:
            _send_email_smtp_util(db_inst, task.to_email, task.subject, task.html)
            try:
                from config import append_system_log
                actor = task.meta.get('actor') or 'auto'
                module_action = task.meta.get('action') or '发送邮件'
                result = f"to={task.to_email} subject={task.subject}"
                append_system_log(actor, module_action, f"成功 {result}")
            except Exception:
                pass
        except Exception as e:
            logger.warn('后台发送邮件失败', e)
            try:
                from config import append_system_log
                actor = (task.meta.get('actor') if task else 'auto') or 'auto'
                module_action = (task.meta.get('action') if task else '发送邮件') or '发送邮件'
                append_system_log(actor, module_action, f"失败 to={task.to_email} subject={task.subject}")
            except Exception:
                pass
        finally:
            q.task_done()

_email_queue = Queue()
def _enqueue_email(to_email: str, subject: str, html: str, meta: Optional[dict] = None):
    _email_queue.put(_EmailTask(to_email, subject, html, meta))

app.enqueue_email = _enqueue_email
_t = Thread(target=_email_worker_loop, args=(app, db, _email_queue), daemon=True)
_t.start()

# Register all routes first (API should take precedence over catch-all static)
register_frontend_routes(app, db)
register_backend_routes(app, db)
register_bot_api_routes(app, db)

# Serve static files
@app.route('/')
def index():
    return send_from_directory('public', 'index.html')

@app.route('/<path:path>')
def serve_static(path):
    return send_from_directory('public', path)

# Error handlers
@app.errorhandler(404)
def not_found(error):
    return jsonify({
        'success': False,
        'message': '请求的资源未找到'
    }), 404

@app.errorhandler(500)
def internal_error(error):
    logger.error('服务器内部错误:', error)
    return jsonify({
        'success': False,
        'message': '服务器内部错误'
    }), 500

# Initialize and run
if __name__ == '__main__':
    logger.info(f'服务器正在运行于 http://localhost:{PORT}')
    logger.info(f'用户前端页面位于 http://localhost:{PORT}/index.html')
    logger.info(f'后台管理页面位于 http://localhost:{PORT}/admin.html')
    app.run(host='0.0.0.0', port=PORT, debug=False)
