# 用户端API测试工具

## 概述

这是一个专门用于测试用户端API的可视化工具，主要用于测试以下功能：
- **功能黑名单检查** - 测试群组的功能黑名单状态检查
- **功能白名单检查** - 测试群组的功能白名单状态检查  
- **按次付费功能使用** - 测试按次付费功能的使用和兑换码兑换

## 快速开始

### 1. 环境要求

- Python 3.7+
- 主管理端服务已运行在 `http://localhost:4000`

### 2. 安装依赖

```bash
cd test_api
pip install -r requirements.txt
```

### 3. 启动测试工具

```bash
python app.py
```

启动后会看到以下信息：
```
============================================================
用户端API测试工具启动
监听端口: 9520
目标服务器: http://localhost:4000
访问地址: http://localhost:9520
API文档: http://localhost:9520/api/docs
============================================================
```

### 4. 访问测试工具

打开浏览器访问：`http://localhost:9520`

## 功能说明

### 基础配置

在使用测试工具前，需要配置：
- **目标服务器**：主管理端的服务器地址（默认：http://localhost:4000）

### 功能检查测试

#### 1. 检查功能黑名单
- **API端点**：`POST /api/check-feature-blacklist`
- **操作**：填写群组ID和请求标识符后点击"检查黑名单"
- **必填字段**：群组ID、请求标识符
- **权限**：无需认证

#### 2. 检查功能白名单
- **API端点**：`POST /api/check-feature-whitelist`
- **操作**：填写群组ID和请求标识符后点击"检查白名单"
- **必填字段**：群组ID、请求标识符
- **权限**：无需认证

### 按次付费功能测试

#### 1. 使用按次付费功能
- **API端点**：`POST /api/pay-per-use/use`
- **操作**：填写群组ID、功能代码等信息后点击"使用功能"
- **必填字段**：群组ID、功能代码
- **可选字段**：请求备注
- **权限**：无需认证

#### 2. 兑换兑换码
- **API端点**：`POST /api/pay-per-use/redeem`
- **操作**：填写群组ID和兑换码后点击"兑换兑换码"
- **必填字段**：群组ID、兑换码
- **权限**：无需认证

## API接口文档

详细的API接口文档请访问：`http://localhost:9520/api/docs`

文档包含：
- 完整的API端点列表
- 请求参数说明
- 响应格式示例
- 多种编程语言的示例代码（Python、JavaScript、cURL）

## 使用技巧

### 1. 键盘快捷键
- `Ctrl + Enter`：执行当前选项卡的主要操作
- `Ctrl + L`：清空测试结果
- `Ctrl + Shift + C`：复制测试结果

### 2. 便捷功能
- **填充示例数据**：点击"填充示例数据"按钮自动填充测试数据
- **导出结果**：点击"导出结果"按钮将测试结果导出为文本文件
- **清空结果**：清空结果显示区域
- **复制结果**：将结果复制到剪贴板

### 3. 结果解读
测试结果会显示：
- **HTTP状态码**：响应的HTTP状态码
- **请求信息**：完整的请求URL、方法、头部、参数
- **响应内容**：服务器返回的完整响应数据
- **时间戳**：请求发送的时间

## 常见使用场景

### 场景1：测试功能黑名单检查
1. 切换到"功能检查"选项卡
2. 填写黑名单检查信息：
   - 群组ID：`123456789`
   - 请求标识符：`generate_image`
3. 点击"检查黑名单"
4. 验证返回的黑名单状态是否正确

### 场景2：测试按次付费功能使用
1. 切换到"按次付费"选项卡
2. 填写使用信息：
   - 群组ID：`123456789`
   - 功能代码：`translation`
   - 请求备注：`测试翻译功能`
3. 点击"使用功能"
4. 检查是否正确扣费

### 场景3：测试兑换码兑换
1. 在"按次付费"选项卡中
2. 填写兑换信息：
   - 群组ID：`123456789`
   - 兑换码：`TEST-CODE-123`
3. 点击"兑换兑换码"
4. 验证兑换是否成功

## 故障排除

### 常见问题

#### 1. 连接失败
**错误信息**：`请求错误: ConnectionError`
**解决方案**：
- 确认主管理端服务已启动
- 检查目标服务器地址是否正确
- 确认端口4000未被防火墙阻止

#### 3. 参数错误
**错误信息**：`请求失败: 参数不完整`
**解决方案**：
- 检查必填字段是否已填写
- 确认数据格式是否正确（如数字字段不能输入文字）

### 调试技巧

1. **查看完整请求信息**：测试结果会显示完整的请求信息，可用于调试
2. **对比API文档**：确认请求格式是否与API文档一致
3. **逐步测试**：先测试简单的功能检查，再测试按次付费功能
4. **检查日志**：查看主管理端的日志输出

## 配置说明

### 修改目标服务器

如果主管理端不在默认地址，可以通过两种方式修改：

#### 方法1：在界面中修改
在"基础配置"区域修改"目标服务器"字段

#### 方法2：修改代码
编辑 `app.py` 文件，修改 `TARGET_SERVER` 变量：
```python
TARGET_SERVER = "http://your-server:port"
```

### 自定义端口

如果需要修改测试工具的监听端口，编辑 `app.py` 文件的最后一行：
```python
app.run(host='0.0.0.0', port=9520, debug=True)  # 修改port参数
```

## 开发者接口

### 为其他开发者提供的对接信息

#### 主要API端点

| 功能模块 | 端点路径 | 方法 | 说明 |
|---------|---------|------|------|
| 功能检查 | `/api/check-feature-blacklist` | POST | 检查黑名单 |
| 功能检查 | `/api/check-feature-whitelist` | POST | 检查白名单 |
| 功能使用 | `/api/pay-per-use/use` | POST | 使用功能 |
| 功能使用 | `/api/pay-per-use/redeem` | POST | 兑换兑换码 |

#### 认证方式

**用户端API**：无需认证，直接访问

#### 数据格式

所有API均使用JSON格式：
```
Content-Type: application/json
```

#### 响应格式

标准响应格式：
```json
{
  "success": true|false,
  "message": "操作结果信息",
  "data": {}  // 具体数据（可选）
}
```

## 版本信息

- **版本**：1.0.0
- **Python版本要求**：3.7+
- **依赖框架**：Flask 2.3.3
- **开发时间**：2024年

## 支持与反馈

如有问题或建议，请：
1. 检查本文档的故障排除部分
2. 查看API文档获取更详细的接口说明
3. 检查主管理端的日志输出

---

**注意**：此工具仅用于开发和测试环境，请勿在生产环境中使用。使用前请确保已备份重要数据。
