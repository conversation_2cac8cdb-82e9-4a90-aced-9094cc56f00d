#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
管理端API测试工具
监听端口：9520
用于测试功能黑名单、功能白名单、按次付费功能的API接口
"""

from flask import Flask, render_template, request, jsonify
from flask_cors import CORS
import requests
import json
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# 配置目标服务器地址
TARGET_SERVER = "http://localhost:4000"  # 主管理端服务器地址

@app.route('/config/update', methods=['POST'])
def update_config():
    """更新配置"""
    try:
        data = request.json
        new_target = data.get('target_server', '').strip()
        if new_target:
            global TARGET_SERVER
            TARGET_SERVER = new_target
            return jsonify({'success': True, 'message': '配置已更新', 'target_server': TARGET_SERVER})
        else:
            return jsonify({'success': False, 'message': '目标服务器地址不能为空'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/')
def index():
    """主页面"""
    return render_template('index.html')



@app.route('/test/pay-per-use/use', methods=['POST'])
def test_pay_per_use_use():
    """测试使用按次付费功能"""
    try:
        data = request.json
        use_data = {
            'groupId': data.get('group_id', ''),
            'featureCode': data.get('feature_code', ''),
            'requestNote': data.get('request_note', '')
        }
        
        headers = {'Content-Type': 'application/json'}
        
        response = requests.post(
            f"{TARGET_SERVER}/api/pay-per-use/use", 
            json=use_data,
            headers=headers
        )
        
        return jsonify({
            'success': True,
            'status_code': response.status_code,
            'response': response.json(),
            'request_url': f"{TARGET_SERVER}/api/pay-per-use/use",
            'request_method': 'POST',
            'request_headers': headers,
            'request_body': use_data
        })
    except Exception as e:
        logger.error(f"测试按次付费功能使用失败: {e}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/test/pay-per-use/redeem', methods=['POST'])
def test_pay_per_use_redeem():
    """测试兑换按次付费兑换码"""
    try:
        data = request.json
        redeem_data = {
            'groupId': data.get('group_id', ''),
            'code': data.get('code', '')
        }
        
        headers = {'Content-Type': 'application/json'}
        
        response = requests.post(
            f"{TARGET_SERVER}/api/pay-per-use/redeem", 
            json=redeem_data,
            headers=headers
        )
        
        return jsonify({
            'success': True,
            'status_code': response.status_code,
            'response': response.json(),
            'request_url': f"{TARGET_SERVER}/api/pay-per-use/redeem",
            'request_method': 'POST',
            'request_headers': headers,
            'request_body': redeem_data
        })
    except Exception as e:
        logger.error(f"测试按次付费兑换码兑换失败: {e}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/test/check-feature-blacklist', methods=['POST'])
def test_check_feature_blacklist():
    """测试检查功能黑名单"""
    try:
        data = request.json
        check_data = {
            'groupId': data.get('group_id', ''),
            'requestIdentifier': data.get('request_identifier', '')
        }
        
        headers = {'Content-Type': 'application/json'}
        
        response = requests.post(
            f"{TARGET_SERVER}/api/check-feature-blacklist", 
            json=check_data,
            headers=headers
        )
        
        return jsonify({
            'success': True,
            'status_code': response.status_code,
            'response': response.json(),
            'request_url': f"{TARGET_SERVER}/api/check-feature-blacklist",
            'request_method': 'POST',
            'request_headers': headers,
            'request_body': check_data
        })
    except Exception as e:
        logger.error(f"测试功能黑名单检查失败: {e}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/test/check-feature-whitelist', methods=['POST'])
def test_check_feature_whitelist():
    """测试检查功能白名单"""
    try:
        data = request.json
        check_data = {
            'groupId': data.get('group_id', ''),
            'requestIdentifier': data.get('request_identifier', '')
        }
        
        headers = {'Content-Type': 'application/json'}
        
        response = requests.post(
            f"{TARGET_SERVER}/api/check-feature-whitelist", 
            json=check_data,
            headers=headers
        )
        
        return jsonify({
            'success': True,
            'status_code': response.status_code,
            'response': response.json(),
            'request_url': f"{TARGET_SERVER}/api/check-feature-whitelist",
            'request_method': 'POST',
            'request_headers': headers,
            'request_body': check_data
        })
    except Exception as e:
        logger.error(f"测试功能白名单检查失败: {e}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/test/group-bots-info/<group_id>', methods=['GET'])
def test_group_bots_info(group_id):
    """测试群组机器人详细信息API"""
    try:
        response = requests.get(f"{TARGET_SERVER}/api/group-bots-info/{group_id}")
        
        return jsonify({
            'success': True,
            'status_code': response.status_code,
            'response': response.json(),
            'request_url': f"{TARGET_SERVER}/api/group-bots-info/{group_id}",
            'request_method': 'GET'
        })
    except Exception as e:
        logger.error(f"测试群组机器人信息失败: {e}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/test/group-authorization-summary/<group_id>', methods=['GET'])
def test_group_authorization_summary(group_id):
    """测试群组授权摘要API"""
    try:
        response = requests.get(f"{TARGET_SERVER}/api/group-authorization-summary/{group_id}")
        
        return jsonify({
            'success': True,
            'status_code': response.status_code,
            'response': response.json(),
            'request_url': f"{TARGET_SERVER}/api/group-authorization-summary/{group_id}",
            'request_method': 'GET'
        })
    except Exception as e:
        logger.error(f"测试群组授权摘要失败: {e}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/test/check-authorizations', methods=['POST'])
def test_check_authorizations():
    """测试批量检查群聊授权状态"""
    try:
        data = request.json
        group_ids = data.get('group_ids', [])
        
        if isinstance(group_ids, str):
            group_ids = [id.strip() for id in group_ids.split(',') if id.strip()]
        
        check_data = {'groupIds': group_ids}
        headers = {'Content-Type': 'application/json'}
        
        response = requests.post(
            f"{TARGET_SERVER}/api/check-authorizations", 
            json=check_data,
            headers=headers
        )
        
        return jsonify({
            'success': True,
            'status_code': response.status_code,
            'response': response.json(),
            'request_url': f"{TARGET_SERVER}/api/check-authorizations",
            'request_method': 'POST',
            'request_headers': headers,
            'request_body': check_data
        })
    except Exception as e:
        logger.error(f"测试群聊授权检查失败: {e}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/test/check-authorization-with-user', methods=['POST'])
def test_check_authorization_with_user():
    """测试检查用户在群聊的授权"""
    try:
        data = request.json
        check_data = {
            'groupId': data.get('group_id', ''),
            'userId': data.get('user_qq', '')
        }
        
        headers = {'Content-Type': 'application/json'}
        
        response = requests.post(
            f"{TARGET_SERVER}/api/check-authorization-with-user", 
            json=check_data,
            headers=headers
        )
        
        return jsonify({
            'success': True,
            'status_code': response.status_code,
            'response': response.json(),
            'request_url': f"{TARGET_SERVER}/api/check-authorization-with-user",
            'request_method': 'POST',
            'request_headers': headers,
            'request_body': check_data
        })
    except Exception as e:
        logger.error(f"测试用户群聊授权检查失败: {e}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/test/check-user-by-qq', methods=['POST'])
def test_check_user_by_qq():
    """测试检查QQ用户是否注册"""
    try:
        data = request.json
        check_data = {'qq': data.get('qq', '')}
        
        headers = {'Content-Type': 'application/json'}
        
        response = requests.post(
            f"{TARGET_SERVER}/api/check-user-by-qq", 
            json=check_data,
            headers=headers
        )
        
        return jsonify({
            'success': True,
            'status_code': response.status_code,
            'response': response.json(),
            'request_url': f"{TARGET_SERVER}/api/check-user-by-qq",
            'request_method': 'POST',
            'request_headers': headers,
            'request_body': check_data
        })
    except Exception as e:
        logger.error(f"测试QQ用户检查失败: {e}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/test/authorized-groups', methods=['GET'])
def test_get_authorized_groups():
    """测试获取已授权群聊列表"""
    try:
        sku_id = request.args.get('sku_id', '')
        
        params = {}
        if sku_id:
            params['skuId'] = sku_id
            
        response = requests.get(
            f"{TARGET_SERVER}/api/authorized-groups",
            params=params
        )
        
        return jsonify({
            'success': True,
            'status_code': response.status_code,
            'response': response.json(),
            'request_url': f"{TARGET_SERVER}/api/authorized-groups",
            'request_method': 'GET',
            'request_params': params
        })
    except Exception as e:
        logger.error(f"测试获取已授权群聊失败: {e}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/test/simple/feature-blacklist/<group_id>', methods=['GET'])
def test_simple_feature_blacklist(group_id):
    """测试简化版功能黑名单API"""
    try:
        response = requests.get(f"{TARGET_SERVER}/api/simple/feature-blacklist/{group_id}")
        
        return jsonify({
            'success': True,
            'status_code': response.status_code,
            'response': response.json(),
            'request_url': f"{TARGET_SERVER}/api/simple/feature-blacklist/{group_id}",
            'request_method': 'GET'
        })
    except Exception as e:
        logger.error(f"测试简化版功能黑名单失败: {e}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/test/simple/feature-whitelist/<group_id>', methods=['GET'])
def test_simple_feature_whitelist(group_id):
    """测试简化版功能白名单API"""
    try:
        response = requests.get(f"{TARGET_SERVER}/api/simple/feature-whitelist/{group_id}")
        
        return jsonify({
            'success': True,
            'status_code': response.status_code,
            'response': response.json(),
            'request_url': f"{TARGET_SERVER}/api/simple/feature-whitelist/{group_id}",
            'request_method': 'GET'
        })
    except Exception as e:
        logger.error(f"测试简化版功能白名单失败: {e}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/test/simple/authorization/<group_id>', methods=['GET'])
def test_simple_authorization(group_id):
    """测试简化版授权检查API"""
    try:
        response = requests.get(f"{TARGET_SERVER}/api/simple/authorization/{group_id}")
        
        return jsonify({
            'success': True,
            'status_code': response.status_code,
            'response': response.json(),
            'request_url': f"{TARGET_SERVER}/api/simple/authorization/{group_id}",
            'request_method': 'GET'
        })
    except Exception as e:
        logger.error(f"测试简化版授权检查失败: {e}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/test/simple/pay-per-use-balance/<group_id>', methods=['GET'])
def test_simple_pay_per_use_balance(group_id):
    """测试简化版按次付费余额API"""
    try:
        response = requests.get(f"{TARGET_SERVER}/api/simple/pay-per-use-balance/{group_id}")
        
        return jsonify({
            'success': True,
            'status_code': response.status_code,
            'response': response.json(),
            'request_url': f"{TARGET_SERVER}/api/simple/pay-per-use-balance/{group_id}",
            'request_method': 'GET'
        })
    except Exception as e:
        logger.error(f"测试简化版按次付费余额失败: {e}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/docs')
def api_docs():
    """API文档页面"""
    return render_template('api_docs.html')

if __name__ == '__main__':
    print("=" * 60)
    print("管理端API测试工具启动")
    print("监听端口: 9520")
    print("目标服务器: " + TARGET_SERVER)
    print("访问地址: http://localhost:9520")
    print("API文档: http://localhost:9520/api/docs")
    print("=" * 60)
    app.run(host='0.0.0.0', port=9520, debug=True)
