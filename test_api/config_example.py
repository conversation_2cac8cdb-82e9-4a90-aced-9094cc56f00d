#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件示例
复制此文件为 config.py 并根据需要修改配置
"""

# 目标服务器配置
TARGET_SERVER = "http://localhost:4000"  # 主管理端服务器地址

# 测试工具配置
TEST_TOOL_HOST = "0.0.0.0"  # 测试工具监听地址
TEST_TOOL_PORT = 9520       # 测试工具监听端口
DEBUG_MODE = True           # 调试模式

# 默认测试数据
DEFAULT_TEST_DATA = {
    "blacklist": {
        "displayName": "示例黑名单功能",
        "actualFeatures": ["feature1", "feature2"],
        "requestIdentifiers": ["test_identifier"],
        "allowedTiers": ["basic", "pro"]
    },
    "whitelist": {
        "displayName": "示例白名单功能", 
        "actualFeatures": ["premium_feature"],
        "requestIdentifiers": ["premium_identifier"],
        "allowedTiers": ["enterprise"]
    },
    "payperuse": {
        "featureCode": "test_feature",
        "displayName": "测试功能",
        "description": "这是一个测试功能",
        "apiEndpoint": "/api/pay-per-use/test_feature",
        "costPerUse": 1
    }
}

# 测试用群组ID
TEST_GROUP_ID = "123456789"

# 超时设置（秒）
REQUEST_TIMEOUT = 30

# 日志配置
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(levelname)s - %(message)s"
