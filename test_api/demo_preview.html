<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试工具预览</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        .feature-showcase {
            background: #f7fafc;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 25px;
            border-left: 4px solid #667eea;
        }
        .api-preview {
            background: #1a202c;
            color: #f7fafc;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 13px;
            margin: 10px 0;
            overflow-x: auto;
        }
        .highlight {
            background: #48bb78;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .feature-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
        }
        .feature-card h3 {
            color: #4a5568;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .icon {
            font-size: 1.5rem;
        }
        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-top: 20px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 用户端API测试工具</h1>
            <p>全新升级版本，专为开发者对接设计</p>
        </div>

        <div class="feature-showcase">
            <h2>✨ 新增功能：请求格式与返回示例</h2>
            <p>现在每个API测试功能都包含详细的请求格式和返回示例，让开发者一目了然！</p>
            
            <h3>🔍 示例：功能黑名单检查</h3>
            <div style="margin-bottom: 15px;">
                <span class="highlight">API路径</span>: POST /api/check-feature-blacklist
            </div>
            
            <h4>请求格式:</h4>
            <div class="api-preview">{
  "groupId": "群组ID",
  "requestIdentifier": "请求标识符"
}</div>

            <h4>返回示例:</h4>
            <div class="api-preview">{
  "success": true,
  "blacklisted": true,
  "blacklistedFeatures": ["text_generation", "ai_chat"],
  "allowedTiers": ["pro", "enterprise"]
}</div>
        </div>

        <div class="feature-list">
            <div class="feature-card">
                <h3><span class="icon">🛡️</span>功能检查</h3>
                <ul>
                    <li>✅ 功能黑名单检查</li>
                    <li>✅ 功能白名单检查</li>
                    <li>✅ 完整请求/响应示例</li>
                    <li>✅ 实时测试功能</li>
                </ul>
            </div>

            <div class="feature-card">
                <h3><span class="icon">💰</span>按次付费</h3>
                <ul>
                    <li>✅ 功能使用测试</li>
                    <li>✅ 兑换码兑换</li>
                    <li>✅ 详细API文档</li>
                    <li>✅ 错误处理示例</li>
                </ul>
            </div>

            <div class="feature-card">
                <h3><span class="icon">📱</span>用户体验</h3>
                <ul>
                    <li>✅ 清晰的API路径显示</li>
                    <li>✅ 标准化请求格式</li>
                    <li>✅ 实际返回数据示例</li>
                    <li>✅ 移动端适配</li>
                </ul>
            </div>

            <div class="feature-card">
                <h3><span class="icon">⚡</span>开发便利</h3>
                <ul>
                    <li>✅ 即时测试结果</li>
                    <li>✅ 配置可保存</li>
                    <li>✅ 示例数据填充</li>
                    <li>✅ 完整错误信息</li>
                </ul>
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px;">
            <p style="color: #718096; margin-bottom: 20px;">
                🎯 专为开发者对接设计的API测试工具
            </p>
            <a href="/api/docs" class="btn">查看完整API文档</a>
        </div>
    </div>
</body>
</html>
