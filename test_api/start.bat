@echo off
chcp 65001 >nul
echo ================================================================
echo 管理端API测试工具
echo ================================================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.7或更高版本
    pause
    exit /b 1
)

REM 检查是否在正确目录
if not exist "app.py" (
    echo 错误: 请在test_api目录下运行此脚本
    pause
    exit /b 1
)

REM 安装依赖
echo 检查依赖...
pip install -r requirements.txt >nul 2>&1

REM 启动应用
echo 启动测试工具...
echo.
python start.py

pause
