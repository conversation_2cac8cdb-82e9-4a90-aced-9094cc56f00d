#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
管理端API测试工具启动脚本
"""

import sys
import os

def check_requirements():
    """检查依赖是否已安装"""
    try:
        import flask
        import flask_cors
        import requests
        return True
    except ImportError as e:
        print(f"缺少依赖: {e}")
        print("请运行: pip install -r requirements.txt")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("管理端API测试工具")
    print("=" * 60)
    
    # 检查依赖
    if not check_requirements():
        return 1
    
    # 检查主管理端是否运行
    try:
        import requests
        response = requests.get("http://localhost:5000", timeout=3)
        print("✓ 检测到主管理端服务正在运行")
    except:
        print("⚠ 警告: 无法连接到主管理端服务 (http://localhost:5000)")
        print("  请确保主管理端服务已启动")
        print()
    
    # 启动测试工具
    try:
        from app import app
        print("启动测试工具...")
        app.run(host='0.0.0.0', port=9520, debug=True)
    except KeyboardInterrupt:
        print("\n测试工具已停止")
        return 0
    except Exception as e:
        print(f"启动失败: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
