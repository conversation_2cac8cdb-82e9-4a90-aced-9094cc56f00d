#!/bin/bash

echo "================================================================"
echo "管理端API测试工具"
echo "================================================================"
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    echo "错误: 未找到Python，请先安装Python 3.7或更高版本"
    exit 1
fi

# 确定Python命令
if command -v python3 &> /dev/null; then
    PYTHON_CMD=python3
else
    PYTHON_CMD=python
fi

# 检查是否在正确目录
if [ ! -f "app.py" ]; then
    echo "错误: 请在test_api目录下运行此脚本"
    exit 1
fi

# 安装依赖
echo "检查依赖..."
$PYTHON_CMD -m pip install -r requirements.txt > /dev/null 2>&1

# 启动应用
echo "启动测试工具..."
echo
$PYTHON_CMD start.py
