/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    text-align: center;
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.header h1 {
    font-size: 2.5rem;
    color: #4a5568;
    margin-bottom: 10px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.subtitle {
    font-size: 1.1rem;
    color: #718096;
}

/* 配置面板样式 */
.config-panel {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 12px;
    margin-bottom: 30px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.config-panel h2 {
    color: #4a5568;
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.config-row {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    gap: 15px;
}

.config-row label {
    font-weight: 600;
    min-width: 120px;
    color: #4a5568;
}

.config-row input {
    flex: 1;
    padding: 10px 15px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.config-row input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 选项卡样式 */
.tabs {
    display: flex;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 8px;
    margin-bottom: 30px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    overflow-x: auto;
}

.tab-button {
    flex: 1;
    padding: 12px 20px;
    border: none;
    background: transparent;
    color: #718096;
    font-weight: 600;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    min-width: 120px;
}

.tab-button:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.tab-button.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

/* 子选项卡样式 */
.sub-tabs {
    display: flex;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 8px;
    padding: 6px;
    margin-bottom: 20px;
    gap: 4px;
}

.sub-tab-button {
    flex: 1;
    padding: 8px 15px;
    border: none;
    background: transparent;
    color: #718096;
    font-weight: 500;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.sub-tab-button:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.sub-tab-button.active {
    background: white;
    color: #4a5568;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 内容面板样式 */
.tab-content {
    display: none;
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.tab-content.active {
    display: block;
}

.sub-tab-content {
    display: none;
}

.sub-tab-content.active {
    display: block;
}

.tab-content h2 {
    color: #4a5568;
    margin-bottom: 25px;
    font-size: 1.8rem;
    border-bottom: 3px solid #e2e8f0;
    padding-bottom: 10px;
}

/* 操作区域样式 */
.operation-section {
    background: #f7fafc;
    padding: 25px;
    border-radius: 10px;
    margin-bottom: 25px;
    border-left: 4px solid #667eea;
}

.operation-section h3 {
    color: #4a5568;
    margin-bottom: 10px;
    font-size: 1.3rem;
}

/* API路径样式 */
.api-path {
    background: #2d3748;
    color: #f7fafc;
    padding: 8px 12px;
    border-radius: 6px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    margin-bottom: 20px;
    border: 1px solid #4a5568;
}

/* API详情样式 */
.api-details {
    background: #f8f9fa;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.api-details h4 {
    color: #4a5568;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 8px;
    margin-top: 15px;
}

.api-details h4:first-child {
    margin-top: 0;
}

.api-example {
    background: #1a202c;
    color: #f7fafc;
    padding: 12px 15px;
    border-radius: 6px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    line-height: 1.4;
    overflow-x: auto;
    margin: 0 0 10px 0;
    border: 1px solid #2d3748;
    white-space: pre-wrap;
}

/* JSON语法高亮 */
.api-example .json-key {
    color: #79b8ff;
}

.api-example .json-string {
    color: #b392f0;
}

.api-example .json-number {
    color: #79b8ff;
}

.api-example .json-boolean {
    color: #ffab70;
}

/* 表单样式 */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 600;
    margin-bottom: 8px;
    color: #4a5568;
    font-size: 14px;
}

.form-group input {
    padding: 12px 15px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 按钮样式 */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    min-width: 120px;
}

.btn-primary {
    background: linear-gradient(135deg, #4299e1, #3182ce);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #3182ce, #2c5282);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(66, 153, 225, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
}

.btn-success:hover {
    background: linear-gradient(135deg, #38a169, #2f855a);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(72, 187, 120, 0.3);
}

.btn-danger {
    background: linear-gradient(135deg, #f56565, #e53e3e);
    color: white;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #e53e3e, #c53030);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(245, 101, 101, 0.3);
}

.btn-warning {
    background: linear-gradient(135deg, #ed8936, #dd6b20);
    color: white;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #dd6b20, #c05621);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(237, 137, 54, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, #a0aec0, #718096);
    color: white;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #718096, #4a5568);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(160, 174, 192, 0.3);
}

.btn-info {
    background: linear-gradient(135deg, #4299e1, #3182ce);
    color: white;
}

.btn-info:hover {
    background: linear-gradient(135deg, #3182ce, #2c5282);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(66, 153, 225, 0.3);
}

/* 结果显示区域 */
.result-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.result-section h2 {
    color: #4a5568;
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.result-controls {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

#resultArea {
    background: #1a202c;
    color: #f7fafc;
    padding: 20px;
    border-radius: 8px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    line-height: 1.5;
    overflow-x: auto;
    min-height: 200px;
    max-height: 500px;
    overflow-y: auto;
    border: 1px solid #2d3748;
    box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* 加载指示器 */
.loading-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(4px);
}

.loading-indicator.show {
    display: flex;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-indicator span {
    color: white;
    font-weight: 600;
    font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .tabs {
        flex-direction: column;
        gap: 5px;
    }
    
    .tab-button {
        width: 100%;
        min-width: auto;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        flex-direction: column;
    }
    
    .config-row {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }
    
    .config-row label {
        min-width: auto;
    }
    
    .result-controls {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 10px;
    }
    
    /* API详情移动端适配 */
    .api-details {
        padding: 15px;
        margin-bottom: 15px;
    }
    
    .api-details h4 {
        font-size: 13px;
    }
    
    .api-example {
        font-size: 12px;
        padding: 10px 12px;
    }
    
    .api-path {
        font-size: 13px;
        padding: 6px 10px;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #764ba2, #667eea);
}

/* 输入验证样式 */
input.error {
    border-color: #f56565 !important;
    box-shadow: 0 0 0 3px rgba(245, 101, 101, 0.1) !important;
}

input.success {
    border-color: #48bb78 !important;
    box-shadow: 0 0 0 3px rgba(72, 187, 120, 0.1) !important;
}

/* 提示信息样式 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    z-index: 10000;
    animation: slideIn 0.3s ease-out;
    max-width: 400px;
    word-wrap: break-word;
}

.toast.success {
    background: linear-gradient(135deg, #48bb78, #38a169);
}

.toast.error {
    background: linear-gradient(135deg, #f56565, #e53e3e);
}

.toast.warning {
    background: linear-gradient(135deg, #ed8936, #dd6b20);
}

.toast.info {
    background: linear-gradient(135deg, #4299e1, #3182ce);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* JSON高亮样式 */
.json-key {
    color: #79b8ff;
}

.json-string {
    color: #b392f0;
}

.json-number {
    color: #79b8ff;
}

.json-boolean {
    color: #ffab70;
}

.json-null {
    color: #f97583;
}

/* 简化API专用样式 */
.subtitle {
    color: #666;
    font-style: italic;
    margin-bottom: 25px;
    padding: 12px 15px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-left: 4px solid #007bff;
    border-radius: 8px;
    font-size: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* 移动端适配更新 */
@media (max-width: 768px) {
    .tabs {
        flex-wrap: wrap;
    }
    
    .tab-button {
        flex: 1;
        min-width: 85px;
        font-size: 12px;
        padding: 10px 8px;
    }
    
    .subtitle {
        font-size: 14px;
        padding: 10px 12px;
        margin-bottom: 20px;
    }
}
