// 全局变量
let currentTab = 'checker';

// DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeTabs();
    setupEventListeners();
});

// 初始化选项卡
function initializeTabs() {
    const tabButtons = document.querySelectorAll('.tab-button');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const tabName = button.getAttribute('data-tab');
            switchTab(tabName);
        });
    });
}

// 设置事件监听器
function setupEventListeners() {
    // 回车键触发测试
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && e.ctrlKey) {
            e.preventDefault();
            executeCurrentTabAction();
        }
    });
}

// 切换主选项卡
function switchTab(tabName) {
    // 更新按钮状态
    document.querySelectorAll('.tab-button').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
    
    // 更新内容显示
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    document.getElementById(tabName).classList.add('active');
    
    currentTab = tabName;
}

// 执行当前选项卡的主要操作
function executeCurrentTabAction() {
    switch (currentTab) {
        case 'checker':
            testCheckBlacklist();
            break;
        case 'payperuse':
            testUsePayPerUse();
            break;
        case 'authorization':
            testCheckAuthorizations();
            break;
        case 'simple':
            testSimpleFeatureBlacklist();
            break;
    }
}

// 更新目标服务器配置
async function updateTargetServer() {
    const targetServer = document.getElementById('targetServer').value.trim();
    if (!targetServer) {
        showToast('请输入有效的目标服务器地址', 'error');
        return;
    }
    
    try {
        const response = await fetch('/config/update', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                target_server: targetServer
            })
        });
        
        const result = await response.json();
        if (result.success) {
            showToast('配置保存成功', 'success');
        } else {
            showToast(`配置保存失败: ${result.message}`, 'error');
        }
    } catch (error) {
        showToast(`配置保存失败: ${error.message}`, 'error');
    }
}

// 显示加载指示器
function showLoading() {
    document.getElementById('loadingIndicator').classList.add('show');
}

// 隐藏加载指示器
function hideLoading() {
    document.getElementById('loadingIndicator').classList.remove('show');
}

// 显示结果（只保留当前结果）
function displayResult(result) {
    const resultArea = document.getElementById('resultArea');
    const timestamp = new Date().toLocaleString();
    const formattedResult = JSON.stringify(result, null, 2);
    
    // 直接替换内容，不保留历史
    resultArea.textContent = `[${timestamp}] 测试结果:\n\n${formattedResult}`;
    
    // 滚动到顶部
    resultArea.scrollTop = 0;
}

// 清空结果
function clearResults() {
    document.getElementById('resultArea').textContent = '';
    showToast('结果已清空', 'info');
}

// 复制结果
function copyResults() {
    const resultArea = document.getElementById('resultArea');
    if (!resultArea.textContent) {
        showToast('没有可复制的内容', 'warning');
        return;
    }
    
    navigator.clipboard.writeText(resultArea.textContent).then(() => {
        showToast('结果已复制到剪贴板', 'success');
    }).catch(() => {
        showToast('复制失败', 'error');
    });
}

// 显示提示信息
function showToast(message, type = 'info') {
    // 移除已存在的toast
    const existingToast = document.querySelector('.toast');
    if (existingToast) {
        existingToast.remove();
    }
    
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.textContent = message;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.remove();
    }, 3000);
}

// 发送API请求的通用函数
async function sendRequest(url, options = {}) {
    showLoading();
    
    try {
        const response = await fetch(url, {
            ...options,
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            }
        });
        
        const result = await response.json();
        displayResult(result);
        
        if (result.success) {
            showToast('请求成功', 'success');
        } else {
            showToast(`请求失败: ${result.message}`, 'error');
        }
        
        return result;
    } catch (error) {
        const errorResult = {
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
        };
        displayResult(errorResult);
        showToast(`请求错误: ${error.message}`, 'error');
        return errorResult;
    } finally {
        hideLoading();
    }
}

// 功能检查器测试函数
async function testCheckBlacklist() {
    const groupId = document.getElementById('checkBlacklistGroupId').value.trim();
    const requestIdentifier = document.getElementById('checkBlacklistIdentifier').value.trim();
    
    if (!groupId || !requestIdentifier) {
        showToast('请输入群组ID和请求标识符', 'error');
        return;
    }
    
    const data = {
        group_id: groupId,
        request_identifier: requestIdentifier
    };
    
    await sendRequest('/test/check-feature-blacklist', {
        method: 'POST',
        body: JSON.stringify(data)
    });
}

async function testCheckWhitelist() {
    const groupId = document.getElementById('checkWhitelistGroupId').value.trim();
    const requestIdentifier = document.getElementById('checkWhitelistIdentifier').value.trim();
    
    if (!groupId || !requestIdentifier) {
        showToast('请输入群组ID和请求标识符', 'error');
        return;
    }
    
    const data = {
        group_id: groupId,
        request_identifier: requestIdentifier
    };
    
    await sendRequest('/test/check-feature-whitelist', {
        method: 'POST',
        body: JSON.stringify(data)
    });
}

// 按次付费功能测试函数
async function testUsePayPerUse() {
    const groupId = document.getElementById('useGroupId').value.trim();
    const featureCode = document.getElementById('useFeatureCode').value.trim();
    const requestNote = document.getElementById('useRequestNote').value.trim();
    
    if (!groupId || !featureCode) {
        showToast('请输入群组ID和功能代码', 'error');
        return;
    }
    
    const data = {
        group_id: groupId,
        feature_code: featureCode,
        request_note: requestNote
    };
    
    await sendRequest('/test/pay-per-use/use', {
        method: 'POST',
        body: JSON.stringify(data)
    });
}

async function testRedeemPayPerUse() {
    const groupId = document.getElementById('redeemGroupId').value.trim();
    const code = document.getElementById('redeemCode').value.trim();
    
    if (!groupId || !code) {
        showToast('请输入群组ID和兑换码', 'error');
        return;
    }
    
    const data = {
        group_id: groupId,
        code: code
    };
    
    await sendRequest('/test/pay-per-use/redeem', {
        method: 'POST',
        body: JSON.stringify(data)
    });
}

// 授权检验测试函数
async function testCheckAuthorizations() {
    const groupIds = document.getElementById('authGroupIds').value.trim();
    
    if (!groupIds) {
        showToast('请输入群组ID列表', 'error');
        return;
    }
    
    const data = {
        group_ids: groupIds
    };
    
    await sendRequest('/test/check-authorizations', {
        method: 'POST',
        body: JSON.stringify(data)
    });
}

async function testCheckUserAuth() {
    const groupId = document.getElementById('userAuthGroupId').value.trim();
    const userQQ = document.getElementById('userAuthQQ').value.trim();
    
    if (!groupId || !userQQ) {
        showToast('请输入群组ID和用户QQ号', 'error');
        return;
    }
    
    const data = {
        group_id: groupId,
        user_qq: userQQ
    };
    
    await sendRequest('/test/check-authorization-with-user', {
        method: 'POST',
        body: JSON.stringify(data)
    });
}

async function testCheckUserByQQ() {
    const qq = document.getElementById('checkQQ').value.trim();
    
    if (!qq) {
        showToast('请输入QQ号', 'error');
        return;
    }
    
    const data = {
        qq: qq
    };
    
    await sendRequest('/test/check-user-by-qq', {
        method: 'POST',
        body: JSON.stringify(data)
    });
}

async function testGetAuthorizedGroups() {
    const skuId = document.getElementById('filterSkuId').value.trim();
    
    let url = '/test/authorized-groups';
    if (skuId) {
        url += `?sku_id=${encodeURIComponent(skuId)}`;
    }
    
    await sendRequest(url, {
        method: 'GET'
    });
}

// 群组机器人信息测试函数
async function testGroupBotsInfo() {
    const groupId = document.getElementById('botsInfoGroupId').value.trim();
    
    if (!groupId) {
        showToast('请输入群组ID', 'error');
        return;
    }
    
    await sendRequest(`/test/group-bots-info/${encodeURIComponent(groupId)}`, {
        method: 'GET'
    });
}

async function testGroupAuthorizationSummary() {
    const groupId = document.getElementById('authSummaryGroupId').value.trim();
    
    if (!groupId) {
        showToast('请输入群组ID', 'error');
        return;
    }
    
    await sendRequest(`/test/group-authorization-summary/${encodeURIComponent(groupId)}`, {
        method: 'GET'
    });
}

// 简化API测试函数
async function testSimpleFeatureBlacklist() {
    const groupId = document.getElementById('simpleBlacklistGroupId').value.trim();
    
    if (!groupId) {
        showToast('请输入群组ID', 'error');
        return;
    }
    
    await sendRequest(`/test/simple/feature-blacklist/${encodeURIComponent(groupId)}`, {
        method: 'GET'
    });
}

async function testSimpleFeatureWhitelist() {
    const groupId = document.getElementById('simpleWhitelistGroupId').value.trim();
    
    if (!groupId) {
        showToast('请输入群组ID', 'error');
        return;
    }
    
    await sendRequest(`/test/simple/feature-whitelist/${encodeURIComponent(groupId)}`, {
        method: 'GET'
    });
}

async function testSimpleAuthorization() {
    const groupId = document.getElementById('simpleAuthGroupId').value.trim();
    
    if (!groupId) {
        showToast('请输入群组ID', 'error');
        return;
    }
    
    await sendRequest(`/test/simple/authorization/${encodeURIComponent(groupId)}`, {
        method: 'GET'
    });
}

async function testSimplePayPerUseBalance() {
    const groupId = document.getElementById('simpleBalanceGroupId').value.trim();
    
    if (!groupId) {
        showToast('请输入群组ID', 'error');
        return;
    }
    
    await sendRequest(`/test/simple/pay-per-use-balance/${encodeURIComponent(groupId)}`, {
        method: 'GET'
    });
}

// 填充示例数据
function fillSampleData() {
    if (currentTab === 'checker') {
        document.getElementById('checkBlacklistGroupId').value = '123456789';
        document.getElementById('checkBlacklistIdentifier').value = 'test_identifier';
        document.getElementById('checkWhitelistGroupId').value = '123456789';
        document.getElementById('checkWhitelistIdentifier').value = 'premium_identifier';
        document.getElementById('botsInfoGroupId').value = '123456789';
        document.getElementById('authSummaryGroupId').value = '123456789';
    } else if (currentTab === 'payperuse') {
        document.getElementById('useGroupId').value = '123456789';
        document.getElementById('useFeatureCode').value = 'translation';
        document.getElementById('useRequestNote').value = '测试翻译功能';
        document.getElementById('redeemGroupId').value = '123456789';
        document.getElementById('redeemCode').value = 'TEST-CODE-123';
    } else if (currentTab === 'authorization') {
        document.getElementById('authGroupIds').value = '123456789,987654321';
        document.getElementById('userAuthGroupId').value = '123456789';
        document.getElementById('userAuthQQ').value = '123456789';
        document.getElementById('checkQQ').value = '123456789';
        document.getElementById('filterSkuId').value = '2';
    } else if (currentTab === 'simple') {
        document.getElementById('simpleBlacklistGroupId').value = '123456789';
        document.getElementById('simpleWhitelistGroupId').value = '123456789';
        document.getElementById('simpleAuthGroupId').value = '123456789';
        document.getElementById('simpleBalanceGroupId').value = '123456789';
    }
    
    showToast('示例数据已填充', 'success');
}