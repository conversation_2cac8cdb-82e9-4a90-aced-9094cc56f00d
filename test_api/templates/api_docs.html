<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API接口文档 - 用户端测试工具</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        .doc-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .api-section {
            margin-bottom: 40px;
            padding: 25px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: #f7fafc;
        }
        
        .api-title {
            color: #4a5568;
            font-size: 1.5rem;
            margin-bottom: 15px;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .endpoint {
            background: #1a202c;
            color: #f7fafc;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        
        .method {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            margin-right: 10px;
            font-size: 12px;
        }
        
        .method.post { background: #48bb78; color: white; }
        
        .param-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        
        .param-table th,
        .param-table td {
            border: 1px solid #e2e8f0;
            padding: 10px;
            text-align: left;
        }
        
        .param-table th {
            background: #edf2f7;
            font-weight: 600;
        }
        
        .required {
            color: #f56565;
            font-weight: bold;
        }
        
        .optional {
            color: #718096;
        }
        
        .back-btn {
            display: inline-block;
            margin-bottom: 20px;
            padding: 10px 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="doc-container">
            <a href="/" class="back-btn">← 返回测试工具</a>
            
            <header class="header">
                <h1>用户端API接口文档</h1>
                <p class="subtitle">用户端功能测试API详细说明</p>
            </header>

            <!-- 功能检查 API -->
            <section id="checker" class="api-section">
                <h2 class="api-title">功能检查 API</h2>
                
                <h3>1. 检查功能黑名单</h3>
                <div class="endpoint">
                    <span class="method post">POST</span>/api/check-feature-blacklist
                </div>
                <p><strong>描述：</strong>检查指定群组和请求标识符是否在功能黑名单中</p>
                <p><strong>权限：</strong>无需认证</p>
                
                <h4>请求体参数：</h4>
                <table class="param-table">
                    <tr>
                        <th>参数名</th>
                        <th>类型</th>
                        <th>必填</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>groupId</td>
                        <td>string</td>
                        <td class="required">是</td>
                        <td>群组ID</td>
                    </tr>
                    <tr>
                        <td>requestIdentifier</td>
                        <td>string</td>
                        <td class="required">是</td>
                        <td>请求标识符</td>
                    </tr>
                </table>

                <h4>响应示例：</h4>
                <div class="endpoint">
{
  "success": true,
  "blacklisted": true,
  "blacklistedFeatures": ["text_generation", "ai_chat"],
  "allowedTiers": ["pro", "enterprise"]
}
                </div>

                <h3>2. 检查功能白名单</h3>
                <div class="endpoint">
                    <span class="method post">POST</span>/api/check-feature-whitelist
                </div>
                <p><strong>描述：</strong>检查指定群组和请求标识符是否在功能白名单中</p>
                <p><strong>权限：</strong>无需认证</p>
                
                <h4>请求体参数：</h4>
                <p>参数结构与功能黑名单检查相同</p>

                <h4>响应示例：</h4>
                <div class="endpoint">
{
  "success": true,
  "whitelisted": true,
  "whitelistedFeatures": ["premium_feature"],
  "allowedTiers": ["enterprise"]
}
                </div>
            </section>

            <!-- 群组机器人管理 API -->
            <section id="group-bots" class="api-section">
                <h2 class="api-title">群组机器人管理 API</h2>
                
                <h3>1. 群组机器人详细信息</h3>
                <div class="endpoint">
                    <span class="method get">GET</span>/api/group-bots-info/{group_id}
                </div>
                <p><strong>描述：</strong>检查群组授权状态并返回群组所属BOT账号信息，避免群内存在多个bot</p>
                <p><strong>权限：</strong>无需认证</p>
                
                <h4>路径参数：</h4>
                <table class="param-table">
                    <tr>
                        <th>参数名</th>
                        <th>类型</th>
                        <th>必填</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>group_id</td>
                        <td>string</td>
                        <td class="required">是</td>
                        <td>群组ID</td>
                    </tr>
                </table>

                <h4>响应示例（已授权）：</h4>
                <div class="endpoint">
{
  "success": true,
  "authorized": true,
  "group_id": "*********",
  "authorization_info": {
    "main_tier": {
      "sku_type": "专业版",
      "sku_id": 2,
      "expiration_date": "2024-12-31T23:59:59",
      "days_left": 45,
      "owner": "username1"
    },
    "all_tiers": [...],
    "owners": ["username1"],
    "tier_count": 1
  },
  "bots_info": {
    "total_bots": 2,
    "active_bots": 1,
    "bots": [
      {
        "bot_id": 1,
        "bot_account": "*********",
        "bot_name": "AI助手",
        "api_url": "http://example.com/api",
        "is_active": true,
        "created_time": "2024-01-01T00:00:00",
        "group_info": {
          "group_name": "测试群",
          "member_count": 100,
          "max_member_count": 500
        }
      }
    ]
  },
  "warnings": [],
  "recommendations": {
    "single_bot_recommended": true,
    "message": "群组机器人配置正常"
  }
}
                </div>

                <h4>响应示例（未授权）：</h4>
                <div class="endpoint">
{
  "success": true,
  "authorized": false,
  "message": "该群组未授权",
  "group_id": "*********",
  "bots": []
}
                </div>

                <h3>2. 群组授权摘要（简化版）</h3>
                <div class="endpoint">
                    <span class="method get">GET</span>/api/group-authorization-summary/{group_id}
                </div>
                <p><strong>描述：</strong>简化版API：只返回群组授权状态和主要BOT信息，用于快速检查</p>
                <p><strong>权限：</strong>无需认证</p>
                
                <h4>路径参数：</h4>
                <table class="param-table">
                    <tr>
                        <th>参数名</th>
                        <th>类型</th>
                        <th>必填</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>group_id</td>
                        <td>string</td>
                        <td class="required">是</td>
                        <td>群组ID</td>
                    </tr>
                </table>

                <h4>响应示例（已授权）：</h4>
                <div class="endpoint">
{
  "success": true,
  "authorized": true,
  "group_id": "*********",
  "authorization": {
    "sku_ids": ["2", "3"],
    "owners": ["username1", "username2"]
  },
  "primary_bot": {
    "bot_account": "*********",
    "bot_name": "AI助手",
    "is_active": true
  },
  "bot_status": {
    "total_bots": 2,
    "active_bots": 1,
    "has_conflict": false
  }
}
                </div>

                <h4>响应示例（未授权）：</h4>
                <div class="endpoint">
{
  "success": true,
  "authorized": false,
  "group_id": "*********",
  "message": "该群组未授权"
}
                </div>
            </section>

            <!-- 简化API -->
            <section id="simple" class="api-section">
                <h2 class="api-title">简化API</h2>
                <p><strong>特点：</strong>使用GET方法，只需群组ID作为路径参数，调用更简洁</p>
                
                <h3>1. 简化版功能黑名单</h3>
                <div class="endpoint">
                    <span class="method get">GET</span>/api/simple/feature-blacklist/{group_id}
                </div>
                <p><strong>描述：</strong>返回指定群组当前禁用的所有功能列表</p>
                <p><strong>权限：</strong>无需认证</p>
                
                <h4>路径参数：</h4>
                <table class="param-table">
                    <tr>
                        <th>参数名</th>
                        <th>类型</th>
                        <th>必填</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>group_id</td>
                        <td>string</td>
                        <td class="required">是</td>
                        <td>群组ID</td>
                    </tr>
                </table>

                <h4>响应示例：</h4>
                <div class="endpoint">
{
  "success": true,
  "group_id": "*********",
  "disabled_feature_names": ["图片生成", "文本翻译"],
  "disabled_features": ["image_generation", "translation"],
  "count": 2
}
                </div>

                <h3>2. 简化版功能白名单</h3>
                <div class="endpoint">
                    <span class="method get">GET</span>/api/simple/feature-whitelist/{group_id}
                </div>
                <p><strong>描述：</strong>返回指定群组所属套餐的所有白名单功能列表</p>
                <p><strong>权限：</strong>无需认证</p>
                
                <h4>路径参数：</h4>
                <table class="param-table">
                    <tr>
                        <th>参数名</th>
                        <th>类型</th>
                        <th>必填</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>group_id</td>
                        <td>string</td>
                        <td class="required">是</td>
                        <td>群组ID</td>
                    </tr>
                </table>

                <h4>响应示例：</h4>
                <div class="endpoint">
{
  "success": true,
  "group_id": "*********",
  "authorized": true,
  "current_sku_ids": ["6b9d46742cc311f094855254001e7c00"],
  "whitelisted_feature_names": ["高级功能包", "AI助手"],
  "whitelisted_features": ["advanced_ai", "premium_chat"],
  "count": 2
}
                </div>

                <h3>3. 简化版授权检查</h3>
                <div class="endpoint">
                    <span class="method get">GET</span>/api/simple/authorization/{group_id}
                </div>
                <p><strong>描述：</strong>检查指定群组的授权状态</p>
                <p><strong>权限：</strong>无需认证</p>
                
                <h4>路径参数：</h4>
                <table class="param-table">
                    <tr>
                        <th>参数名</th>
                        <th>类型</th>
                        <th>必填</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>group_id</td>
                        <td>string</td>
                        <td class="required">是</td>
                        <td>群组ID</td>
                    </tr>
                </table>

                <h4>响应示例（已授权）：</h4>
                <div class="endpoint">
{
  "success": true,
  "group_id": "*********",
  "authorized": true,
  "sku_ids": ["6b9d46742cc311f094855254001e7c00"],
  "tier_count": 1,
  "earliest_expiration": "2024-12-31T23:59:59",
  "earliest_days_left": 45
}
                </div>

                <h4>响应示例（未授权）：</h4>
                <div class="endpoint">
{
  "success": true,
  "group_id": "*********",
  "authorized": false,
  "message": "该群组未授权"
}
                </div>

                <h3>4. 简化版按次付费余额</h3>
                <div class="endpoint">
                    <span class="method get">GET</span>/api/simple/pay-per-use-balance/{group_id}
                </div>
                <p><strong>描述：</strong>返回指定群组的增值功能余额</p>
                <p><strong>权限：</strong>无需认证</p>
                
                <h4>路径参数：</h4>
                <table class="param-table">
                    <tr>
                        <th>参数名</th>
                        <th>类型</th>
                        <th>必填</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>group_id</td>
                        <td>string</td>
                        <td class="required">是</td>
                        <td>群组ID</td>
                    </tr>
                </table>

                <h4>响应示例：</h4>
                <div class="endpoint">
{
  "success": true,
  "group_id": "*********",
  "balance_details": [
    {
      "feature_code": "translation",
      "feature_name": "文本翻译",
      "remaining_count": 50,
      "total_purchased": 100
    }
  ],
  "summary": {
    "total_features": 1,
    "total_remaining_count": 50,
    "total_purchased_count": 100
  }
}
                </div>
            </section>

            <!-- 授权检验 API -->
            <section id="authorization" class="api-section">
                <h2 class="api-title">授权检验 API</h2>
                
                <h3>1. 批量检查群聊授权状态</h3>
                <div class="endpoint">
                    <span class="method post">POST</span>/api/check-authorizations
                </div>
                <p><strong>描述：</strong>批量检查多个群聊的授权状态和到期时间</p>
                <p><strong>权限：</strong>无需认证</p>
                
                <h4>请求体参数：</h4>
                <table class="param-table">
                    <tr>
                        <th>参数名</th>
                        <th>类型</th>
                        <th>必填</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>groupIds</td>
                        <td>array</td>
                        <td class="required">是</td>
                        <td>群组ID数组</td>
                    </tr>
                </table>

                <h4>响应示例：</h4>
                <div class="endpoint">
{
  "success": true,
  "data": {
    "*********": {
      "authorized": true,
      "skuType": "专业版",
      "skuId": 2,
      "expirationDate": "2024-12-31T23:59:59",
      "allTiers": [...],
      "owner": "username"
    },
    "987654321": {
      "authorized": false
    }
  }
}
                </div>

                <h3>2. 检查用户在群聊的授权</h3>
                <div class="endpoint">
                    <span class="method post">POST</span>/api/check-authorization-with-user
                </div>
                <p><strong>描述：</strong>检查指定用户是否有权限拉入/管理指定群聊</p>
                <p><strong>权限：</strong>无需认证</p>
                
                <h4>请求体参数：</h4>
                <table class="param-table">
                    <tr>
                        <th>参数名</th>
                        <th>类型</th>
                        <th>必填</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>groupId</td>
                        <td>string</td>
                        <td class="required">是</td>
                        <td>群组ID</td>
                    </tr>
                    <tr>
                        <td>userId</td>
                        <td>string</td>
                        <td class="required">是</td>
                        <td>用户QQ号</td>
                    </tr>
                </table>

                <h4>响应示例：</h4>
                <div class="endpoint">
{
  "authorized": true,
  "skuType": "专业版",
  "expirationDate": "2024-12-31T23:59:59",
  "daysLeft": 90,
  "username": "用户名",
  "userTiers": [...]
}
                </div>

                <h3>3. 检查QQ用户是否注册</h3>
                <div class="endpoint">
                    <span class="method post">POST</span>/api/check-user-by-qq
                </div>
                <p><strong>描述：</strong>检查指定QQ号是否已注册账户</p>
                <p><strong>权限：</strong>无需认证</p>
                
                <h4>请求体参数：</h4>
                <table class="param-table">
                    <tr>
                        <th>参数名</th>
                        <th>类型</th>
                        <th>必填</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>qq</td>
                        <td>string</td>
                        <td class="required">是</td>
                        <td>QQ号</td>
                    </tr>
                </table>

                <h4>响应示例：</h4>
                <div class="endpoint">
{
  "isRegistered": true,
  "username": "用户名"
}
                </div>

                <h3>4. 获取已授权群聊列表</h3>
                <div class="endpoint">
                    <span class="method get">GET</span>/api/authorized-groups
                </div>
                <p><strong>描述：</strong>获取所有已授权的群聊列表</p>
                <p><strong>权限：</strong>无需认证</p>
                
                <h4>查询参数：</h4>
                <table class="param-table">
                    <tr>
                        <th>参数名</th>
                        <th>类型</th>
                        <th>必填</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>skuId</td>
                        <td>string</td>
                        <td class="optional">否</td>
                        <td>套餐ID筛选</td>
                    </tr>
                </table>

                <h4>响应示例：</h4>
                <div class="endpoint">
{
  "success": true,
  "data": [
    {
      "groupId": "*********",
      "skuId": 2,
      "skuType": "专业版",
      "expirationDate": "2024-12-31T23:59:59",
      "owners": ["username1", "username2"]
    }
  ]
}
                </div>
            </section>

            <!-- 按次付费 API -->
            <section id="payperuse" class="api-section">
                <h2 class="api-title">按次付费 API</h2>
                
                <h3>1. 使用按次付费功能</h3>
                <div class="endpoint">
                    <span class="method post">POST</span>/api/pay-per-use/use
                </div>
                <p><strong>描述：</strong>使用按次付费功能（扣费）</p>
                <p><strong>权限：</strong>无需认证</p>
                
                <h4>请求体参数：</h4>
                <table class="param-table">
                    <tr>
                        <th>参数名</th>
                        <th>类型</th>
                        <th>必填</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>groupId</td>
                        <td>string</td>
                        <td class="required">是</td>
                        <td>群组ID</td>
                    </tr>
                    <tr>
                        <td>featureCode</td>
                        <td>string</td>
                        <td class="required">是</td>
                        <td>功能代码</td>
                    </tr>
                    <tr>
                        <td>requestNote</td>
                        <td>string</td>
                        <td class="optional">否</td>
                        <td>请求备注</td>
                    </tr>
                </table>

                <h4>响应示例：</h4>
                <div class="endpoint">
{
  "success": true,
  "message": "功能使用成功",
  "remainingCount": 9,
  "featureName": "文本翻译"
}
                </div>

                <h3>2. 兑换兑换码</h3>
                <div class="endpoint">
                    <span class="method post">POST</span>/api/pay-per-use/redeem
                </div>
                <p><strong>描述：</strong>兑换按次付费兑换码</p>
                <p><strong>权限：</strong>无需认证</p>
                
                <h4>请求体参数：</h4>
                <table class="param-table">
                    <tr>
                        <th>参数名</th>
                        <th>类型</th>
                        <th>必填</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>groupId</td>
                        <td>string</td>
                        <td class="required">是</td>
                        <td>群组ID</td>
                    </tr>
                    <tr>
                        <td>code</td>
                        <td>string</td>
                        <td class="required">是</td>
                        <td>兑换码</td>
                    </tr>
                </table>

                <h4>响应示例：</h4>
                <div class="endpoint">
{
  "success": true,
  "message": "兑换成功",
  "redeemedFeatures": [
    {
      "featureName": "文本翻译",
      "addedCount": 10
    }
  ]
}
                </div>
            </section>

            <!-- 示例代码 -->
            <section id="examples" class="api-section">
                <h2 class="api-title">示例代码</h2>
                
                <h3>JavaScript示例</h3>
                <div class="endpoint">
// 检查功能黑名单
async function checkBlacklist(groupId, requestIdentifier) {
    const response = await fetch('http://localhost:4000/api/check-feature-blacklist', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            groupId: groupId,
            requestIdentifier: requestIdentifier
        })
    });
    
    return await response.json();
}

// 批量检查群聊授权状态
async function checkGroupAuthorizations(groupIds) {
    const response = await fetch('http://localhost:4000/api/check-authorizations', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            groupIds: groupIds
        })
    });
    
    return await response.json();
}

// 检查用户在群聊的授权
async function checkUserAuthInGroup(groupId, userQQ) {
    const response = await fetch('http://localhost:4000/api/check-authorization-with-user', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            groupId: groupId,
            userId: userQQ
        })
    });
    
    return await response.json();
}

// 检查QQ用户是否注册
async function checkUserByQQ(qq) {
    const response = await fetch('http://localhost:4000/api/check-user-by-qq', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            qq: qq
        })
    });
    
    return await response.json();
}

// 简化版功能黑名单
async function getSimpleFeatureBlacklist(groupId) {
    const response = await fetch(`http://localhost:4000/api/simple/feature-blacklist/${groupId}`);
    return await response.json();
}

// 简化版功能白名单
async function getSimpleFeatureWhitelist(groupId) {
    const response = await fetch(`http://localhost:4000/api/simple/feature-whitelist/${groupId}`);
    return await response.json();
}

// 简化版授权检查
async function getSimpleAuthorization(groupId) {
    const response = await fetch(`http://localhost:4000/api/simple/authorization/${groupId}`);
    return await response.json();
}

// 简化版按次付费余额
async function getSimplePayPerUseBalance(groupId) {
    const response = await fetch(`http://localhost:4000/api/simple/pay-per-use-balance/${groupId}`);
    return await response.json();
}

// 群组机器人详细信息
async function getGroupBotsInfo(groupId) {
    const response = await fetch(`http://localhost:4000/api/group-bots-info/${groupId}`);
    return await response.json();
}

// 群组授权摘要
async function getGroupAuthorizationSummary(groupId) {
    const response = await fetch(`http://localhost:4000/api/group-authorization-summary/${groupId}`);
    return await response.json();
}

// 使用按次付费功能
async function usePayPerUseFeature(groupId, featureCode, requestNote) {
    const response = await fetch('http://localhost:4000/api/pay-per-use/use', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            groupId: groupId,
            featureCode: featureCode,
            requestNote: requestNote
        })
    });
    
    return await response.json();
}

// 兑换兑换码
async function redeemCode(groupId, code) {
    const response = await fetch('http://localhost:4000/api/pay-per-use/redeem', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            groupId: groupId,
            code: code
        })
    });
    
    return await response.json();
}
                </div>

                <h3>Python示例</h3>
                <div class="endpoint">
import requests
import json

def check_blacklist(group_id, request_identifier):
    """检查功能黑名单"""
    url = "http://localhost:4000/api/check-feature-blacklist"
    data = {
        "groupId": group_id,
        "requestIdentifier": request_identifier
    }
    
    response = requests.post(url, json=data)
    return response.json()

def check_group_authorizations(group_ids):
    """批量检查群聊授权状态"""
    url = "http://localhost:4000/api/check-authorizations"
    data = {
        "groupIds": group_ids
    }
    
    response = requests.post(url, json=data)
    return response.json()

def check_user_auth_in_group(group_id, user_qq):
    """检查用户在群聊的授权"""
    url = "http://localhost:4000/api/check-authorization-with-user"
    data = {
        "groupId": group_id,
        "userId": user_qq
    }
    
    response = requests.post(url, json=data)
    return response.json()

def check_user_by_qq(qq):
    """检查QQ用户是否注册"""
    url = "http://localhost:4000/api/check-user-by-qq"
    data = {
        "qq": qq
    }
    
    response = requests.post(url, json=data)
    return response.json()

def get_authorized_groups(sku_id=None):
    """获取已授权群聊列表"""
    url = "http://localhost:4000/api/authorized-groups"
    params = {}
    if sku_id:
        params["skuId"] = sku_id
    
    response = requests.get(url, params=params)
    return response.json()

def get_simple_feature_blacklist(group_id):
    """简化版功能黑名单"""
    url = f"http://localhost:4000/api/simple/feature-blacklist/{group_id}"
    response = requests.get(url)
    return response.json()

def get_simple_feature_whitelist(group_id):
    """简化版功能白名单"""
    url = f"http://localhost:4000/api/simple/feature-whitelist/{group_id}"
    response = requests.get(url)
    return response.json()

def get_simple_authorization(group_id):
    """简化版授权检查"""
    url = f"http://localhost:4000/api/simple/authorization/{group_id}"
    response = requests.get(url)
    return response.json()

def get_simple_pay_per_use_balance(group_id):
    """简化版按次付费余额"""
    url = f"http://localhost:4000/api/simple/pay-per-use-balance/{group_id}"
    response = requests.get(url)
    return response.json()

def get_group_bots_info(group_id):
    """群组机器人详细信息"""
    url = f"http://localhost:4000/api/group-bots-info/{group_id}"
    response = requests.get(url)
    return response.json()

def get_group_authorization_summary(group_id):
    """群组授权摘要"""
    url = f"http://localhost:4000/api/group-authorization-summary/{group_id}"
    response = requests.get(url)
    return response.json()

def use_pay_per_use(group_id, feature_code, request_note=""):
    """使用按次付费功能"""
    url = "http://localhost:4000/api/pay-per-use/use"
    data = {
        "groupId": group_id,
        "featureCode": feature_code,
        "requestNote": request_note
    }
    
    response = requests.post(url, json=data)
    return response.json()

def redeem_code(group_id, code):
    """兑换兑换码"""
    url = "http://localhost:4000/api/pay-per-use/redeem"
    data = {
        "groupId": group_id,
        "code": code
    }
    
    response = requests.post(url, json=data)
    return response.json()
                </div>

                <h3>cURL示例</h3>
                <div class="endpoint">
# 检查功能黑名单
curl -X POST "http://localhost:4000/api/check-feature-blacklist" \
     -H "Content-Type: application/json" \
     -d '{
       "groupId": "*********",
       "requestIdentifier": "test_identifier"
     }'

# 批量检查群聊授权状态
curl -X POST "http://localhost:4000/api/check-authorizations" \
     -H "Content-Type: application/json" \
     -d '{
       "groupIds": ["*********", "987654321"]
     }'

# 检查用户在群聊的授权
curl -X POST "http://localhost:4000/api/check-authorization-with-user" \
     -H "Content-Type: application/json" \
     -d '{
       "groupId": "*********",
       "userId": "*********"
     }'

# 检查QQ用户是否注册
curl -X POST "http://localhost:4000/api/check-user-by-qq" \
     -H "Content-Type: application/json" \
     -d '{
       "qq": "*********"
     }'

# 获取已授权群聊列表
curl -X GET "http://localhost:4000/api/authorized-groups?skuId=2"

# 简化版功能黑名单
curl -X GET "http://localhost:4000/api/simple/feature-blacklist/*********"

# 简化版功能白名单
curl -X GET "http://localhost:4000/api/simple/feature-whitelist/*********"

# 简化版授权检查
curl -X GET "http://localhost:4000/api/simple/authorization/*********"

# 简化版按次付费余额
curl -X GET "http://localhost:4000/api/simple/pay-per-use-balance/*********"

# 群组机器人详细信息
curl -X GET "http://localhost:4000/api/group-bots-info/*********"

# 群组授权摘要
curl -X GET "http://localhost:4000/api/group-authorization-summary/*********"

# 使用按次付费功能
curl -X POST "http://localhost:4000/api/pay-per-use/use" \
     -H "Content-Type: application/json" \
     -d '{
       "groupId": "*********",
       "featureCode": "translation",
       "requestNote": "翻译测试"
     }'

# 兑换兑换码
curl -X POST "http://localhost:4000/api/pay-per-use/redeem" \
     -H "Content-Type: application/json" \
     -d '{
       "groupId": "*********",
       "code": "REDEEM-CODE-123"
     }'
                </div>
            </section>

            <footer style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #e2e8f0; color: #718096;">
                <p>用户端API测试工具 - 版本 1.0</p>
                <p>监听端口：9520 | 目标服务器：http://localhost:4000</p>
            </footer>
        </div>
    </div>
</body>
</html>