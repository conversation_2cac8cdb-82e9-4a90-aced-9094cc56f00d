<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户端API测试工具</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>用户端API测试工具</h1>
            <p class="subtitle">测试功能黑名单检查、功能白名单检查、按次付费功能使用</p>
        </header>

        <!-- 配置面板 -->
        <div class="config-panel">
            <h2>基础配置</h2>
            <div class="config-row">
                <label for="targetServer">目标服务器:</label>
                <input type="text" id="targetServer" value="http://localhost:4000" placeholder="目标服务器地址">
                <button onclick="updateTargetServer()" class="btn btn-primary">保存配置</button>
            </div>
        </div>

        <!-- 功能选项卡 -->
        <div class="tabs">
            <button class="tab-button active" data-tab="checker">功能检查</button>
            <button class="tab-button" data-tab="payperuse">按次付费</button>
            <button class="tab-button" data-tab="authorization">授权检验</button>
            <button class="tab-button" data-tab="simple">简化API</button>
        </div>

        <!-- 功能检查面板 -->
        <div id="checker" class="tab-content active">
            <h2>功能检查测试</h2>
            
            <div class="operation-section">
                <h3>检查功能黑名单</h3>
                <p class="api-path"><strong>API路径:</strong> POST /api/check-feature-blacklist</p>
                
                <div class="api-details">
                    <h4>请求格式:</h4>
                    <pre class="api-example">{
  "groupId": "群组ID",
  "requestIdentifier": "请求标识符"
}</pre>
                    
                    <h4>返回示例:</h4>
                    <pre class="api-example">{
  "success": true,
  "blacklisted": true,
  "blacklistedFeatures": ["text_generation", "ai_chat"],
  "allowedTiers": ["pro", "enterprise"]
}</pre>
                </div>
                
                <div class="form-grid">
                    <div class="form-group">
                        <label for="checkBlacklistGroupId">群组ID:</label>
                        <input type="text" id="checkBlacklistGroupId" placeholder="群组ID">
                    </div>
                    <div class="form-group">
                        <label for="checkBlacklistIdentifier">请求标识符:</label>
                        <input type="text" id="checkBlacklistIdentifier" placeholder="请求标识符">
                    </div>
                </div>
                <button onclick="testCheckBlacklist()" class="btn btn-primary">检查黑名单</button>
            </div>

            <div class="operation-section">
                <h3>检查功能白名单</h3>
                <p class="api-path"><strong>API路径:</strong> POST /api/check-feature-whitelist</p>
                
                <div class="api-details">
                    <h4>请求格式:</h4>
                    <pre class="api-example">{
  "groupId": "群组ID",
  "requestIdentifier": "请求标识符"
}</pre>
                    
                    <h4>返回示例:</h4>
                    <pre class="api-example">{
  "success": true,
  "whitelisted": true,
  "whitelistedFeatures": ["premium_feature"],
  "allowedTiers": ["enterprise"]
}</pre>
                </div>
                
                <div class="form-grid">
                    <div class="form-group">
                        <label for="checkWhitelistGroupId">群组ID:</label>
                        <input type="text" id="checkWhitelistGroupId" placeholder="群组ID">
                    </div>
                    <div class="form-group">
                        <label for="checkWhitelistIdentifier">请求标识符:</label>
                        <input type="text" id="checkWhitelistIdentifier" placeholder="请求标识符">
                    </div>
                </div>
                <button onclick="testCheckWhitelist()" class="btn btn-primary">检查白名单</button>
            </div>

            <div class="operation-section">
                <h3>群组机器人详细信息</h3>
                <p class="api-path"><strong>API路径:</strong> GET /api/group-bots-info/{group_id}</p>
                
                <div class="api-details">
                    <h4>请求格式:</h4>
                    <pre class="api-example">GET /api/group-bots-info/*********</pre>
                    
                    <h4>返回示例:</h4>
                    <pre class="api-example">{
  "success": true,
  "authorized": true,
  "group_id": "*********",
  "authorization_info": {
    "main_tier": {
      "sku_type": "专业版",
      "sku_id": 2,
      "expiration_date": "2024-12-31T23:59:59",
      "days_left": 45
    },
    "all_tiers": [...],
    "owners": ["username1"],
    "tier_count": 1
  },
  "bots_info": {
    "total_bots": 2,
    "active_bots": 1,
    "bots": [...]
  },
  "warnings": [],
  "recommendations": {
    "single_bot_recommended": true,
    "message": "群组机器人配置正常"
  }
}</pre>
                </div>
                
                <div class="form-group">
                    <label for="botsInfoGroupId">群组ID:</label>
                    <input type="text" id="botsInfoGroupId" placeholder="群组ID">
                </div>
                <button onclick="testGroupBotsInfo()" class="btn btn-info">获取机器人信息</button>
            </div>

            <div class="operation-section">
                <h3>群组授权摘要（简化版）</h3>
                <p class="api-path"><strong>API路径:</strong> GET /api/group-authorization-summary/{group_id}</p>
                
                <div class="api-details">
                    <h4>请求格式:</h4>
                    <pre class="api-example">GET /api/group-authorization-summary/*********</pre>
                    
                    <h4>返回示例:</h4>
                    <pre class="api-example">{
  "success": true,
  "authorized": true,
  "group_id": "*********",
  "authorization": {
    "sku_ids": ["2", "3"],
    "owners": ["username1", "username2"]
  },
  "primary_bot": {
    "bot_account": "*********",
    "bot_name": "AI助手",
    "is_active": true
  },
  "bot_status": {
    "total_bots": 2,
    "active_bots": 1,
    "has_conflict": false
  }
}</pre>
                </div>
                
                <div class="form-group">
                    <label for="authSummaryGroupId">群组ID:</label>
                    <input type="text" id="authSummaryGroupId" placeholder="群组ID">
                </div>
                <button onclick="testGroupAuthorizationSummary()" class="btn btn-success">获取授权摘要</button>
            </div>
        </div>

        <!-- 按次付费面板 -->
        <div id="payperuse" class="tab-content">
            <h2>按次付费功能测试</h2>
            
            <div class="operation-section">
                <h3>使用按次付费功能</h3>
                <p class="api-path"><strong>API路径:</strong> POST /api/pay-per-use/use</p>
                
                <div class="api-details">
                    <h4>请求格式:</h4>
                    <pre class="api-example">{
  "groupId": "群组ID",
  "featureCode": "功能代码",
  "requestNote": "请求备注(可选)"
}</pre>
                    
                    <h4>返回示例:</h4>
                    <pre class="api-example">{
  "success": true,
  "message": "功能使用成功",
  "remainingCount": 9,
  "featureName": "文本翻译"
}</pre>
                </div>
                
                <div class="form-grid">
                    <div class="form-group">
                        <label for="useGroupId">群组ID:</label>
                        <input type="text" id="useGroupId" placeholder="群组ID">
                    </div>
                    <div class="form-group">
                        <label for="useFeatureCode">功能代码:</label>
                        <input type="text" id="useFeatureCode" placeholder="功能代码">
                    </div>
                    <div class="form-group">
                        <label for="useRequestNote">请求备注:</label>
                        <input type="text" id="useRequestNote" placeholder="使用说明">
                    </div>
                </div>
                <button onclick="testUsePayPerUse()" class="btn btn-warning">使用功能</button>
            </div>

            <div class="operation-section">
                <h3>兑换兑换码</h3>
                <p class="api-path"><strong>API路径:</strong> POST /api/pay-per-use/redeem</p>
                
                <div class="api-details">
                    <h4>请求格式:</h4>
                    <pre class="api-example">{
  "groupId": "群组ID",
  "code": "兑换码"
}</pre>
                    
                    <h4>返回示例:</h4>
                    <pre class="api-example">{
  "success": true,
  "message": "兑换成功",
  "redeemedFeatures": [
    {
      "featureName": "文本翻译",
      "addedCount": 10
    }
  ]
}</pre>
                </div>
                
                <div class="form-grid">
                    <div class="form-group">
                        <label for="redeemGroupId">群组ID:</label>
                        <input type="text" id="redeemGroupId" placeholder="群组ID">
                    </div>
                    <div class="form-group">
                        <label for="redeemCode">兑换码:</label>
                        <input type="text" id="redeemCode" placeholder="兑换码">
                    </div>
                </div>
                <button onclick="testRedeemPayPerUse()" class="btn btn-success">兑换兑换码</button>
            </div>
        </div>

        <!-- 授权检验面板 -->
        <div id="authorization" class="tab-content">
            <h2>授权检验测试</h2>
            
            <div class="operation-section">
                <h3>批量检查群聊授权状态</h3>
                <p class="api-path"><strong>API路径:</strong> POST /api/check-authorizations</p>
                
                <div class="api-details">
                    <h4>请求格式:</h4>
                    <pre class="api-example">{
  "groupIds": ["群组ID1", "群组ID2", "群组ID3"]
}</pre>
                    
                    <h4>返回示例:</h4>
                    <pre class="api-example">{
  "success": true,
  "data": {
    "*********": {
      "authorized": true,
      "skuType": "专业版",
      "skuId": 2,
      "expirationDate": "2024-12-31T23:59:59",
      "allTiers": [...],
      "owner": "username"
    },
    "987654321": {
      "authorized": false
    }
  }
}</pre>
                </div>
                
                <div class="form-group">
                    <label for="authGroupIds">群组ID列表 (用逗号分隔):</label>
                    <input type="text" id="authGroupIds" placeholder="*********,987654321">
                </div>
                <button onclick="testCheckAuthorizations()" class="btn btn-primary">检查群聊授权</button>
            </div>

            <div class="operation-section">
                <h3>检查用户在群聊的授权</h3>
                <p class="api-path"><strong>API路径:</strong> POST /api/check-authorization-with-user</p>
                
                <div class="api-details">
                    <h4>请求格式:</h4>
                    <pre class="api-example">{
  "groupId": "群组ID",
  "userId": "用户QQ号"
}</pre>
                    
                    <h4>返回示例:</h4>
                    <pre class="api-example">{
  "authorized": true,
  "skuType": "专业版",
  "expirationDate": "2024-12-31T23:59:59",
  "daysLeft": 90,
  "username": "用户名",
  "userTiers": [...]
}</pre>
                </div>
                
                <div class="form-grid">
                    <div class="form-group">
                        <label for="userAuthGroupId">群组ID:</label>
                        <input type="text" id="userAuthGroupId" placeholder="群组ID">
                    </div>
                    <div class="form-group">
                        <label for="userAuthQQ">用户QQ号:</label>
                        <input type="text" id="userAuthQQ" placeholder="QQ号">
                    </div>
                </div>
                <button onclick="testCheckUserAuth()" class="btn btn-primary">检查用户授权</button>
            </div>

            <div class="operation-section">
                <h3>检查QQ用户是否注册</h3>
                <p class="api-path"><strong>API路径:</strong> POST /api/check-user-by-qq</p>
                
                <div class="api-details">
                    <h4>请求格式:</h4>
                    <pre class="api-example">{
  "qq": "QQ号"
}</pre>
                    
                    <h4>返回示例:</h4>
                    <pre class="api-example">{
  "isRegistered": true,
  "username": "用户名"
}</pre>
                </div>
                
                <div class="form-group">
                    <label for="checkQQ">QQ号:</label>
                    <input type="text" id="checkQQ" placeholder="QQ号">
                </div>
                <button onclick="testCheckUserByQQ()" class="btn btn-primary">检查QQ注册</button>
            </div>

            <div class="operation-section">
                <h3>获取已授权群聊列表</h3>
                <p class="api-path"><strong>API路径:</strong> GET /api/authorized-groups</p>
                
                <div class="api-details">
                    <h4>请求格式:</h4>
                    <pre class="api-example">GET /api/authorized-groups?skuId=2</pre>
                    
                    <h4>返回示例:</h4>
                    <pre class="api-example">{
  "success": true,
  "data": [
    {
      "groupId": "*********",
      "skuId": 2,
      "skuType": "专业版",
      "expirationDate": "2024-12-31T23:59:59",
      "owners": ["username1", "username2"]
    }
  ]
}</pre>
                </div>
                
                <div class="form-group">
                    <label for="filterSkuId">套餐ID筛选 (可选):</label>
                    <input type="number" id="filterSkuId" placeholder="套餐ID，留空获取所有">
                </div>
                <button onclick="testGetAuthorizedGroups()" class="btn btn-primary">获取授权群聊</button>
            </div>
        </div>

        <!-- 简化API面板 -->
        <div id="simple" class="tab-content">
            <h2>简化API测试</h2>
            <p class="subtitle">这些API只需要群组ID作为参数，使用GET方法，更加简洁高效</p>
            
            <div class="operation-section">
                <h3>简化版功能黑名单</h3>
                <p class="api-path"><strong>API路径:</strong> GET /api/simple/feature-blacklist/{group_id}</p>
                
                <div class="api-details">
                    <h4>请求格式:</h4>
                    <pre class="api-example">GET /api/simple/feature-blacklist/*********</pre>
                    
                    <h4>返回示例:</h4>
                    <pre class="api-example">{
  "success": true,
  "group_id": "*********",
  "disabled_feature_names": ["图片生成", "文本翻译"],
  "disabled_features": ["image_generation", "translation"],
  "count": 2
}</pre>
                </div>
                
                <div class="form-group">
                    <label for="simpleBlacklistGroupId">群组ID:</label>
                    <input type="text" id="simpleBlacklistGroupId" placeholder="群组ID">
                </div>
                <button onclick="testSimpleFeatureBlacklist()" class="btn btn-primary">获取功能黑名单</button>
            </div>

            <div class="operation-section">
                <h3>简化版功能白名单</h3>
                <p class="api-path"><strong>API路径:</strong> GET /api/simple/feature-whitelist/{group_id}</p>
                
                <div class="api-details">
                    <h4>请求格式:</h4>
                    <pre class="api-example">GET /api/simple/feature-whitelist/*********</pre>
                    
                    <h4>返回示例:</h4>
                    <pre class="api-example">{
  "success": true,
  "group_id": "*********",
  "authorized": true,
  "current_sku_ids": ["6b9d46742cc311f094855254001e7c00"],
  "whitelisted_feature_names": ["高级功能包", "AI助手"],
  "whitelisted_features": ["advanced_ai", "premium_chat"],
  "count": 2
}</pre>
                </div>
                
                <div class="form-group">
                    <label for="simpleWhitelistGroupId">群组ID:</label>
                    <input type="text" id="simpleWhitelistGroupId" placeholder="群组ID">
                </div>
                <button onclick="testSimpleFeatureWhitelist()" class="btn btn-primary">获取功能白名单</button>
            </div>

            <div class="operation-section">
                <h3>简化版授权检查</h3>
                <p class="api-path"><strong>API路径:</strong> GET /api/simple/authorization/{group_id}</p>
                
                <div class="api-details">
                    <h4>请求格式:</h4>
                    <pre class="api-example">GET /api/simple/authorization/*********</pre>
                    
                    <h4>返回示例（已授权）:</h4>
                    <pre class="api-example">{
  "success": true,
  "group_id": "*********",
  "authorized": true,
  "sku_ids": ["6b9d46742cc311f094855254001e7c00"],
  "tier_count": 1,
  "earliest_expiration": "2024-12-31T23:59:59",
  "earliest_days_left": 45
}</pre>
                    
                    <h4>返回示例（未授权）:</h4>
                    <pre class="api-example">{
  "success": true,
  "group_id": "*********",
  "authorized": false,
  "message": "该群组未授权"
}</pre>
                </div>
                
                <div class="form-group">
                    <label for="simpleAuthGroupId">群组ID:</label>
                    <input type="text" id="simpleAuthGroupId" placeholder="群组ID">
                </div>
                <button onclick="testSimpleAuthorization()" class="btn btn-primary">检查授权状态</button>
            </div>

            <div class="operation-section">
                <h3>简化版按次付费余额</h3>
                <p class="api-path"><strong>API路径:</strong> GET /api/simple/pay-per-use-balance/{group_id}</p>
                
                <div class="api-details">
                    <h4>请求格式:</h4>
                    <pre class="api-example">GET /api/simple/pay-per-use-balance/*********</pre>
                    
                    <h4>返回示例:</h4>
                    <pre class="api-example">{
  "success": true,
  "group_id": "*********",
  "balance_details": [
    {
      "feature_code": "translation",
      "feature_name": "文本翻译",
      "remaining_count": 50,
      "total_purchased": 100
    }
  ],
  "summary": {
    "total_features": 1,
    "total_remaining_count": 50,
    "total_purchased_count": 100
  }
}</pre>
                </div>
                
                <div class="form-group">
                    <label for="simpleBalanceGroupId">群组ID:</label>
                    <input type="text" id="simpleBalanceGroupId" placeholder="群组ID">
                </div>
                <button onclick="testSimplePayPerUseBalance()" class="btn btn-primary">获取余额信息</button>
            </div>
        </div>

        <!-- 结果显示区域 -->
        <div class="result-section">
            <h2>测试结果</h2>
            <div class="result-controls">
                <button onclick="clearResults()" class="btn btn-secondary">清空结果</button>
                <button onclick="copyResults()" class="btn btn-secondary">复制结果</button>
                <button onclick="fillSampleData()" class="btn btn-secondary">填充示例数据</button>
            </div>
            <pre id="resultArea"></pre>
        </div>

        <!-- 加载指示器 -->
        <div id="loadingIndicator" class="loading-indicator">
            <div class="spinner"></div>
            <span>正在处理请求...</span>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>