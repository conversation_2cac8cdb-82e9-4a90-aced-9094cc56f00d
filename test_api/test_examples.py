#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
管理端API测试示例脚本
演示如何使用各个API接口
"""

import requests
import json
import time

class APITester:
    def __init__(self, base_url="http://localhost:5000", admin_password="your_password"):
        self.base_url = base_url
        self.admin_password = admin_password
        self.session = requests.Session()
        
    def admin_headers(self):
        """获取管理员请求头"""
        return {
            "X-Admin-Password": self.admin_password,
            "Content-Type": "application/json"
        }
    
    def test_feature_blacklist(self):
        """测试功能黑名单API"""
        print("🔥 测试功能黑名单API")
        print("-" * 50)
        
        # 1. 获取黑名单列表
        print("1. 获取黑名单列表...")
        response = self.session.get(
            f"{self.base_url}/admin/feature-blacklist",
            headers=self.admin_headers()
        )
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        print()
        
        # 2. 创建黑名单
        print("2. 创建新的黑名单...")
        blacklist_data = {
            "displayName": "测试黑名单功能",
            "actualFeatures": ["test_feature1", "test_feature2"],
            "requestIdentifiers": ["test_id1", "test_id2"],
            "allowedTiers": ["pro", "enterprise"]
        }
        
        response = self.session.post(
            f"{self.base_url}/admin/feature-blacklist/save",
            headers=self.admin_headers(),
            json=blacklist_data
        )
        print(f"状态码: {response.status_code}")
        result = response.json()
        print(f"响应: {result}")
        
        # 保存创建的ID用于后续删除
        created_id = None
        if result.get('success') and result.get('data', {}).get('id'):
            created_id = result['data']['id']
            print(f"创建的黑名单ID: {created_id}")
        print()
        
        return created_id
    
    def test_feature_whitelist(self):
        """测试功能白名单API"""
        print("⚪ 测试功能白名单API")
        print("-" * 50)
        
        # 1. 获取白名单列表
        print("1. 获取白名单列表...")
        response = self.session.get(
            f"{self.base_url}/admin/feature-whitelist",
            headers=self.admin_headers()
        )
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        print()
        
        # 2. 创建白名单
        print("2. 创建新的白名单...")
        whitelist_data = {
            "displayName": "测试白名单功能",
            "actualFeatures": ["premium_feature"],
            "requestIdentifiers": ["premium_id"],
            "allowedTiers": ["enterprise"]
        }
        
        response = self.session.post(
            f"{self.base_url}/admin/feature-whitelist/save",
            headers=self.admin_headers(),
            json=whitelist_data
        )
        print(f"状态码: {response.status_code}")
        result = response.json()
        print(f"响应: {result}")
        print()
        
        return result.get('data', {}).get('id')
    
    def test_pay_per_use(self):
        """测试按次付费API"""
        print("💰 测试按次付费API")
        print("-" * 50)
        
        # 1. 获取按次付费功能列表
        print("1. 获取按次付费功能列表...")
        response = self.session.get(
            f"{self.base_url}/admin/pay-per-use/features",
            headers=self.admin_headers()
        )
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        print()
        
        # 2. 创建按次付费功能
        print("2. 创建新的按次付费功能...")
        feature_data = {
            "featureCode": "test_translation",
            "displayName": "测试翻译功能",
            "description": "用于测试的翻译功能",
            "apiEndpoint": "/api/pay-per-use/test_translation",
            "costPerUse": 1
        }
        
        response = self.session.post(
            f"{self.base_url}/admin/pay-per-use/features/save",
            headers=self.admin_headers(),
            json=feature_data
        )
        print(f"状态码: {response.status_code}")
        result = response.json()
        print(f"响应: {result}")
        
        feature_id = result.get('data', {}).get('id')
        if feature_id:
            print(f"创建的功能ID: {feature_id}")
            
            # 3. 生成兑换码
            print("3. 生成兑换码...")
            code_data = {
                "featureIds": [feature_id],
                "count": 1,
                "usageCount": 10,
                "note": "测试兑换码"
            }
            
            response = self.session.post(
                f"{self.base_url}/admin/pay-per-use/codes/generate",
                headers=self.admin_headers(),
                json=code_data
            )
            print(f"状态码: {response.status_code}")
            print(f"响应: {response.json()}")
        
        print()
        return feature_id
    
    def test_feature_checker(self):
        """测试功能检查API"""
        print("🔍 测试功能检查API")
        print("-" * 50)
        
        # 1. 检查功能黑名单
        print("1. 检查功能黑名单...")
        check_data = {
            "groupId": "123456789",
            "requestIdentifier": "test_id1"
        }
        
        response = self.session.post(
            f"{self.base_url}/api/check-feature-blacklist",
            headers={"Content-Type": "application/json"},
            json=check_data
        )
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        print()
        
        # 2. 检查功能白名单
        print("2. 检查功能白名单...")
        check_data = {
            "groupId": "123456789",
            "requestIdentifier": "premium_id"
        }
        
        response = self.session.post(
            f"{self.base_url}/api/check-feature-whitelist",
            headers={"Content-Type": "application/json"},
            json=check_data
        )
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        print()
    
    def cleanup(self, blacklist_id=None, whitelist_id=None, feature_id=None):
        """清理测试数据"""
        print("🧹 清理测试数据")
        print("-" * 50)
        
        # 删除创建的黑名单
        if blacklist_id:
            print(f"删除黑名单 ID: {blacklist_id}")
            response = self.session.post(
                f"{self.base_url}/admin/feature-blacklist/delete",
                headers=self.admin_headers(),
                json={"featureId": blacklist_id}
            )
            print(f"删除黑名单结果: {response.json()}")
        
        # 删除创建的白名单
        if whitelist_id:
            print(f"删除白名单 ID: {whitelist_id}")
            response = self.session.post(
                f"{self.base_url}/admin/feature-whitelist/delete",
                headers=self.admin_headers(),
                json={"featureId": whitelist_id}
            )
            print(f"删除白名单结果: {response.json()}")
        
        # 删除创建的按次付费功能
        if feature_id:
            print(f"删除按次付费功能 ID: {feature_id}")
            response = self.session.delete(
                f"{self.base_url}/admin/pay-per-use/features/delete/{feature_id}",
                headers=self.admin_headers()
            )
            print(f"删除功能结果: {response.json()}")
        
        print()
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始运行管理端API测试")
        print("=" * 60)
        print()
        
        try:
            # 测试连接
            response = self.session.get(f"{self.base_url}")
            print(f"✓ 连接到目标服务器: {self.base_url}")
        except Exception as e:
            print(f"❌ 无法连接到目标服务器: {e}")
            return
        
        blacklist_id = None
        whitelist_id = None
        feature_id = None
        
        try:
            # 运行各项测试
            blacklist_id = self.test_feature_blacklist()
            time.sleep(1)  # 避免请求过快
            
            whitelist_id = self.test_feature_whitelist()
            time.sleep(1)
            
            feature_id = self.test_pay_per_use()
            time.sleep(1)
            
            self.test_feature_checker()
            
        except Exception as e:
            print(f"❌ 测试过程中出现错误: {e}")
        
        finally:
            # 清理测试数据
            self.cleanup(blacklist_id, whitelist_id, feature_id)
        
        print("✅ 所有测试完成")

def main():
    """主函数"""
    print("管理端API测试示例")
    print("请确保主管理端服务已启动在 http://localhost:5000")
    print()
    
    # 这里需要修改为实际的管理员密码
    admin_password = input("请输入管理员密码: ").strip()
    if not admin_password:
        print("❌ 管理员密码不能为空")
        return
    
    # 创建测试器并运行测试
    tester = APITester(admin_password=admin_password)
    tester.run_all_tests()

if __name__ == '__main__':
    main()
