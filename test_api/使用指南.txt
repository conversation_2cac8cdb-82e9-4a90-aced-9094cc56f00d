========================================================================================
                              用户端API测试工具 - 快速使用指南
========================================================================================

📍 工具位置：test_api 文件夹
🌐 监听端口：9520
🎯 目标服务器：http://localhost:4000 (主管理端)

========================================================================================
                                    快速启动
========================================================================================

方法1：Windows用户 (推荐)
------------------------
双击运行：test_api/start.bat

方法2：命令行启动
-----------------
1. 打开命令行/终端
2. 进入test_api目录：cd test_api
3. 安装依赖：pip install -r requirements.txt
4. 启动程序：python app.py

方法3：使用启动脚本
------------------
Windows: python start.py
Linux/Mac: ./start.sh

========================================================================================
                                    访问地址
========================================================================================

🔗 测试工具主页：http://localhost:9520
📚 API接口文档：http://localhost:9520/api/docs

========================================================================================
                                  主要功能测试
========================================================================================

1️⃣ 功能检查测试
---------------
• 检查指定群组的黑名单状态
• 检查指定群组的白名单状态
• 获取群组机器人详细信息（含冲突检测）
• 获取群组授权摘要（快速查询版）
• 验证功能访问权限
• 📋 显示完整的请求格式和返回示例

2️⃣ 按次付费功能测试
------------------
• 功能使用：测试扣费机制
• 兑换码使用：测试兑换流程
• 📋 包含详细的API请求和响应格式

3️⃣ 授权检验测试
---------------
• 批量检查群聊授权状态和到期时间
• 检查用户拉群权限验证
• 检查QQ用户是否注册
• 获取已授权群聊列表
• 📋 完整的授权验证API文档

4️⃣ 简化API测试
--------------
• 简化版功能黑名单：GET方式，仅需群组ID
• 简化版功能白名单：GET方式，仅需群组ID
• 简化版授权检查：GET方式，仅需群组ID
• 简化版按次付费余额：GET方式，仅需群组ID
• 🚀 调用更简洁，响应更快速

========================================================================================
                                    使用步骤
========================================================================================

第一步：配置基础信息
------------------
1. 确认目标服务器地址（默认：http://localhost:4000）
2. 点击"保存配置"按钮保存设置

第二步：选择测试功能
------------------
点击对应的选项卡：
• 功能检查
• 按次付费
• 授权检验
• 简化API

第三步：执行测试
--------------
1. 填写必要的测试参数
2. 点击对应的测试按钮
3. 查看返回结果

第四步：查看结果
--------------
• 所有测试结果会显示在底部的"测试结果"区域
• 包含完整的请求信息和响应数据
• 可以复制、导出或清空结果
• 📋 每个API都显示标准的请求格式和返回示例

========================================================================================
                                  常用API端点
========================================================================================

用户端API（无需认证）：
• POST /api/check-feature-blacklist       - 检查功能黑名单
• POST /api/check-feature-whitelist       - 检查功能白名单
• GET  /api/group-bots-info/{group_id}    - 群组机器人详细信息
• GET  /api/group-authorization-summary/{group_id} - 群组授权摘要
• POST /api/pay-per-use/use               - 使用按次付费功能
• POST /api/pay-per-use/redeem            - 兑换兑换码
• POST /api/check-authorizations          - 批量检查群聊授权
• POST /api/check-authorization-with-user - 检查用户授权
• POST /api/check-user-by-qq              - 检查QQ用户注册
• GET  /api/authorized-groups             - 获取已授权群聊

简化API（仅需群组ID）：
• GET  /api/simple/feature-blacklist/{group_id}      - 简化版功能黑名单
• GET  /api/simple/feature-whitelist/{group_id}      - 简化版功能白名单
• GET  /api/simple/authorization/{group_id}          - 简化版授权检查
• GET  /api/simple/pay-per-use-balance/{group_id}    - 简化版按次付费余额

========================================================================================
                                    测试数据
========================================================================================

可以使用"填充示例数据"按钮自动填充测试数据，或手动输入：

功能检查示例：
• 群组ID：123456789
• 请求标识符（黑名单）：test_identifier
• 请求标识符（白名单）：premium_identifier

按次付费功能示例：
• 群组ID：123456789
• 功能代码：translation
• 请求备注：测试翻译功能
• 兑换码：TEST-CODE-123

授权检验示例：
• 群组ID列表：123456789,987654321
• 用户QQ号：123456789
• 套餐ID筛选：2（专业版）

简化API示例：
• 群组ID：123456789（所有简化API都使用相同的群组ID）

========================================================================================
                                    故障排除
========================================================================================

❌ 连接失败
-----------
• 确认主管理端服务已启动（端口4000）
• 检查防火墙设置
• 确认目标服务器地址正确

❌ 参数错误
-----------
• 检查必填字段是否已填写
• 确认数据格式（数字字段不能输入文字）



========================================================================================
                                  快捷键支持
========================================================================================

⌨️ Ctrl + Enter：执行当前选项卡的主要操作
⌨️ Ctrl + L：清空测试结果
⌨️ Ctrl + Shift + C：复制测试结果

========================================================================================
                                    注意事项
========================================================================================

⚠️ 仅用于开发测试环境，请勿在生产环境使用
⚠️ 使用前请备份重要数据
⚠️ 确保主管理端服务正常运行
⚠️ 测试数据可能会影响实际业务，请谨慎操作

========================================================================================

如有问题，请查看：
📖 完整文档：test_api/README.md
🌐 API文档：http://localhost:9520/api/docs

版本：1.0.0
更新时间：2024年
