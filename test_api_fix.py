#!/usr/bin/env python3
"""
测试时间修复的API脚本
"""

import requests
import json
from datetime import datetime

BASE_URL = 'http://localhost:56228'

def test_user_bindings_api():
    """测试用户绑定API的时间格式修复"""
    print("=" * 50)
    print("测试用户绑定API")
    print("=" * 50)
    
    # 首先尝试登录获取token
    login_data = {
        'username': 'MENG',
        'password': 'test123'  # 这里需要实际密码
    }
    
    try:
        # 登录
        login_response = requests.post(f'{BASE_URL}/login', json=login_data)
        print(f"登录响应状态: {login_response.status_code}")
        
        if login_response.status_code != 200:
            print("登录失败，尝试不同的密码...")
            # 尝试其他常见密码
            for password in ['123456', 'password', 'admin', '']:
                login_data['password'] = password
                login_response = requests.post(f'{BASE_URL}/login', json=login_data)
                if login_response.status_code == 200:
                    login_result = login_response.json()
                    if login_result.get('success'):
                        print(f"使用密码 '{password}' 登录成功")
                        break
            else:
                print("无法登录，跳过用户绑定API测试")
                return
        
        login_result = login_response.json()
        if not login_result.get('success'):
            print(f"登录失败: {login_result.get('message')}")
            return
            
        token = login_result.get('token')
        print(f"登录成功，获得token: {token[:20]}...")
        
        # 获取用户绑定
        headers = {'Authorization': f'Bearer {token}'}
        bindings_response = requests.get(f'{BASE_URL}/user/bindings', headers=headers)
        
        print(f"绑定API响应状态: {bindings_response.status_code}")
        
        if bindings_response.status_code == 200:
            bindings_result = bindings_response.json()
            if bindings_result.get('success'):
                bindings = bindings_result.get('data', [])
                print(f"找到 {len(bindings)} 个绑定记录")
                
                for i, binding in enumerate(bindings):
                    print(f"\n绑定 {i+1}:")
                    print(f"  群号: {binding.get('groupNumber')}")
                    print(f"  SKU类型: {binding.get('skuType')}")
                    print(f"  到期时间显示: {binding.get('expirationDate')}")
                    print(f"  ISO格式时间: {binding.get('expirationISOString')}")
                    
                    # 测试时间计算
                    if binding.get('expirationISOString'):
                        try:
                            expiration_time = datetime.fromisoformat(binding['expirationISOString'])
                            now = datetime.now()
                            diff = expiration_time - now
                            days_left = diff.days
                            
                            print(f"  解析后时间: {expiration_time}")
                            print(f"  当前时间: {now}")
                            print(f"  剩余天数: {days_left}")
                            print(f"  是否过期: {'是' if days_left < 0 else '否'}")
                            
                            if days_left < 0:
                                print(f"  ⚠️  这个绑定已过期 {abs(days_left)} 天")
                            elif days_left <= 7:
                                print(f"  ⚠️  这个绑定即将在 {days_left} 天内到期")
                            else:
                                print(f"  ✅ 这个绑定还有 {days_left} 天有效期")
                                
                        except Exception as e:
                            print(f"  ❌ 时间解析错误: {e}")
                    else:
                        print(f"  ❌ 缺少ISO格式时间")
            else:
                print(f"API返回失败: {bindings_result.get('message')}")
        else:
            print(f"API请求失败: {bindings_response.text}")
            
    except Exception as e:
        print(f"测试出错: {e}")

def test_time_parsing():
    """测试时间解析逻辑"""
    print("\n" + "=" * 50)
    print("测试时间解析逻辑")
    print("=" * 50)
    
    test_cases = [
        {
            'name': '标准ISO格式',
            'time_str': '2025-09-12T01:29:44'
        },
        {
            'name': '带Z后缀的ISO格式',
            'time_str': '2025-09-12T01:29:44Z'
        },
        {
            'name': '带毫秒和Z后缀',
            'time_str': '2025-09-12T01:29:44.000Z'
        },
        {
            'name': '数据库中的实际格式',
            'time_str': '2025-12-22T03:33:00.000Z'
        }
    ]
    
    for case in test_cases:
        print(f"\n测试: {case['name']}")
        print(f"原始时间: {case['time_str']}")
        
        try:
            # 模拟后端的时间处理逻辑
            time_str = case['time_str']
            if time_str.endswith('Z'):
                time_str = time_str[:-1]
            elif time_str.endswith('.000Z'):
                time_str = time_str[:-5]
            
            if 'T' in time_str:
                parsed_time = datetime.fromisoformat(time_str)
            else:
                parsed_time = datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
            
            # 格式化显示时间
            display_time = parsed_time.strftime('%Y/%m/%d %H:%M:%S')
            iso_string = parsed_time.isoformat()
            
            print(f"处理后时间: {time_str}")
            print(f"解析结果: {parsed_time}")
            print(f"显示格式: {display_time}")
            print(f"ISO格式: {iso_string}")
            
            # 计算剩余天数
            now = datetime.now()
            diff = parsed_time - now
            days_left = diff.days
            
            print(f"剩余天数: {days_left}")
            print(f"状态: {'已过期' if days_left < 0 else '有效'}")
            
        except Exception as e:
            print(f"❌ 解析失败: {e}")

if __name__ == '__main__':
    test_user_bindings_api()
    test_time_parsing()
