#!/usr/bin/env python3
"""
完整的时间修复测试
"""

import sqlite3
from datetime import datetime
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_data():
    """创建测试数据"""
    print("=" * 60)
    print("创建测试数据")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('database.sqlite')
        cursor = conn.cursor()
        
        # 插入一个测试记录，模拟用户提到的时间
        test_time = "2025-09-12T01:29:44.000Z"
        test_activation_code = "TEST_TIME_FIX_2025"
        test_group = "999999999"
        test_user = "test_user"
        
        # 检查是否已存在
        cursor.execute('SELECT COUNT(*) FROM bindings WHERE activation_code = ?', (test_activation_code,))
        if cursor.fetchone()[0] > 0:
            print(f"测试数据已存在: {test_activation_code}")
        else:
            cursor.execute('''
                INSERT INTO bindings (activation_code, sku_id, group_number, owner_username, expiration_time)
                VALUES (?, ?, ?, ?, ?)
            ''', (test_activation_code, 'test_sku', test_group, test_user, test_time))
            
            conn.commit()
            print(f"✅ 创建测试数据成功: {test_activation_code}")
            print(f"   群号: {test_group}")
            print(f"   到期时间: {test_time}")
        
        conn.close()
        return test_group, test_user, test_activation_code
        
    except Exception as e:
        print(f"❌ 创建测试数据失败: {e}")
        return None, None, None

def test_backend_processing(test_group):
    """测试后端处理逻辑"""
    print("\n" + "=" * 60)
    print("测试后端处理逻辑")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('database.sqlite')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 模拟用户绑定API的查询
        cursor.execute('''
            SELECT 
                b1.group_number,
                b1.sku_id,
                MAX(b1.expiration_time) as expiration_time,
                MIN(b1.bind_time) as first_bind_time,
                GROUP_CONCAT(b1.activation_code, ',') as activation_codes,
                COUNT(DISTINCT b1.activation_code) as renewal_count,
                GROUP_CONCAT(DISTINCT b2.owner_username) as all_contributors
            FROM bindings b1
            LEFT JOIN bindings b2 ON b1.group_number = b2.group_number AND b1.sku_id = b2.sku_id
            WHERE b1.group_number = ?
            GROUP BY b1.group_number, b1.sku_id
            ORDER BY b1.group_number, b1.sku_id
        ''', (test_group,))
        
        results = cursor.fetchall()
        
        if not results:
            print(f"❌ 没有找到测试群组 {test_group} 的数据")
            return None
        
        print(f"找到 {len(results)} 个绑定组")
        
        for row in results:
            print(f"\n处理绑定组:")
            print(f"  群号: {row['group_number']}")
            print(f"  原始到期时间: {row['expiration_time']}")
            
            # 应用后端时间处理逻辑
            activation_codes = row['activation_codes'].split(',') if row['activation_codes'] else []
            contributors = row['all_contributors'].split(',') if row['all_contributors'] else []
            
            # 处理到期时间格式
            expiration_time = row['expiration_time']
            expiration_date_display = '永久有效'
            expiration_iso_string = None
            
            if expiration_time:
                try:
                    # 解析ISO格式的时间
                    exp_str = expiration_time
                    if exp_str.endswith('Z'):
                        exp_str = exp_str[:-1]
                    elif exp_str.endswith('.000Z'):
                        exp_str = exp_str[:-5]
                    
                    if 'T' in exp_str:
                        expiry_dt = datetime.fromisoformat(exp_str)
                    else:
                        expiry_dt = datetime.strptime(exp_str, '%Y-%m-%d %H:%M:%S')
                    
                    # 格式化显示时间
                    expiration_date_display = expiry_dt.strftime('%Y/%m/%d %H:%M:%S')
                    # 提供ISO格式用于前端计算
                    expiration_iso_string = expiry_dt.isoformat()
                    
                    print(f"  处理后显示时间: {expiration_date_display}")
                    print(f"  ISO格式时间: {expiration_iso_string}")
                    
                    # 验证时间计算
                    now = datetime.now()
                    diff = expiry_dt - now
                    
                    print(f"  当前时间: {now}")
                    print(f"  剩余时间: {diff}")
                    print(f"  是否过期: {'是' if diff.total_seconds() < 0 else '否'}")
                    
                    if "01:29:44" in expiration_date_display:
                        print("  🎯 这是目标时间！")
                        if diff.total_seconds() > 0:
                            print("  ✅ 后端处理正确：时间未过期")
                        else:
                            print("  ❌ 后端处理错误：时间已过期")
                    
                except Exception as e:
                    print(f'  ❌ 解析到期时间失败: {e}, 原始时间: {expiration_time}')
                    expiration_date_display = str(expiration_time)
                    expiration_iso_string = expiration_time
            
            # 构建API响应数据
            binding_data = {
                'orderNumber': activation_codes[0] if activation_codes else '',
                'activationCodes': activation_codes,
                'groupNumber': row['group_number'],
                'skuType': 'Test SKU',
                'skuId': row['sku_id'],
                'expirationDate': expiration_date_display,
                'expirationISOString': expiration_iso_string,
                'bindTime': row['first_bind_time'],
                'renewalCount': row['renewal_count'] - 1,
                'contributors': contributors
            }
            
            print(f"  API响应数据:")
            print(f"    expirationDate: {binding_data['expirationDate']}")
            print(f"    expirationISOString: {binding_data['expirationISOString']}")
            
            conn.close()
            return binding_data
        
    except Exception as e:
        print(f"❌ 后端处理测试失败: {e}")
        return None

def test_frontend_logic(binding_data):
    """测试前端逻辑"""
    print("\n" + "=" * 60)
    print("测试前端逻辑")
    print("=" * 60)
    
    if not binding_data:
        print("❌ 没有绑定数据可测试")
        return
    
    print("模拟前端JavaScript逻辑:")
    print(f"  接收到的数据:")
    print(f"    expirationDate: {binding_data['expirationDate']}")
    print(f"    expirationISOString: {binding_data['expirationISOString']}")
    
    # 模拟前端过期判断逻辑
    expiration_date = binding_data['expirationDate']
    expiration_iso_string = binding_data['expirationISOString']
    
    # 当前的修复逻辑
    is_expired = (expiration_date and 
                  not str(expiration_date).__contains__('永久') and
                  expiration_iso_string and 
                  datetime.fromisoformat(expiration_iso_string) < datetime.now())
    
    print(f"  过期判断结果: {'已过期' if is_expired else '未过期'}")
    
    # 计算剩余天数
    if expiration_date and expiration_date != '永久' and expiration_iso_string:
        try:
            expiration_time = datetime.fromisoformat(expiration_iso_string)
            now = datetime.now()
            diff_time = expiration_time.timestamp() * 1000 - now.timestamp() * 1000
            diff_days = diff_time / (1000 * 60 * 60 * 24)
            
            print(f"  剩余天数计算: {diff_days:.2f} 天")
            
            if "01:29:44" in expiration_date:
                print("  🎯 这是目标时间！")
                if not is_expired and diff_days > 0:
                    print("  ✅ 前端逻辑正确：显示未过期且有剩余天数")
                else:
                    print("  ❌ 前端逻辑错误：显示已过期或剩余天数为负")
                    
        except Exception as e:
            print(f"  ❌ 前端时间计算失败: {e}")

def cleanup_test_data(test_activation_code):
    """清理测试数据"""
    print("\n" + "=" * 60)
    print("清理测试数据")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('database.sqlite')
        cursor = conn.cursor()
        
        cursor.execute('DELETE FROM bindings WHERE activation_code = ?', (test_activation_code,))
        deleted_count = cursor.rowcount
        
        conn.commit()
        conn.close()
        
        if deleted_count > 0:
            print(f"✅ 清理测试数据成功，删除了 {deleted_count} 条记录")
        else:
            print("ℹ️ 没有找到需要清理的测试数据")
            
    except Exception as e:
        print(f"❌ 清理测试数据失败: {e}")

if __name__ == '__main__':
    print("开始完整的时间修复测试...")
    
    # 创建测试数据
    test_group, test_user, test_activation_code = create_test_data()
    
    if test_group:
        # 测试后端处理
        binding_data = test_backend_processing(test_group)
        
        # 测试前端逻辑
        test_frontend_logic(binding_data)
        
        # 清理测试数据
        cleanup_test_data(test_activation_code)
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
