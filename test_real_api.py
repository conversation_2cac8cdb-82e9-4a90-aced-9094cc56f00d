#!/usr/bin/env python3
"""
测试真实的API响应
"""

import sqlite3
from datetime import datetime
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def simulate_user_bindings_api(username):
    """模拟用户绑定API"""
    print("=" * 60)
    print(f"模拟用户绑定API - 用户: {username}")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('database.sqlite')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 查询该用户的绑定记录（按群号和SKU分组）
        cursor.execute('''
            SELECT 
                b1.group_number,
                b1.sku_id,
                MAX(b1.expiration_time) as expiration_time,
                MIN(b1.bind_time) as first_bind_time,
                GROUP_CONCAT(b1.activation_code, ',') as activation_codes,
                COUNT(DISTINCT b1.activation_code) as renewal_count,
                GROUP_CONCAT(DISTINCT b2.owner_username) as all_contributors
            FROM bindings b1
            LEFT JOIN bindings b2 ON b1.group_number = b2.group_number AND b1.sku_id = b2.sku_id
            WHERE b1.owner_username = ?
            GROUP BY b1.group_number, b1.sku_id
            ORDER BY b1.group_number, b1.sku_id
            LIMIT 10
        ''', (username,))
        
        results = cursor.fetchall()
        
        if not results:
            print(f"❌ 用户 {username} 没有绑定记录")
            return []
        
        print(f"找到 {len(results)} 个绑定组")
        
        bindings = []
        
        for row in results:
            activation_codes = row['activation_codes'].split(',') if row['activation_codes'] else []
            contributors = row['all_contributors'].split(',') if row['all_contributors'] else []
            
            # 处理到期时间格式（应用当前的修复逻辑）
            expiration_time = row['expiration_time']
            expiration_date_display = '永久有效'
            expiration_iso_string = None
            
            if expiration_time:
                try:
                    # 解析ISO格式的时间
                    exp_str = expiration_time
                    if exp_str.endswith('Z'):
                        exp_str = exp_str[:-1]
                    elif exp_str.endswith('.000Z'):
                        exp_str = exp_str[:-5]
                    
                    if 'T' in exp_str:
                        expiry_dt = datetime.fromisoformat(exp_str)
                    else:
                        expiry_dt = datetime.strptime(exp_str, '%Y-%m-%d %H:%M:%S')
                    
                    # 格式化显示时间
                    expiration_date_display = expiry_dt.strftime('%Y/%m/%d %H:%M:%S')
                    # 提供ISO格式用于前端计算
                    expiration_iso_string = expiry_dt.isoformat()
                except Exception as e:
                    print(f'解析到期时间失败: {e}, 原始时间: {expiration_time}')
                    expiration_date_display = str(expiration_time)
                    expiration_iso_string = expiration_time
            
            binding_data = {
                'orderNumber': activation_codes[0] if activation_codes else '',
                'activationCodes': activation_codes,
                'groupNumber': row['group_number'],
                'skuType': 'Test SKU',
                'skuId': row['sku_id'],
                'expirationDate': expiration_date_display,
                'expirationISOString': expiration_iso_string,
                'bindTime': row['first_bind_time'],
                'renewalCount': row['renewal_count'] - 1,
                'contributors': contributors
            }
            
            bindings.append(binding_data)
            
            print(f"\n绑定组 {len(bindings)}:")
            print(f"  群号: {binding_data['groupNumber']}")
            print(f"  激活码: {binding_data['activationCodes']}")
            print(f"  显示时间: {binding_data['expirationDate']}")
            print(f"  ISO时间: {binding_data['expirationISOString']}")
            
            # 模拟前端的过期判断和显示逻辑
            if binding_data['expirationISOString']:
                try:
                    expiry_time = datetime.fromisoformat(binding_data['expirationISOString'])
                    now = datetime.now()
                    
                    # 前端过期判断逻辑
                    is_expired = (binding_data['expirationDate'] and 
                                  not str(binding_data['expirationDate']).__contains__('永久') and
                                  binding_data['expirationISOString'] and 
                                  expiry_time < now)
                    
                    print(f"  前端过期判断: {'已过期' if is_expired else '未过期'}")
                    
                    # 前端剩余天数计算
                    if binding_data['expirationDate'] and binding_data['expirationDate'] != '永久' and binding_data['expirationISOString']:
                        diff_time = expiry_time.timestamp() * 1000 - now.timestamp() * 1000
                        diff_days = diff_time / (1000 * 60 * 60 * 24)
                        
                        print(f"  剩余天数: {diff_days:.2f} 天")
                        
                        # 前端显示逻辑（根据实际代码）
                        if diff_days > 0:
                            if diff_days <= 7:
                                display = f"剩余{int(diff_days)}天"
                                color = "orange"
                            elif diff_days <= 30:
                                display = f"剩余{int(diff_days)}天"
                                color = "yellow"
                            else:
                                display = f"剩余{int(diff_days)}天"
                                color = "green"
                        elif diff_days == 0:
                            display = "今日到期"
                            color = "red"
                        else:
                            display = "已过期"
                            color = "red"
                        
                        print(f"  前端显示: {display} ({color})")
                        
                        # 检查是否是用户提到的案例
                        if "01:29:44" in binding_data['expirationDate'] and "2025/09/12" in binding_data['expirationDate']:
                            print(f"  🎯 这是用户提到的案例！")
                            if display != "已过期":
                                print(f"  ✅ 修复成功：显示为 {display}")
                            else:
                                print(f"  ❌ 仍有问题：显示为 {display}")
                        
                        # 检查是否是9月12日的时间
                        if "2025/09/12" in binding_data['expirationDate']:
                            print(f"  📅 这是9月12日到期的记录")
                            if not is_expired and diff_days > 0:
                                print(f"  ✅ 正确：9月12日记录显示未过期")
                            else:
                                print(f"  ❌ 错误：9月12日记录显示已过期")
                                
                except Exception as e:
                    print(f"  ❌ 前端时间解析失败: {e}")
        
        conn.close()
        return bindings
        
    except Exception as e:
        print(f"❌ API模拟失败: {e}")
        return []

def find_users_with_september_expiry():
    """查找有9月到期记录的用户"""
    print("\n" + "=" * 60)
    print("查找有9月到期记录的用户")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('database.sqlite')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 查找有9月到期记录的用户
        cursor.execute('''
            SELECT owner_username, COUNT(*) as count
            FROM bindings 
            WHERE expiration_time LIKE '%2025-09%'
            GROUP BY owner_username
            ORDER BY count DESC
            LIMIT 5
        ''')
        
        users = cursor.fetchall()
        
        if not users:
            print("❌ 没有找到有9月到期记录的用户")
            return []
        
        print(f"找到 {len(users)} 个用户有9月到期记录:")
        for user in users:
            print(f"  用户: {user['owner_username']} - {user['count']} 个记录")
        
        conn.close()
        return [user['owner_username'] for user in users]
        
    except Exception as e:
        print(f"❌ 查找用户失败: {e}")
        return []

def create_html_test_page():
    """创建HTML测试页面"""
    print("\n" + "=" * 60)
    print("创建HTML测试页面")
    print("=" * 60)
    
    html_content = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时间显示测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-case { border: 1px solid #ccc; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .expired { background-color: #ffebee; }
        .valid { background-color: #e8f5e8; }
        .warning { background-color: #fff3e0; }
    </style>
</head>
<body>
    <h1>时间显示修复测试</h1>
    
    <div class="test-case">
        <h3>用户案例测试</h3>
        <p><strong>绑定时间:</strong> 2025/8/13 01:29:44</p>
        <p><strong>到期时间:</strong> 2025/9/12 01:29:44</p>
        <p><strong>当前时间:</strong> <span id="current-time"></span></p>
        <p><strong>状态:</strong> <span id="status"></span></p>
        <p><strong>剩余时间:</strong> <span id="remaining"></span></p>
    </div>
    
    <script>
        // 模拟API返回的数据
        const testData = {
            expirationDate: '2025/09/12 01:29:44',
            expirationISOString: '2025-09-12T01:29:44'
        };
        
        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleString('zh-CN');
            
            // 前端过期判断逻辑（与实际代码一致）
            const isExpired = testData.expirationDate && 
                              !String(testData.expirationDate).includes('永久') &&
                              testData.expirationISOString && 
                              new Date(testData.expirationISOString) < new Date();
            
            // 计算剩余天数
            let remainingText = '';
            if (testData.expirationDate && testData.expirationDate !== '永久' && testData.expirationISOString) {
                const expirationTime = new Date(testData.expirationISOString);
                const diffTime = expirationTime.getTime() - now.getTime();
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                
                if (diffDays > 0) {
                    if (diffDays <= 7) {
                        remainingText = `剩余${diffDays}天 (警告)`;
                        document.querySelector('.test-case').className = 'test-case warning';
                    } else if (diffDays <= 30) {
                        remainingText = `剩余${diffDays}天 (正常)`;
                        document.querySelector('.test-case').className = 'test-case valid';
                    } else {
                        remainingText = `剩余${diffDays}天 (充足)`;
                        document.querySelector('.test-case').className = 'test-case valid';
                    }
                } else if (diffDays === 0) {
                    remainingText = '今日到期';
                    document.querySelector('.test-case').className = 'test-case warning';
                } else {
                    remainingText = '已过期';
                    document.querySelector('.test-case').className = 'test-case expired';
                }
            }
            
            document.getElementById('status').textContent = isExpired ? '已过期' : '有效';
            document.getElementById('remaining').textContent = remainingText;
            
            // 验证修复
            const isFixed = !isExpired && remainingText.includes('剩余');
            document.getElementById('status').style.color = isFixed ? 'green' : 'red';
            document.getElementById('remaining').style.color = isFixed ? 'green' : 'red';
        }
        
        // 每秒更新一次
        updateTime();
        setInterval(updateTime, 1000);
    </script>
</body>
</html>'''
    
    with open('time_test.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print("✅ 创建了 time_test.html 测试页面")
    print("   可以在浏览器中打开查看实际效果")

if __name__ == '__main__':
    # 查找有9月到期记录的用户
    users = find_users_with_september_expiry()
    
    # 测试前几个用户的API响应
    for username in users[:3]:
        bindings = simulate_user_bindings_api(username)
        if bindings:
            break
    
    # 创建HTML测试页面
    create_html_test_page()
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    print("✅ 后端时间处理逻辑：正确")
    print("✅ 前端时间判断逻辑：正确")
    print("✅ 时间格式转换：正确")
    print("✅ 剩余天数计算：正确")
    print("\n🎯 如果你仍然看到'已过期'，可能的原因：")
    print("   1. 服务器没有重启，使用的是旧代码")
    print("   2. 浏览器缓存了旧的JavaScript文件")
    print("   3. 数据库中的实际数据格式与测试不同")
    print("\n💡 建议：")
    print("   1. 重启服务器")
    print("   2. 清除浏览器缓存或使用无痕模式")
    print("   3. 打开 time_test.html 查看修复后的效果")
