<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>剩余天数测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">剩余天数显示测试</h1>
        
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h2 class="text-lg font-semibold mb-4">测试数据</h2>
            <div id="testResults" class="space-y-4">
                <!-- 测试结果将在这里显示 -->
            </div>
        </div>
        
        <div class="bg-white p-6 rounded-lg shadow">
            <h2 class="text-lg font-semibold mb-4">实际绑定数据测试</h2>
            <button id="testRealData" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded">
                测试实际数据
            </button>
            <div id="realDataResults" class="mt-4 space-y-2">
                <!-- 实际数据测试结果 -->
            </div>
        </div>
    </div>

    <script>
        // 剩余天数计算函数（与前端代码一致）
        function calculateRemainingDays(expirationISOString) {
            if (!expirationISOString) return null;
            
            const expirationTime = new Date(expirationISOString);
            const now = new Date();
            const diffTime = expirationTime.getTime() - now.getTime();
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            
            let remainingDaysHtml = '';
            if (diffDays > 0) {
                if (diffDays <= 7) {
                    remainingDaysHtml = `<span class="ml-1 px-2 py-0.5 text-xs rounded-full bg-orange-100 text-orange-700">剩余${diffDays}天</span>`;
                } else if (diffDays <= 30) {
                    remainingDaysHtml = `<span class="ml-1 px-2 py-0.5 text-xs rounded-full bg-yellow-100 text-yellow-700">剩余${diffDays}天</span>`;
                } else {
                    remainingDaysHtml = `<span class="ml-1 px-2 py-0.5 text-xs rounded-full bg-green-100 text-green-700">剩余${diffDays}天</span>`;
                }
            } else if (diffDays === 0) {
                remainingDaysHtml = `<span class="ml-1 px-2 py-0.5 text-xs rounded-full bg-red-100 text-red-700">今日到期</span>`;
            } else {
                remainingDaysHtml = `<span class="ml-1 px-2 py-0.5 text-xs rounded-full bg-red-100 text-red-700">已过期</span>`;
            }
            
            return { diffDays, remainingDaysHtml };
        }

        // 测试数据
        const testData = [
            {
                name: '今日到期',
                expirationISOString: new Date().toISOString().slice(0, 16)
            },
            {
                name: '明天到期',
                expirationISOString: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().slice(0, 16)
            },
            {
                name: '3天后到期',
                expirationISOString: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString().slice(0, 16)
            },
            {
                name: '7天后到期',
                expirationISOString: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().slice(0, 16)
            },
            {
                name: '15天后到期',
                expirationISOString: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString().slice(0, 16)
            },
            {
                name: '60天后到期',
                expirationISOString: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString().slice(0, 16)
            },
            {
                name: '昨天已过期',
                expirationISOString: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().slice(0, 16)
            }
        ];

        // 运行测试
        function runTests() {
            const resultsDiv = document.getElementById('testResults');
            
            testData.forEach(test => {
                const result = calculateRemainingDays(test.expirationISOString);
                const div = document.createElement('div');
                div.className = 'p-3 border rounded';
                div.innerHTML = `
                    <div class="font-medium">${test.name}</div>
                    <div class="text-sm text-gray-600">到期时间: ${test.expirationISOString}</div>
                    <div class="text-sm">剩余天数: ${result ? result.diffDays : 'N/A'}</div>
                    <div class="mt-2">显示效果: ${result ? result.remainingDaysHtml : '无'}</div>
                `;
                resultsDiv.appendChild(div);
            });
        }

        // 测试实际数据
        document.getElementById('testRealData').addEventListener('click', async () => {
            try {
                const response = await fetch('/admin/bindings', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Admin-Password': '123'
                    },
                    body: JSON.stringify({})
                });
                
                const data = await response.json();
                const resultsDiv = document.getElementById('realDataResults');
                resultsDiv.innerHTML = '';
                
                if (data.success && data.data) {
                    // 取前5个绑定进行测试
                    data.data.slice(0, 5).forEach(binding => {
                        const result = calculateRemainingDays(binding.expirationISOString);
                        const div = document.createElement('div');
                        div.className = 'p-3 border rounded text-sm';
                        div.innerHTML = `
                            <div class="font-medium">群号: ${binding.groupNumber}</div>
                            <div>到期时间: ${binding.expirationDate}</div>
                            <div>ISO时间: ${binding.expirationISOString || '无'}</div>
                            <div class="mt-1">显示效果: ${result ? result.remainingDaysHtml : '无'}</div>
                        `;
                        resultsDiv.appendChild(div);
                    });
                } else {
                    resultsDiv.innerHTML = '<div class="text-red-600">获取数据失败</div>';
                }
            } catch (error) {
                document.getElementById('realDataResults').innerHTML = 
                    `<div class="text-red-600">错误: ${error.message}</div>`;
            }
        });

        // 页面加载时运行测试
        runTests();
    </script>
</body>
</html>
