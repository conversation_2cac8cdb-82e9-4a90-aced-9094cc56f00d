#!/usr/bin/env python3
"""
测试特定时间问题：2025/9/12 01:29:44
"""

from datetime import datetime
import json

def test_specific_time_issue():
    """测试用户提到的具体时间问题"""
    
    print("=" * 60)
    print("测试特定时间问题：2025/9/12 01:29:44")
    print("=" * 60)
    
    # 模拟数据库中可能的时间格式
    possible_formats = [
        "2025-09-12T01:29:44.000Z",  # 标准ISO格式
        "2025-09-12T01:29:44Z",      # 无毫秒的ISO格式
        "2025-09-12 01:29:44",       # 本地时间格式
        "2025/9/12 01:29:44",        # 用户看到的格式
    ]
    
    print("测试不同的时间格式:")
    print("-" * 40)
    
    for i, time_str in enumerate(possible_formats, 1):
        print(f"\n{i}. 原始格式: {time_str}")
        
        try:
            # 应用当前的时间处理逻辑
            expiration_time = time_str
            expiration_date_display = '永久有效'
            expiration_iso_string = None
            
            if expiration_time:
                # 解析ISO格式的时间
                exp_str = expiration_time
                if exp_str.endswith('Z'):
                    exp_str = exp_str[:-1]
                elif exp_str.endswith('.000Z'):
                    exp_str = exp_str[:-5]
                
                if 'T' in exp_str:
                    expiry_dt = datetime.fromisoformat(exp_str)
                else:
                    # 处理 2025/9/12 格式
                    if '/' in exp_str:
                        # 转换为标准格式
                        parts = exp_str.split(' ')
                        if len(parts) == 2:
                            date_part = parts[0].replace('/', '-')
                            # 确保月份和日期是两位数
                            date_components = date_part.split('-')
                            if len(date_components) == 3:
                                year, month, day = date_components
                                month = month.zfill(2)
                                day = day.zfill(2)
                                date_part = f"{year}-{month}-{day}"
                            time_part = parts[1]
                            exp_str = f"{date_part} {time_part}"
                    
                    expiry_dt = datetime.strptime(exp_str, '%Y-%m-%d %H:%M:%S')
                
                # 格式化显示时间
                expiration_date_display = expiry_dt.strftime('%Y/%m/%d %H:%M:%S')
                # 提供ISO格式用于前端计算
                expiration_iso_string = expiry_dt.isoformat()
                
                print(f"   解析后时间: {expiry_dt}")
                print(f"   显示格式: {expiration_date_display}")
                print(f"   ISO格式: {expiration_iso_string}")
                
                # 计算剩余时间
                now = datetime.now()
                diff = expiry_dt - now
                days_left = diff.days
                hours_left = diff.seconds // 3600
                
                print(f"   当前时间: {now}")
                print(f"   剩余天数: {days_left}")
                print(f"   剩余小时: {hours_left}")
                print(f"   是否过期: {'是' if diff.total_seconds() < 0 else '否'}")
                
                # 模拟前端JavaScript的判断
                js_is_expired = diff.total_seconds() < 0
                print(f"   前端判断: {'已过期' if js_is_expired else '未过期'}")
                
                if "01:29:44" in expiration_date_display:
                    print("   🎯 这是目标时间！")
                    if not js_is_expired:
                        print("   ✅ 修复成功：正确显示为未过期")
                    else:
                        print("   ❌ 仍有问题：错误显示为已过期")
                
        except Exception as e:
            print(f"   ❌ 解析失败: {e}")

def test_frontend_javascript_logic():
    """测试前端JavaScript逻辑"""
    
    print("\n" + "=" * 60)
    print("测试前端JavaScript逻辑")
    print("=" * 60)
    
    # 模拟API返回的数据
    mock_api_response = {
        'expirationDate': '2025/09/12 01:29:44',
        'expirationISOString': '2025-09-12T01:29:44'
    }
    
    print("模拟API响应:")
    print(f"  expirationDate: {mock_api_response['expirationDate']}")
    print(f"  expirationISOString: {mock_api_response['expirationISOString']}")
    
    # 模拟前端的过期判断逻辑
    print("\n前端过期判断逻辑:")
    
    # 当前的逻辑（修复后）
    expiration_date = mock_api_response['expirationDate']
    expiration_iso_string = mock_api_response['expirationISOString']
    
    # 检查是否有ISO格式时间
    if expiration_iso_string:
        try:
            expiry_time = datetime.fromisoformat(expiration_iso_string)
            now = datetime.now()
            is_expired = expiry_time < now
            
            print(f"  使用ISO时间判断: {'已过期' if is_expired else '未过期'}")
            print(f"  到期时间: {expiry_time}")
            print(f"  当前时间: {now}")
            print(f"  时间差: {expiry_time - now}")
            
            if not is_expired:
                print("  ✅ 修复成功：正确判断为未过期")
            else:
                print("  ❌ 仍有问题：错误判断为已过期")
                
        except Exception as e:
            print(f"  ❌ ISO时间解析失败: {e}")
    
    # 旧的逻辑（可能有问题的）
    print("\n如果使用显示格式时间判断（旧逻辑）:")
    try:
        # 尝试直接解析显示格式
        expiry_time = datetime.strptime(expiration_date, '%Y/%m/%d %H:%M:%S')
        now = datetime.now()
        is_expired = expiry_time < now
        
        print(f"  使用显示时间判断: {'已过期' if is_expired else '未过期'}")
        print(f"  到期时间: {expiry_time}")
        print(f"  当前时间: {now}")
        
        if not is_expired:
            print("  ✅ 显示格式判断也正确")
        else:
            print("  ❌ 显示格式判断错误")
            
    except Exception as e:
        print(f"  ❌ 显示格式解析失败: {e}")

def create_test_scenario():
    """创建测试场景"""
    
    print("\n" + "=" * 60)
    print("创建完整测试场景")
    print("=" * 60)
    
    # 模拟完整的数据流
    print("1. 数据库存储的时间格式:")
    db_time = "2025-09-12T01:29:44.000Z"
    print(f"   {db_time}")
    
    print("\n2. 后端处理后的API响应:")
    # 应用后端处理逻辑
    exp_str = db_time
    if exp_str.endswith('Z'):
        exp_str = exp_str[:-1]
    elif exp_str.endswith('.000Z'):
        exp_str = exp_str[:-5]
    
    expiry_dt = datetime.fromisoformat(exp_str)
    expiration_date_display = expiry_dt.strftime('%Y/%m/%d %H:%M:%S')
    expiration_iso_string = expiry_dt.isoformat()
    
    api_response = {
        'expirationDate': expiration_date_display,
        'expirationISOString': expiration_iso_string
    }
    
    print(f"   expirationDate: {api_response['expirationDate']}")
    print(f"   expirationISOString: {api_response['expirationISOString']}")
    
    print("\n3. 前端JavaScript判断:")
    # 前端逻辑
    if api_response['expirationISOString']:
        expiry_time = datetime.fromisoformat(api_response['expirationISOString'])
        now = datetime.now()
        is_expired = expiry_time < now
        
        print(f"   到期时间: {expiry_time}")
        print(f"   当前时间: {now}")
        print(f"   剩余时间: {expiry_time - now}")
        print(f"   判断结果: {'已过期' if is_expired else '未过期'}")
        
        if api_response['expirationDate'] == '2025/09/12 01:29:44':
            print("   🎯 这是用户提到的目标时间！")
            if not is_expired:
                print("   ✅ 完整流程测试成功：正确显示为未过期")
            else:
                print("   ❌ 完整流程测试失败：错误显示为已过期")

if __name__ == '__main__':
    test_specific_time_issue()
    test_frontend_javascript_logic()
    create_test_scenario()
