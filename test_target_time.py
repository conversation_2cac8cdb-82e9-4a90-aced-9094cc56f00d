#!/usr/bin/env python3
"""
测试用户提到的具体时间：2025/9/12 01:29:44
"""

from datetime import datetime

def test_target_time():
    """测试用户提到的具体时间"""
    print("=" * 60)
    print("测试目标时间：2025/9/12 01:29:44")
    print("=" * 60)
    
    # 模拟数据库中存储的时间格式
    db_time = "2025-09-12T01:29:44.000Z"
    
    print(f"数据库存储格式: {db_time}")
    
    # 应用后端处理逻辑
    expiration_time = db_time
    expiration_date_display = '永久有效'
    expiration_iso_string = None
    
    if expiration_time:
        try:
            # 解析ISO格式的时间
            exp_str = expiration_time
            if exp_str.endswith('Z'):
                exp_str = exp_str[:-1]
            elif exp_str.endswith('.000Z'):
                exp_str = exp_str[:-5]
            
            if 'T' in exp_str:
                expiry_dt = datetime.fromisoformat(exp_str)
            else:
                expiry_dt = datetime.strptime(exp_str, '%Y-%m-%d %H:%M:%S')
            
            # 格式化显示时间
            expiration_date_display = expiry_dt.strftime('%Y/%m/%d %H:%M:%S')
            # 提供ISO格式用于前端计算
            expiration_iso_string = expiry_dt.isoformat()
            
            print(f"后端处理结果:")
            print(f"  显示时间: {expiration_date_display}")
            print(f"  ISO时间: {expiration_iso_string}")
            
            # 验证时间计算
            now = datetime.now()
            diff = expiry_dt - now
            
            print(f"\n时间计算:")
            print(f"  到期时间: {expiry_dt}")
            print(f"  当前时间: {now}")
            print(f"  时间差: {diff}")
            print(f"  剩余天数: {diff.days}")
            print(f"  剩余小时: {diff.seconds // 3600}")
            print(f"  是否过期: {'是' if diff.total_seconds() < 0 else '否'}")
            
            # 模拟前端过期判断
            is_expired = (expiration_date_display and 
                          not str(expiration_date_display).__contains__('永久') and
                          expiration_iso_string and 
                          datetime.fromisoformat(expiration_iso_string) < datetime.now())
            
            print(f"\n前端判断:")
            print(f"  过期状态: {'已过期' if is_expired else '未过期'}")
            
            # 计算剩余天数（前端逻辑）
            if expiration_date_display and expiration_date_display != '永久' and expiration_iso_string:
                expiration_time_js = datetime.fromisoformat(expiration_iso_string)
                now_js = datetime.now()
                diff_time = expiration_time_js.timestamp() * 1000 - now_js.timestamp() * 1000
                diff_days = diff_time / (1000 * 60 * 60 * 24)
                
                print(f"  剩余天数: {diff_days:.2f} 天")
            
            # 验证修复效果
            print(f"\n🎯 修复验证:")
            if expiration_date_display == '2025/09/12 01:29:44':
                print(f"  ✅ 显示时间格式正确: {expiration_date_display}")
            else:
                print(f"  ❌ 显示时间格式错误: {expiration_date_display}")
            
            if not is_expired and diff.total_seconds() > 0:
                print(f"  ✅ 过期判断正确: 未过期")
            else:
                print(f"  ❌ 过期判断错误: 已过期")
            
            if diff.days >= 1:
                print(f"  ✅ 剩余时间正确: {diff.days} 天")
            else:
                print(f"  ❌ 剩余时间错误: {diff.days} 天")
            
            print(f"\n📊 总结:")
            print(f"  问题: 显示2025/9/12 01:29:44，但9.10就显示已到期")
            print(f"  修复: 使用ISO格式时间进行准确计算")
            print(f"  结果: {'✅ 修复成功' if not is_expired and diff.days >= 1 else '❌ 仍有问题'}")
            
        except Exception as e:
            print(f'❌ 解析到期时间失败: {e}')

def test_api_response():
    """测试完整的API响应"""
    print("\n" + "=" * 60)
    print("测试完整的API响应")
    print("=" * 60)
    
    # 模拟完整的API响应数据
    api_response = {
        'success': True,
        'data': [
            {
                'orderNumber': 'TEST_ORDER_123',
                'activationCodes': ['TEST_ORDER_123'],
                'groupNumber': '123456789',
                'skuType': 'Test SKU',
                'skuId': 'test_sku',
                'expirationDate': '2025/09/12 01:29:44',
                'expirationISOString': '2025-09-12T01:29:44',
                'bindTime': '2024-09-10 10:00:00',
                'renewalCount': 0,
                'contributors': ['test_user']
            }
        ]
    }
    
    print("API响应数据:")
    binding = api_response['data'][0]
    for key, value in binding.items():
        print(f"  {key}: {value}")
    
    # 模拟前端处理
    print(f"\n前端处理:")
    expiration_date = binding['expirationDate']
    expiration_iso_string = binding['expirationISOString']
    
    print(f"  接收到显示时间: {expiration_date}")
    print(f"  接收到ISO时间: {expiration_iso_string}")
    
    # 前端过期判断逻辑
    is_expired = (expiration_date and 
                  not str(expiration_date).__contains__('永久') and
                  expiration_iso_string and 
                  datetime.fromisoformat(expiration_iso_string) < datetime.now())
    
    print(f"  过期判断: {'已过期' if is_expired else '未过期'}")
    
    # 计算剩余天数
    if expiration_date and expiration_date != '永久' and expiration_iso_string:
        try:
            expiration_time = datetime.fromisoformat(expiration_iso_string)
            now = datetime.now()
            diff_time = expiration_time.timestamp() * 1000 - now.timestamp() * 1000
            diff_days = diff_time / (1000 * 60 * 60 * 24)
            
            print(f"  剩余天数: {diff_days:.2f} 天")
            
            # 显示状态
            if diff_days > 0:
                print(f"  显示状态: 有效 ({diff_days:.1f}天后到期)")
            else:
                print(f"  显示状态: 已过期")
            
            # 验证修复
            if expiration_date == '2025/09/12 01:29:44' and not is_expired and diff_days > 0:
                print(f"  🎯 修复验证: ✅ 成功！时间正确显示为未过期")
            else:
                print(f"  🎯 修复验证: ❌ 失败！时间仍显示有问题")
                
        except Exception as e:
            print(f"  ❌ 时间计算失败: {e}")

def test_edge_cases():
    """测试边界情况"""
    print("\n" + "=" * 60)
    print("测试边界情况")
    print("=" * 60)
    
    test_cases = [
        ("2025-09-12T01:29:44.000Z", "标准ISO格式"),
        ("2025-09-12T01:29:44Z", "无毫秒ISO格式"),
        ("2025-09-12 01:29:44", "本地时间格式"),
        ("2025-09-11T23:59:59.000Z", "即将到期"),
        ("2025-09-10T23:59:59.000Z", "刚好到期"),
        ("2025-09-10T01:29:44.000Z", "已过期"),
    ]
    
    for time_str, description in test_cases:
        print(f"\n测试: {description}")
        print(f"时间: {time_str}")
        
        try:
            # 解析时间
            exp_str = time_str
            if exp_str.endswith('Z'):
                exp_str = exp_str[:-1]
            elif exp_str.endswith('.000Z'):
                exp_str = exp_str[:-5]
            
            if 'T' in exp_str:
                expiry_dt = datetime.fromisoformat(exp_str)
            else:
                expiry_dt = datetime.strptime(exp_str, '%Y-%m-%d %H:%M:%S')
            
            # 格式化
            display_time = expiry_dt.strftime('%Y/%m/%d %H:%M:%S')
            iso_time = expiry_dt.isoformat()
            
            # 判断过期
            now = datetime.now()
            is_expired = expiry_dt < now
            diff = expiry_dt - now
            
            print(f"  显示: {display_time}")
            print(f"  状态: {'已过期' if is_expired else '未过期'}")
            print(f"  剩余: {diff.days}天 {diff.seconds//3600}小时")
            
        except Exception as e:
            print(f"  ❌ 处理失败: {e}")

if __name__ == '__main__':
    test_target_time()
    test_api_response()
    test_edge_cases()
