<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时间修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-result {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>时间修复测试</h1>
    <p>这个页面用于测试到期时间显示和判断的修复效果。</p>
    
    <div>
        <button onclick="testUserBindings()">测试用户绑定API</button>
        <button onclick="testTimeCalculation()">测试时间计算</button>
        <button onclick="clearResults()">清除结果</button>
    </div>
    
    <div id="results"></div>

    <script>
        function addResult(title, content, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <div>${content}</div>
            `;
            resultsDiv.appendChild(resultDiv);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testUserBindings() {
            try {
                // 模拟登录获取token（这里需要实际的用户凭据）
                const loginResponse = await fetch('/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'MENG',
                        password: 'test123' // 这里需要实际密码
                    })
                });

                if (!loginResponse.ok) {
                    addResult('登录失败', '无法获取用户token，请检查用户名和密码', 'error');
                    return;
                }

                const loginResult = await loginResponse.json();
                if (!loginResult.success) {
                    addResult('登录失败', loginResult.message, 'error');
                    return;
                }

                const token = loginResult.token;

                // 测试用户绑定API
                const bindingsResponse = await fetch('/user/bindings', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const bindingsResult = await bindingsResponse.json();

                if (bindingsResponse.ok && bindingsResult.success) {
                    const bindings = bindingsResult.data;
                    
                    addResult('用户绑定API测试成功', `
                        <p>找到 ${bindings.length} 个绑定记录</p>
                        <pre>${JSON.stringify(bindings, null, 2)}</pre>
                    `, 'success');

                    // 测试每个绑定的时间格式
                    bindings.forEach((binding, index) => {
                        testBindingTime(binding, index);
                    });
                } else {
                    addResult('用户绑定API测试失败', bindingsResult.message || '未知错误', 'error');
                }

            } catch (error) {
                addResult('测试出错', error.message, 'error');
            }
        }

        function testBindingTime(binding, index) {
            const title = `绑定 ${index + 1} 时间测试`;
            let content = `
                <p><strong>群号:</strong> ${binding.groupNumber}</p>
                <p><strong>SKU类型:</strong> ${binding.skuType}</p>
                <p><strong>到期时间显示:</strong> ${binding.expirationDate}</p>
                <p><strong>ISO格式时间:</strong> ${binding.expirationISOString || '未提供'}</p>
            `;

            if (binding.expirationISOString) {
                try {
                    const expirationTime = new Date(binding.expirationISOString);
                    const now = new Date();
                    const diffTime = expirationTime.getTime() - now.getTime();
                    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                    content += `
                        <p><strong>解析后的时间:</strong> ${expirationTime.toLocaleString('zh-CN')}</p>
                        <p><strong>当前时间:</strong> ${now.toLocaleString('zh-CN')}</p>
                        <p><strong>时间差(毫秒):</strong> ${diffTime}</p>
                        <p><strong>剩余天数:</strong> ${diffDays}</p>
                        <p><strong>是否过期:</strong> ${diffDays <= 0 ? '是' : '否'}</p>
                    `;

                    addResult(title, content, diffDays <= 0 ? 'error' : 'success');
                } catch (error) {
                    content += `<p><strong>时间解析错误:</strong> ${error.message}</p>`;
                    addResult(title, content, 'error');
                }
            } else {
                content += `<p><strong>状态:</strong> 缺少ISO格式时间，无法进行精确计算</p>`;
                addResult(title, content, 'error');
            }
        }

        function testTimeCalculation() {
            const testCases = [
                {
                    name: '2025/9/12 01:29:44 格式测试',
                    displayTime: '2025/9/12 01:29:44',
                    isoTime: '2025-09-12T01:29:44'
                },
                {
                    name: '未来时间测试',
                    displayTime: '2025/12/25 12:00:00',
                    isoTime: '2025-12-25T12:00:00'
                },
                {
                    name: '过期时间测试',
                    displayTime: '2024/1/1 00:00:00',
                    isoTime: '2024-01-01T00:00:00'
                }
            ];

            testCases.forEach(testCase => {
                try {
                    const expirationTime = new Date(testCase.isoTime);
                    const now = new Date();
                    const diffTime = expirationTime.getTime() - now.getTime();
                    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                    const content = `
                        <p><strong>显示时间:</strong> ${testCase.displayTime}</p>
                        <p><strong>ISO时间:</strong> ${testCase.isoTime}</p>
                        <p><strong>解析后:</strong> ${expirationTime.toLocaleString('zh-CN')}</p>
                        <p><strong>当前时间:</strong> ${now.toLocaleString('zh-CN')}</p>
                        <p><strong>剩余天数:</strong> ${diffDays}</p>
                        <p><strong>是否过期:</strong> ${diffDays <= 0 ? '是' : '否'}</p>
                    `;

                    addResult(testCase.name, content, diffDays <= 0 ? 'error' : 'success');
                } catch (error) {
                    addResult(testCase.name, `时间解析错误: ${error.message}`, 'error');
                }
            });
        }
    </script>
</body>
</html>
