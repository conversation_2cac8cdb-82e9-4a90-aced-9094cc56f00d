#!/usr/bin/env python3
"""
测试时间解析逻辑
"""

from datetime import datetime

def test_time_parsing():
    """测试时间解析"""
    
    # 测试数据库中的实际时间格式
    test_times = [
        "2025-09-12T15:32:15.177Z",
        "2025-09-12T04:57:00.000Z", 
        "2025-09-12T05:06:00.000Z",
        "2025-09-12T01:29:44.000Z"
    ]
    
    print("=" * 60)
    print("时间解析测试")
    print("=" * 60)
    
    for time_str in test_times:
        print(f"\n原始时间: {time_str}")
        
        try:
            # 当前的解析逻辑
            exp_str = time_str
            if exp_str.endswith('Z'):
                exp_str = exp_str[:-1]
            elif exp_str.endswith('.000Z'):
                exp_str = exp_str[:-5]
            
            if 'T' in exp_str:
                expiry_dt = datetime.fromisoformat(exp_str)
            else:
                expiry_dt = datetime.strptime(exp_str, '%Y-%m-%d %H:%M:%S')
            
            # 格式化显示时间
            expiration_date_display = expiry_dt.strftime('%Y/%m/%d %H:%M:%S')
            # 提供ISO格式用于前端计算
            expiration_iso_string = expiry_dt.isoformat()
            
            print(f"解析后时间: {expiry_dt}")
            print(f"显示格式: {expiration_date_display}")
            print(f"ISO格式: {expiration_iso_string}")
            
            # 计算剩余时间
            now = datetime.now()
            diff = expiry_dt - now
            days_left = diff.days
            hours_left = diff.seconds // 3600
            
            print(f"当前时间: {now}")
            print(f"时间差: {diff}")
            print(f"剩余天数: {days_left}")
            print(f"剩余小时: {hours_left}")
            print(f"是否过期: {'是' if diff.total_seconds() < 0 else '否'}")
            
            # 特别检查目标时间
            if "01:29:44" in time_str:
                print("🎯 这是目标时间！")
                if diff.total_seconds() > 0:
                    print("✅ 修复成功：时间未过期")
                else:
                    print("❌ 仍有问题：时间显示为过期")
                    
        except Exception as e:
            print(f"❌ 解析失败: {e}")

def test_javascript_parsing():
    """测试JavaScript时间解析逻辑"""
    print("\n" + "=" * 60)
    print("JavaScript时间解析测试")
    print("=" * 60)
    
    # 模拟JavaScript的Date解析
    import json
    
    test_iso_times = [
        "2025-09-12T15:32:15.177",
        "2025-09-12T04:57:00.000", 
        "2025-09-12T05:06:00.000",
        "2025-09-12T01:29:44.000"
    ]
    
    for iso_time in test_iso_times:
        print(f"\nISO时间: {iso_time}")
        
        try:
            # Python模拟JavaScript的Date解析
            dt = datetime.fromisoformat(iso_time)
            now = datetime.now()
            diff = dt - now
            
            print(f"解析后: {dt}")
            print(f"当前时间: {now}")
            print(f"剩余时间: {diff}")
            print(f"JavaScript会判断为: {'过期' if diff.total_seconds() < 0 else '未过期'}")
            
        except Exception as e:
            print(f"❌ 解析失败: {e}")

if __name__ == '__main__':
    test_time_parsing()
    test_javascript_parsing()
