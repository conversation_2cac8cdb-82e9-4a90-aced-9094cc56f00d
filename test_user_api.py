#!/usr/bin/env python3
"""
测试用户绑定API的时间修复
"""

import requests
import json
from datetime import datetime

BASE_URL = 'http://localhost:56228'

def test_user_login_and_bindings():
    """测试用户登录和绑定API"""
    print("=" * 50)
    print("测试用户登录和绑定API")
    print("=" * 50)
    
    # 尝试不同的用户
    test_users = [
        {'username': 'gendaye', 'password': '123456'},
        {'username': '123456789', 'password': '123456'},
        {'username': '寄一片星空', 'password': '123456'},
    ]
    
    for user_data in test_users:
        print(f"\n尝试用户: {user_data['username']}")
        
        try:
            # 登录
            login_response = requests.post(f'{BASE_URL}/login', json=user_data)
            print(f"登录响应状态: {login_response.status_code}")
            
            if login_response.status_code == 200:
                login_result = login_response.json()
                if login_result.get('success'):
                    token = login_result.get('token')
                    print(f"登录成功，获得token: {token[:20]}...")
                    
                    # 获取用户绑定
                    headers = {'Authorization': f'Bearer {token}'}
                    bindings_response = requests.get(f'{BASE_URL}/user/bindings', headers=headers)
                    
                    print(f"绑定API响应状态: {bindings_response.status_code}")
                    
                    if bindings_response.status_code == 200:
                        bindings_result = bindings_response.json()
                        if bindings_result.get('success'):
                            bindings = bindings_result.get('data', [])
                            print(f"找到 {len(bindings)} 个绑定记录")
                            
                            for i, binding in enumerate(bindings):
                                print(f"\n  绑定 {i+1}:")
                                print(f"    群号: {binding.get('groupNumber')}")
                                print(f"    SKU类型: {binding.get('skuType')}")
                                print(f"    到期时间显示: {binding.get('expirationDate')}")
                                print(f"    ISO格式时间: {binding.get('expirationISOString')}")
                                
                                # 测试时间计算
                                if binding.get('expirationISOString'):
                                    try:
                                        expiration_time = datetime.fromisoformat(binding['expirationISOString'])
                                        now = datetime.now()
                                        diff = expiration_time - now
                                        days_left = diff.days
                                        
                                        print(f"    解析后时间: {expiration_time}")
                                        print(f"    当前时间: {now}")
                                        print(f"    剩余天数: {days_left}")
                                        print(f"    是否过期: {'是' if days_left < 0 else '否'}")
                                        
                                        # 验证修复效果
                                        if binding.get('expirationDate') == '2025/9/12 01:29:44':
                                            print(f"    🎯 找到目标时间！修复验证:")
                                            print(f"       - 显示时间: {binding.get('expirationDate')}")
                                            print(f"       - 剩余天数: {days_left}")
                                            print(f"       - 修复状态: {'✅ 已修复' if days_left >= 0 else '❌ 仍有问题'}")
                                        
                                    except Exception as e:
                                        print(f"    ❌ 时间解析错误: {e}")
                                else:
                                    print(f"    ❌ 缺少ISO格式时间")
                            
                            # 如果找到了绑定记录，就不需要继续尝试其他用户了
                            if bindings:
                                return
                        else:
                            print(f"API返回失败: {bindings_result.get('message')}")
                    else:
                        print(f"API请求失败: {bindings_response.text}")
                else:
                    print(f"登录失败: {login_result.get('message')}")
            else:
                print(f"登录请求失败: {login_response.text}")
                
        except Exception as e:
            print(f"测试用户 {user_data['username']} 时出错: {e}")
    
    print("\n所有测试用户都无法登录或没有绑定记录")

def test_specific_time_case():
    """测试特定的时间案例"""
    print("\n" + "=" * 50)
    print("测试特定时间案例")
    print("=" * 50)
    
    # 测试问题中提到的时间
    test_time = "2025-09-12T01:29:44"
    
    try:
        expiration_time = datetime.fromisoformat(test_time)
        now = datetime.now()
        diff = expiration_time - now
        days_left = diff.days
        hours_left = diff.seconds // 3600
        
        print(f"测试时间: {test_time}")
        print(f"解析后: {expiration_time}")
        print(f"当前时间: {now}")
        print(f"时间差: {diff}")
        print(f"剩余天数: {days_left}")
        print(f"剩余小时: {hours_left}")
        print(f"是否过期: {'是' if diff.total_seconds() < 0 else '否'}")
        
        # 验证修复逻辑
        if diff.total_seconds() > 0:
            print("✅ 修复成功：时间未过期，应该显示为有效")
        else:
            print("❌ 仍有问题：时间已过期")
            
    except Exception as e:
        print(f"时间解析错误: {e}")

if __name__ == '__main__':
    test_user_login_and_bindings()
    test_specific_time_case()
