#!/usr/bin/env python3
"""
测试用户绑定API的时间处理
"""

import sqlite3
from datetime import datetime
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_time_processing():
    """测试时间处理逻辑"""
    
    print("=" * 60)
    print("用户绑定时间处理测试")
    print("=" * 60)
    
    # 连接数据库
    try:
        conn = sqlite3.connect('database.sqlite')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 查询包含目标时间的绑定
        cursor.execute('''
            SELECT activation_code, group_number, expiration_time, owner_username
            FROM bindings 
            WHERE expiration_time LIKE '%2025-09-12%'
            LIMIT 5
        ''')
        
        bindings = cursor.fetchall()
        
        if not bindings:
            print("❌ 没有找到2025-09-12的绑定记录")
            return
            
        print(f"找到 {len(bindings)} 个相关绑定记录\n")
        
        for row in bindings:
            print(f"激活码: {row['activation_code']}")
            print(f"群号: {row['group_number']}")
            print(f"用户: {row['owner_username']}")
            print(f"原始到期时间: {row['expiration_time']}")
            
            # 应用当前的时间处理逻辑
            expiration_time = row['expiration_time']
            expiration_date_display = '永久有效'
            expiration_iso_string = None
            
            if expiration_time:
                try:
                    # 解析ISO格式的时间
                    exp_str = expiration_time
                    if exp_str.endswith('Z'):
                        exp_str = exp_str[:-1]
                    elif exp_str.endswith('.000Z'):
                        exp_str = exp_str[:-5]
                    
                    if 'T' in exp_str:
                        expiry_dt = datetime.fromisoformat(exp_str)
                    else:
                        expiry_dt = datetime.strptime(exp_str, '%Y-%m-%d %H:%M:%S')
                    
                    # 格式化显示时间
                    expiration_date_display = expiry_dt.strftime('%Y/%m/%d %H:%M:%S')
                    # 提供ISO格式用于前端计算
                    expiration_iso_string = expiry_dt.isoformat()
                    
                    print(f"处理后显示时间: {expiration_date_display}")
                    print(f"ISO格式时间: {expiration_iso_string}")
                    
                    # 计算剩余时间
                    now = datetime.now()
                    diff = expiry_dt - now
                    days_left = diff.days
                    hours_left = diff.seconds // 3600
                    
                    print(f"当前时间: {now}")
                    print(f"剩余天数: {days_left}")
                    print(f"剩余小时: {hours_left}")
                    print(f"是否过期: {'是' if diff.total_seconds() < 0 else '否'}")
                    
                    # 检查是否是目标时间
                    if "01:29:44" in expiration_date_display:
                        print("🎯 这是用户提到的目标时间！")
                        if diff.total_seconds() > 0:
                            print("✅ 时间处理正确：显示未过期")
                        else:
                            print("❌ 时间处理错误：显示已过期")
                    
                except Exception as e:
                    print(f"❌ 时间解析失败: {e}")
                    expiration_date_display = str(expiration_time)
                    expiration_iso_string = expiration_time
            
            print("-" * 40)
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据库操作失败: {e}")

def simulate_frontend_api():
    """模拟前端API调用"""
    
    print("\n" + "=" * 60)
    print("模拟前端API响应")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('database.sqlite')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 模拟用户绑定API的查询逻辑
        cursor.execute('''
            SELECT 
                GROUP_CONCAT(activation_code) as activation_codes,
                group_number,
                sku_id,
                MIN(bind_time) as first_bind_time,
                COUNT(*) as renewal_count,
                MAX(expiration_time) as expiration_time,
                GROUP_CONCAT(DISTINCT owner_username) as all_contributors
            FROM bindings 
            WHERE expiration_time LIKE '%2025-09-12%'
            GROUP BY group_number, sku_id
            LIMIT 3
        ''')
        
        results = cursor.fetchall()
        
        if not results:
            print("❌ 没有找到相关的绑定组")
            return
            
        print(f"找到 {len(results)} 个绑定组\n")
        
        bindings = []
        
        for row in results:
            activation_codes = row['activation_codes'].split(',') if row['activation_codes'] else []
            contributors = row['all_contributors'].split(',') if row['all_contributors'] else []
            
            # 处理到期时间格式
            expiration_time = row['expiration_time']
            expiration_date_display = '永久有效'
            expiration_iso_string = None
            
            if expiration_time:
                try:
                    # 解析ISO格式的时间
                    exp_str = expiration_time
                    if exp_str.endswith('Z'):
                        exp_str = exp_str[:-1]
                    elif exp_str.endswith('.000Z'):
                        exp_str = exp_str[:-5]
                    
                    if 'T' in exp_str:
                        expiry_dt = datetime.fromisoformat(exp_str)
                    else:
                        expiry_dt = datetime.strptime(exp_str, '%Y-%m-%d %H:%M:%S')
                    
                    # 格式化显示时间
                    expiration_date_display = expiry_dt.strftime('%Y/%m/%d %H:%M:%S')
                    # 提供ISO格式用于前端计算
                    expiration_iso_string = expiry_dt.isoformat()
                except Exception as e:
                    print(f'解析到期时间失败: {e}, 原始时间: {expiration_time}')
                    expiration_date_display = str(expiration_time)
                    expiration_iso_string = expiration_time
            
            binding_data = {
                'orderNumber': activation_codes[0] if activation_codes else '',
                'activationCodes': activation_codes,
                'groupNumber': row['group_number'],
                'skuType': 'Unknown',  # 简化处理
                'skuId': row['sku_id'],
                'expirationDate': expiration_date_display,
                'expirationISOString': expiration_iso_string,
                'bindTime': row['first_bind_time'],
                'renewalCount': row['renewal_count'] - 1,
                'contributors': contributors
            }
            
            bindings.append(binding_data)
            
            print(f"绑定组 {len(bindings)}:")
            print(f"  群号: {binding_data['groupNumber']}")
            print(f"  激活码: {binding_data['activationCodes']}")
            print(f"  显示时间: {binding_data['expirationDate']}")
            print(f"  ISO时间: {binding_data['expirationISOString']}")
            
            # 模拟前端的过期判断
            if binding_data['expirationISOString']:
                try:
                    expiry_time = datetime.fromisoformat(binding_data['expirationISOString'])
                    now = datetime.now()
                    is_expired = expiry_time < now
                    
                    print(f"  前端判断: {'已过期' if is_expired else '未过期'}")
                    
                    if "01:29:44" in binding_data['expirationDate']:
                        print("  🎯 这是目标时间！")
                        if not is_expired:
                            print("  ✅ 前端判断正确：未过期")
                        else:
                            print("  ❌ 前端判断错误：显示已过期")
                            
                except Exception as e:
                    print(f"  ❌ 前端时间解析失败: {e}")
            
            print()
        
        conn.close()
        
        print(f"API将返回 {len(bindings)} 个绑定记录")
        
    except Exception as e:
        print(f"❌ 模拟API失败: {e}")

if __name__ == '__main__':
    test_time_processing()
    simulate_frontend_api()
