<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时间显示测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-case { border: 1px solid #ccc; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .expired { background-color: #ffebee; }
        .valid { background-color: #e8f5e8; }
        .warning { background-color: #fff3e0; }
    </style>
</head>
<body>
    <h1>时间显示修复测试</h1>
    
    <div class="test-case">
        <h3>用户案例测试</h3>
        <p><strong>绑定时间:</strong> 2025/8/13 01:29:44</p>
        <p><strong>到期时间:</strong> 2025/9/12 01:29:44</p>
        <p><strong>当前时间:</strong> <span id="current-time"></span></p>
        <p><strong>状态:</strong> <span id="status"></span></p>
        <p><strong>剩余时间:</strong> <span id="remaining"></span></p>
    </div>
    
    <script>
        // 模拟API返回的数据
        const testData = {
            expirationDate: '2025/09/12 01:29:44',
            expirationISOString: '2025-09-12T01:29:44'
        };
        
        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleString('zh-CN');
            
            // 前端过期判断逻辑（与实际代码一致）
            const isExpired = testData.expirationDate && 
                              !String(testData.expirationDate).includes('永久') &&
                              testData.expirationISOString && 
                              new Date(testData.expirationISOString) < new Date();
            
            // 计算剩余天数
            let remainingText = '';
            if (testData.expirationDate && testData.expirationDate !== '永久' && testData.expirationISOString) {
                const expirationTime = new Date(testData.expirationISOString);
                const diffTime = expirationTime.getTime() - now.getTime();
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                
                if (diffDays > 0) {
                    if (diffDays <= 7) {
                        remainingText = `剩余${diffDays}天 (警告)`;
                        document.querySelector('.test-case').className = 'test-case warning';
                    } else if (diffDays <= 30) {
                        remainingText = `剩余${diffDays}天 (正常)`;
                        document.querySelector('.test-case').className = 'test-case valid';
                    } else {
                        remainingText = `剩余${diffDays}天 (充足)`;
                        document.querySelector('.test-case').className = 'test-case valid';
                    }
                } else if (diffDays === 0) {
                    remainingText = '今日到期';
                    document.querySelector('.test-case').className = 'test-case warning';
                } else {
                    remainingText = '已过期';
                    document.querySelector('.test-case').className = 'test-case expired';
                }
            }
            
            document.getElementById('status').textContent = isExpired ? '已过期' : '有效';
            document.getElementById('remaining').textContent = remainingText;
            
            // 验证修复
            const isFixed = !isExpired && remainingText.includes('剩余');
            document.getElementById('status').style.color = isFixed ? 'green' : 'red';
            document.getElementById('remaining').style.color = isFixed ? 'green' : 'red';
        }
        
        // 每秒更新一次
        updateTime();
        setInterval(updateTime, 1000);
    </script>
</body>
</html>